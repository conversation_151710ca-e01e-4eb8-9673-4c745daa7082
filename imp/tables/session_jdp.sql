declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}

select  @country_id as country,
		id,
		date_diff,
		[date],
		flags,
		uid_job,
		job_age,
		job_id_project,
		id_session,
		id_account,
		letter_type,
		id_click,
		id_ref_action,
		[position],
		results_total,
		id_cdp,
		id_pwa_account,
		pwa_date_diff,
		id_click_no_serp
from session_jdp with (nolock)
where date_diff between @dt_begin and @dt_end
