declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}

select @country_id as country,
       id,
       date_diff,
       id_away,
       id_jdp,
       source,
       flags,
       id_session,
       uid_job,
       date_created,
       cpc_for_partner,
       id_traf_source,
       id_session_external_init
from session_external with (nolock)
where date_diff between @dt_begin and @dt_end