declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}

select @country_id as country,
       id,
       date_diff,
       id_session,
       id_recommend,
       page,
       uid_job,
       position,
       job_age,
       score,
       job_destination,
       visual_flags,
       type
from dbo.session_impression_recommend with(nolock)
where date_diff between @dt_begin and @dt_end