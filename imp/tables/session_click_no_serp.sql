declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end}

select  1 as country,
		date_diff,
		id,
		id_session,
		id_api_query,
		click_flags,
		uid_job,
		date,
		position,
		rel_bonus,
		job_age,
		flags,
		id_project,
		id_job,
		id_campaign,
		id_impression,
		click_price,
		id_currency,
		id_session_click,
		job_destination,
		id_recommend,
		id_impression_recommend
from session_click_no_serp with(nolock)
where date_diff between @dt_begin and @dt_end
