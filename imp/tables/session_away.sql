declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}

select  @country_id as country,
		id,
		date_diff,
		id_session,
		uid_job,
		id_project,
		cast(date as datetime) as date,
		flags,
		id_click,
		id_jdp,
		id_click_no_serp,
		base_score,
		score,
		base_rel_bonus,
		rel_bonus,
		id_campaign,
		click_price,
		id_currency,
		id_cdp,
		letter_type
from session_away with(nolock)
where date_diff between @dt_begin and @dt_end
