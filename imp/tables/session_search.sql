declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end}

select  1 as country,
		id,
		date_diff,
		id_session,
		[date],
		results_total,
		results_on_page,
		service_flags,
		search_source,
		elastic_query_time_ms,
		q_flags,
		iif(charindex(nchar(0x00) collate Latin1_General_BIN, q_kw collate Latin1_General_BIN) > 0,
					left(replace(q_kw collate Latin1_General_BIN, nchar(0x00) collate Latin1_General_BIN ,''),
								(charindex(nchar(0x00) collate Latin1_General_BIN, q_kw collate Latin1_General_BIN)) - 1),
										q_kw) as q_kw,
		q_id_region,
		iif(charindex(nchar(0x00) collate Latin1_General_BIN, q_txt_region collate Latin1_General_BIN) > 0,
					left(replace(q_txt_region collate Latin1_General_BIN, nchar(0x00) collate Latin1_General_BIN ,''),
								(charindex(nchar(0x00) collate Latin1_General_BIN, q_txt_region collate Latin1_General_BIN)) - 1),
										q_txt_region) as q_txt_region,
		q_radius_km,
		q_age,
		q_salary_gte,
		kw_hash,
		query_hash,
		q_job_type,
		id_account,
		elastic_search_time_ms,
		id_pwa_account,
		pwa_date_diff,
		cdomain_hash64,
		elastic_took_time_ms
from session_search with (nolock)
where date_diff between @dt_begin and @dt_end