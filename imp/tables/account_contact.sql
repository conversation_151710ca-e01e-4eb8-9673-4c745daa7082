declare @dt_start date = :to_sql_start_date,
        @country_id int = :to_sql_country_id;


select  @country_id as country,
		c.id_account,
		ISNULL(iif(charindex(nchar(0x00) collate Latin1_General_BIN, c.anonymized_contact collate Latin1_General_BIN) > 0,
					left(replace(c.anonymized_contact collate Latin1_General_BIN, nchar(0x00) collate Latin1_General_BIN ,''),
								(charindex(nchar(0x00) collate Latin1_General_BIN, c.anonymized_contact collate Latin1_General_BIN)) - 1),
										c.anonymized_contact), 'None') as contact,
		c.hash64,
		cast(c.verify_date as datetime) as verify_date,
		c.type,
		c.confirm_type,
		c.id_message_verify,
		c.id_message_unsub,
		c.encrypted_contact
from dbo.account a with(nolock)
left join dbo.account_info i with(nolock) on a.id = i.id_account
left join dbo.account_contact c with(nolock) on a.id = c.id_account
where (a.date_add >= @dt_start or i.date_modified >= @dt_start) and c.id_account is not null
;