create table imp.session_jdp_action
(
	country smallint not null,
	id bigint not null,
	date_diff integer not null,
	id_jdp bigint not null,
	date timestamp not null,
	type integer not null,
	uid_job_action bigint,
	flags integer not null,
	respond_type integer,
	click_price numeric(14,5),
	id_currency integer,

	constraint pk_session_jdp_action_date_diff_id
		primary key (country, date_diff, id)

) partition by range (country)
  tablespace data_old;

alter table imp.session_jdp_action owner to postgres;

create index ind_session_jdp_action_jdp
	on imp.session_jdp_action (id_jdp);

create index ind_session_jdp_action_type
	on imp.session_jdp_action (type);


create table imp.session_jdp_action_pt_ua partition of imp.session_jdp_action
    for values from (1) to (2) partition by range (date_diff);

    create table imp.session_jdp_action_pt_UA_2020 partition of imp.session_jdp_action_pt_ua
        for values from (43707) to (44195);

    create table imp.session_jdp_action_pt_UA_2021_1 partition of imp.session_jdp_action_pt_ua
        for values from (44195) to (44285);

    create table imp.session_jdp_action_pt_UA_2021_2 partition of imp.session_jdp_action_pt_ua
        for values from (44285) to (44376);

    create table imp.session_jdp_action_pt_UA_2021_3 partition of imp.session_jdp_action_pt_ua
        for values from (44376) to (44468);

    create table imp.session_jdp_action_pt_UA_2021_4 partition of imp.session_jdp_action_pt_ua
        for values from (44468) to (44560);

create table imp.session_jdp_action_pt_2_10 partition of imp.session_jdp_action
    for values from (2) to (11);

create table imp.session_jdp_action_pt_11_29 partition of imp.session_jdp_action
    for values from (11) to (30);

create table imp.session_jdp_action_pt_30_71 partition of imp.session_jdp_action
    for values from (30) to (72);

create table imp.session_jdp_action_pt_def partition of imp.session_jdp_action
    default;
