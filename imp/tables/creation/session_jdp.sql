create table imp.session_jdp
(
	country smallint not null,
	id bigint not null,
	date_diff integer not null,
	date timestamp not null,
	flags integer not null,
	uid_job bigint not null,
	job_age integer not null,
	job_id_project integer not null,
	id_session bigint not null,
	id_account integer,
	letter_type integer,
	id_click bigint,
	id_ref_action bigint,
	position smallint,
	results_total smallint,
	id_cdp bigint,
	id_pwa_account bigint,
	pwa_date_diff integer,
	id_click_no_serp bigint,

	constraint pk_session_jdp_diff_id
		primary key (country, date_diff, id)

) partition by range (country)
  tablespace data_old;

alter table imp.session_jdp owner to postgres;

create index ind_session_jdp_ids
	on imp.session_jdp (id_session);


create table imp.session_jdp_pt_ua partition of imp.session_jdp
    for values from (1) to (2) partition by range (date_diff);

    create table imp.session_jdp_pt_UA_2020 partition of imp.session_jdp_pt_ua
        for values from (43707) to (44195);

    create table imp.session_jdp_pt_UA_2021_1 partition of imp.session_jdp_pt_ua
        for values from (44195) to (44285);

    create table imp.session_jdp_pt_UA_2021_2 partition of imp.session_jdp_pt_ua
        for values from (44285) to (44376);

    create table imp.session_jdp_pt_UA_2021_3 partition of imp.session_jdp_pt_ua
        for values from (44376) to (44468);

    create table imp.session_jdp_pt_UA_2021_4 partition of imp.session_jdp_pt_ua
        for values from (44468) to (44560);

create table imp.session_jdp_pt_2_10 partition of imp.session_jdp
    for values from (2) to (11);

create table imp.session_jdp_pt_11_29 partition of imp.session_jdp
    for values from (11) to (30);

create table imp.session_jdp_pt_30_71 partition of imp.session_jdp
    for values from (30) to (72);

create table imp.session_jdp_pt_def partition of imp.session_jdp
    default;
