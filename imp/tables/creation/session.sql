create table imp.session
(
	country smallint not null,
	id bigint not null,
	date_diff integer not null,
	start_date timestamp not null,
	cookie_label bigint,
	ip varchar(256),
	flags integer,
	user_agent_hash64 bigint,
	id_traf_source integer,
	ip_cc varchar(2),
	first_visit_date_diff integer,
	is_bot smallint default 0,
	id_current_traf_source integer,

	constraint pk_session_id
		primary key (country, id, date_diff)

) partition by range (country)
  tablespace data_old;

alter table imp.session owner to postgres;

create index ind_session_cl
	on imp.session (cookie_label);

create index ind_session_tr
	on imp.session (id_traf_source);

create index ind_session_cur_tr
	on imp.session (id_current_traf_source);



create table imp.session_pt_ua partition of imp.session
    for values from (1) to (2) partition by range (date_diff);

    create table imp.session_pt_UA_2020 partition of imp.session_pt_ua
        for values from (43707) to (44195);

    create table imp.session_pt_UA_2021_1 partition of imp.session_pt_ua
        for values from (44195) to (44285);

    create table imp.session_pt_UA_2021_2 partition of imp.session_pt_ua
        for values from (44285) to (44376);

    create table imp.session_pt_UA_2021_3 partition of imp.session_pt_ua
        for values from (44376) to (44468);

    create table imp.session_pt_UA_2021_4 partition of imp.session_pt_ua
        for values from (44468) to (44560);

create table imp.session_pt_2_10 partition of imp.session
    for values from (2) to (11);

create table imp.session_pt_11_29 partition of imp.session
    for values from (11) to (30);

create table imp.session_pt_30_71 partition of imp.session
    for values from (30) to (72);

create table imp.session_pt_def partition of imp.session
    default;
