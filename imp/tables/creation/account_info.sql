-- auto-generated definition
create table account_info
(
    country         smallint not null,
    id_account      integer  not null,
    adsense_version integer,
    id_traf_src     integer,
    unsub_date      timestamp,
    id_unsub_type   integer,
    last_visit      integer,
    day_alarm       integer,
    date_modified   timestamp,
    constraint pk_account_info_id
        primary key (country, id_account)
)
    tablespace data_old;

alter table account_info
    owner to postgres;

create index ind_account_info_unsub_date
    on account_info (country, unsub_date);



