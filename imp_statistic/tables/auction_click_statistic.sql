SET NOCOUNT ON;

DECLARE @date_start date = :to_sqlcode_date_or_datediff_start,
        @date_end date = :to_sqlcode_date_or_datediff_end;

SELECT country,
       CAST(date AS datetime) AS date,
       id_project,
       site,
       contractor,
       id_campaign,
       campaign,
       click_count,
       click_price,
       currency,
       rel_bonus,
       job_count,
       session_count,
       ip_count,
       to_jdp_count,
       from_jdp_count,
       total_value,
       id_user,
       test_count,
       organic_count,
       paid_overflow_count,
       flags
FROM auction.click_statistic WITH (NOLOCK)
WHERE [date] BETWEEN @date_start AND @date_end
;
