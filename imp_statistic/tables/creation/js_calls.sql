-- auto-generated definition
create table js_calls
(
    id             integer,
    call           varchar,
    created_on     timestamp not null,
    ea_id          integer,
    in_number      varchar,
    out_number     varchar,
    file           varchar,
    datetime       timestamp,
    billsec        integer,
    voip_status    varchar,
    activity_id    integer,
    call_status_id integer
);

alter table js_calls
    owner to postgres;

create index calls_in_number
    on js_calls (created_on) include (in_number);

grant select on js_calls to readonly;

