select  country,
		jdp_id,
		date_diff,
		max(apply_click) as apply_click,
		case when max(id_apply) is not null then 1 else 0 end as apply_submit,
		max(id_apply) as id_apply
from (
		select  sjd.country,
				sjd.id as jdp_id,
				sjd.date_diff,
				case when ((sja.type = 1 and sja.flags & 2 = 2) or sja.type = 29)
						   		then 1
						   		else 0
					  		end as apply_click,
				(select subsa.id
				 from imp.session_apply subsa
				 where subsa.id_src_jdp_action = sja.id and subsa.date_diff = sja.date_diff and subsa.country = sja.country
				 order by subsa.apply_date desc limit 1) as id_apply
		from imp.session_jdp sjd
		join imp.session_jdp_action sja on sjd.id = sja.id_jdp and sjd.date_diff = sja.date_diff and sjd.country = sja.country
		left join imp.session_apply sa on sa.id_src_jdp_action = sja.id and sa.date_diff = sja.date_diff and sa.country = sja.country
		where sja.country in (1, 12, 11, 10, 9, 8, 7)
	  		and ((sja.type = 1 and sja.flags & 2 = 2) or sja.type = 29)
	  		and sjd.date_diff = ${DT_NOW} - 1 ) q
group by country,
		jdp_id,
		date_diff;
