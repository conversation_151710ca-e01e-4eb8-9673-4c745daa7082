create or replace view jooble_goals.v_dte_strategy_2022_ajpu7_daily
as
SELECT cvp.plan_datediff               AS seeker_cycle_first_datediff,
       cvp.ajpu7_plan,
       sum(vjpp.jcoin_7_days_cnt)      AS jcoin_7_days_cnt,
       count(DISTINCT vjpp.profile_id) AS seeker_cycle_cnt,
       sum(vjpp.jcoin_2_days_cnt)      AS jcoin_2_days_cnt,
       sum(vjpp.jcoin_1_days_cnt)      AS jcoin_1_days_cnt
FROM jooble_goals.v_dte_strategy_2021_client_value_plan_daily cvp
         LEFT JOIN profile.v_jcoin_per_profile vjpp ON vjpp.country_id = 1 AND vjpp.profile_blue_collar_type_id = 3 AND
                                                       cvp.plan_datediff = vjpp.seeker_cycle_first_datediff
where cvp.plan_datediff between 44407 /*01/08/2021*/ and 44649 /*31/03/2022*/
GROUP BY cvp.plan_datediff, cvp.ajpu7_plan;



alter table jooble_goals.v_dte_strategy_2022_ajpu7_daily owner to dap;

grant select on jooble_goals.v_dte_strategy_2022_ajpu7_daily to readonly;
