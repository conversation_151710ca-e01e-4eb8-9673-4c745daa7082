create or replace view jooble_goals.v_dte_strategy_2021_money_value_daily
            (date, call_revenue_plan, apply_revenue_plan, profile_base_revenue_plan, digital_recruiter_revenue_plan,
             unutilized_revenue_plan, call_revenue, apply_revenue, profile_base_revenue, digital_recruiter_revenue,
             unutilized_revenue, call_cost, paid_employer_cnt, retained_employer_cnt,
             retained_with_payment_employer_cnt, packet_payment_without_vat_uah,
             retained_employer_packet_payment_without_vat_uah, autoretained_employer_packet_payment_without_vat_uah,
             cashflow_uah_plan, cashflow_uah)
as
SELECT mv_plan.date,
       mv_plan.call_revenue_plan,
       mv_plan.apply_revenue_plan,
       mv_plan.profile_base_revenue_plan,
       mv_plan.digital_recruiter_revenue_plan,
       mv_plan.unutilized_revenue_plan,
       sum(cv_fact.call_revenue)                                                  AS call_revenue,
       sum(cv_fact.apply_revenue)                                                 AS apply_revenue,
       sum(cv_fact.profile_base_revenue)                                          AS profile_base_revenue,
       sum(cv_fact.digital_recruiter_revenue)                                     AS digital_recruiter_revenue,
       sum(cv_fact.revenue_pnl_uah) - sum(cv_fact.call_revenue) - sum(cv_fact.apply_revenue) -
       sum(cv_fact.profile_base_revenue) - sum(cv_fact.digital_recruiter_revenue) AS unutilized_revenue,
       cr.costs_total::numeric                                                    as call_cost,
       count(distinct vpper.employer_id)                                          AS paid_employer_cnt,
       count(distinct CASE
                          WHEN vpper.next_packet_start_datetime <=
                               (vpper.packet_start_datetime + '1 mon 10 days'::interval) then vpper.employer_id
           END)                                                                   AS retained_employer_cnt,
       count(distinct CASE
                          WHEN vpper.next_packet_start_datetime <=
                               (vpper.packet_start_datetime + '1 mon 10 days'::interval)
                              and vpper.next_packet_start_datetime = vpper.next_payment_start_datetime
                              THEN vpper.employer_id
           END)                                                                   AS retained_with_payment_employer_cnt,
       sum(vpper.packet_payment_without_vat_uah)                                  as packet_payment_without_vat_uah,
       sum(vpper.packet_payment_without_vat_uah * CASE
                                                      WHEN vpper.next_packet_start_datetime <=
                                                           (vpper.packet_start_datetime + '1 mon 10 days'::interval)
                                                          and
                                                           vpper.next_packet_start_datetime = vpper.next_payment_start_datetime
                                                          THEN 1
                                                      ELSE 0
           END)                                                                   as retained_employer_packet_payment_without_vat_uah,
       sum(CASE
               WHEN vpper.next_packet_start_datetime <= (vpper.packet_start_datetime + '1 mon 10 days'::interval)
                   and (vpper.next_packet_start_datetime <> vpper.next_payment_start_datetime or
                        vpper.next_payment_start_datetime is null)
                   THEN vpper.packet_payment_without_vat_uah
               ELSE 0
           END)                                                                   as autoretained_employer_packet_payment_without_vat_uah,
       mv_plan.cashflow_plan                                                      as cashflow_uah_plan,
       sum(cv_fact.revenue_cashflow_uah)                                          as cashflow_uah
FROM jooble_goals.v_dte_strategy_2021_money_value_plan_daily mv_plan
         LEFT JOIN employer.jcoin_model_daily cv_fact
                   ON cv_fact.database_source_id = 1
                       AND cv_fact.country_code = 'ua'
                       AND mv_plan.date = fn_get_timestamp_from_date_diff(cv_fact.action_datediff)
                       AND fn_get_timestamp_from_date_diff(cv_fact.action_datediff) < CURRENT_DATE
         LEFT JOIN employer.cashflow_ringostat cr
                   ON cr.change_date = mv_plan.date
         LEFT JOIN employer.v_paid_packet_retention vpper
                   ON vpper.database_source_id = 1
                       AND vpper.country_code = 'ua'
                       AND vpper.subscription_id = cv_fact.subscription_id
                       AND vpper.packet_rank = cv_fact.packet_rank
                       AND fn_get_date_diff(vpper.packet_start_datetime) = cv_fact.action_datediff

WHERE mv_plan.date >= '2021-08-01'::date
GROUP BY mv_plan.date, mv_plan.call_revenue_plan, mv_plan.apply_revenue_plan, mv_plan.profile_base_revenue_plan,
         mv_plan.digital_recruiter_revenue_plan, mv_plan.unutilized_revenue_plan, cr.costs_total::numeric,
         mv_plan.cashflow_plan;

alter table jooble_goals.v_dte_strategy_2021_money_value_daily
    owner to dap;

grant select on jooble_goals.v_dte_strategy_2021_money_value_daily to readonly;

grant select on jooble_goals.v_dte_strategy_2021_money_value_daily to writeonly_product;

grant select on jooble_goals.v_dte_strategy_2021_money_value_daily to readonly_ds;
