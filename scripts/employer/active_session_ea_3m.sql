with sess as (
select distinct cast('1900-01-01'::timestamp + max(date_diff) over (partition by id_account)  * interval '1 days' as date) as date_last_session
        , id_account
        , count(id) over (partition by id_account) as cnt_sess
        , sources
from employer.employer_account_session
WHERE using_secret_key=FALSE -- не джубловые ип
    and date_started >= date_trunc('day', NOW() - interval '3 month')
),
ea as (
select id as id_employer
    , upper(country_code) as country_code
    , id_cdp
    , sources
from employer.employer
    
    ),
rel_acc as (
    select id_employer
        , id_account
        , sources
    from employer.employer_account_account
),
     cdp as (
         select company_name
                , industry
                , id
                , sources
         from employer.employer_cdp
     )
select ea.id_employer
    , ea.country_code
    , rel_acc.id_account
    , coalesce(cdp.industry, '-99') as industry
    , coalesce(cdp.company_name, '-99') as company_name
    , case
        when sess.id_account is null then 0
        else 1 end as exist_session
    , coalesce(sess.date_last_session, '2999-12-31') as date_last_session
    , coalesce(sess.cnt_sess,-1) as cnt_session
    , cast(NOW() as date) as time_key
    , ea.sources
from ea
    inner join rel_acc on ea.id_employer=rel_acc.id_employer and ea.sources=rel_acc.sources
    left join sess on rel_acc.id_account=sess.id_account and rel_acc.sources=sess.sources
    left join  cdp on ea.id_cdp=cdp.id and ea.sources=cdp.sources
;