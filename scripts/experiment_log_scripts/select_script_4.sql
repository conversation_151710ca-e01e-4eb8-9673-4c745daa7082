-- Запрос на выборку данных №4:

explain select country_id, report_datediff, profile_id, registration_source_id, profile_blue_collar_type_id, is_created_last_7_days,
       is_session_after_creation_last_7_days, is_activated_last_7_days, is_session_after_activation_last_7_days,
       is_returned_apply_last_7_days
from profile.select_profile_liveliness_level_daily_detailed(${DT_NOW});

/*
date_diff - 44231 "-1";
rows - 244 594;
time - 1 m 41 s 952 ms (execution: 1 m 40 s 177 ms, fetching: 1 s 775 ms);
cost - 0.25..10.25;
*/

explain select vpca.country_id,
           ${DT_NOW} - 1  as report_datediff,
           vpca.profile_id,
           coalesce((select rs.registration_source_id
               from profile.profile_registration_source rs
                where rs.country_id = vpca.country_id
                  and rs.profile_id = vpca.profile_id
               ),0) as registration_source_id,
            CASE
               WHEN pbs.blue_score >= 0::double precision AND pbs.blue_score <= 0.34::double precision THEN 1
               WHEN pbs.blue_score >= 0.35::double precision AND pbs.blue_score <= 0.64::double precision THEN 2
               WHEN pbs.blue_score >= 0.65::double precision AND pbs.blue_score <= 1::double precision THEN 3
               ELSE 0
            END AS profile_blue_collar_type_id,
           max(case when vpca.submission_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 then 1 else 0 end) as is_created_last_7_days,
           max(case when s.date_diff > vpca.submission_datediff then 1 else 0 end) as is_session_after_creation_last_7_days,
           max(case when vpca.activation_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 then 1 else 0 end) as is_activated_last_7_days,
           max(case when s.date_diff > vpca.activation_datediff then 1 else 0 end) as is_session_after_activation_last_7_days,
           max(case when vpca.last_active_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 and last_active_datediff > activation_datediff then 1 else 0 end) as is_returned_apply_last_7_days
    from profile.v_profile_creation_activation vpca
    left join imp.profile_cookie_labels pcl
         on pcl.country = vpca.country_id
        and pcl.id_profile = vpca.profile_id
    left join imp.session s
          on s.cookie_label = pcl.cookie_labels
        and s.country = pcl.country
        and s.date_diff between ${DT_NOW} - 7 and ${DT_NOW} - 1
        and s.date_diff > vpca.submission_datediff
    left join profile.v_profile_blue_score pbs on pbs.country_id = vpca.country_id and pbs.profile_id = vpca.profile_id
    where creation_datediff between 44034 and ${DT_NOW} - 1
    group by vpca.profile_id, vpca.country_id,
             CASE
               WHEN pbs.blue_score >= 0::double precision AND pbs.blue_score <= 0.34::double precision THEN 1
               WHEN pbs.blue_score >= 0.35::double precision AND pbs.blue_score <= 0.64::double precision THEN 2
               WHEN pbs.blue_score >= 0.65::double precision AND pbs.blue_score <= 1::double precision THEN 3
               ELSE 0
            END;

/*
date_diff - 44231 "-1";
rows - 244 594;
time - 32 s 824 ms (execution: 31 s 233 ms, fetching: 1 s 591 ms);
cost - 1418973.64..4338206.19;
*/
