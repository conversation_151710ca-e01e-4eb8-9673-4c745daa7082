declare @dt_begin int = datediff(day,0,dbo.ex_getdate()-7),
        @dt_end int = datediff(day,0,dbo.ex_getdate()-1)
    
    
select substring(db_name(), 5, 2)                                as country,
       cast(dateadd(DAY, s.date_diff, '1900-01-01') as datetime) as dt,
       sign(sc.flags & 8) /*1 - mobile*/                         as device,
       sign(s.flags & 2)                                         as is_returned,
       s.id_traf_source,
       sc.id_project,
       count(distinct sc.id)                                     as click_aways_serp,
       sum(sc.click_price * ic.value_to_eur)                     as sum_click_price_eur
from session_search ss with(nolock)
join session_click sc with (nolock) on sc.date_diff = ss.date_diff and sc.id_search = ss.id and sc.job_destination = 1 /*away_serp*/
join session s with (nolock) on s.id = ss.id_session and s.date_diff = ss.date_diff
left join info_currency ic with (nolock) on ic.id = sc.id_currency
where ss.date_diff between @dt_begin and @dt_end and s.flags & 1 = 0 and s.flags & 4 = 0
group by sc.id_project,
         cast (dateadd(DAY, s.date_diff, '1900-01-01') as datetime),
         sign(sc.flags & 8), sign(s.flags & 2),
         s.id_traf_source
