declare @dt_begin int =  datediff(day,0,dbo.ex_getdate()-31),
		@dt_end int   =  datediff(day,0,dbo.ex_getdate()-1),
		@dt_input date = DATEADD(month, DATEDIFF(month, 0, dbo.ex_getdate()+3), 0)
    
    
select cast(@dt_input as datetime)                                                     as dt_month,
       substring(db_name(), 5, 2)                                                      as country,
       sc.id_project                                                                   as project_id, /*ip.name as name_project,*/
       cast(min(ss.date) as datetime)                                                  as min_date,
       cast(max(ss.date) as datetime)                                                  as max_date, /*iif(ip.jdp_flags & 1 = 0 and ip.jdp_flags & 2 = 0, 1,0) as is_ugly_jobs, 	   count (distinct sc.uid_job) as cnt_jobs, 	   count (distinct ss.id_session) as cnt_sessions,*/
       count(distinct case when sc.job_destination = 1 then sc.id end)                 as click_aways_serp,
       count(distinct case
                          when (sc.job_destination != 1 or sc.job_destination is null) and
                               sjd.flags & 8192 = 0 /*not sample*/ then sc.id end)     as click_serp_no_away,
       avg(case when sc.job_destination = 1 then sc.click_price * ic.value_to_eur end) as avg_click_price_eur
from session_search ss with(nolock)
join session_click sc with (nolock) on sc.date_diff = ss.date_diff and sc.id_search = ss.id
join session s with (nolock) on s.id = ss.id_session and s.date_diff = ss.date_diff
join info_project ip with (nolock) on ip.id = sc.id_project
left join info_currency ic with (nolock) on ic.id = sc.id_currency
left join session_jdp sjd with (nolock) on sjd.id_click = sc.id and sjd.date_diff = sc.date_diff and sjd.uid_job = sc.uid_job
where ss.date_diff between @dt_begin and @dt_end and s.flags & 1 = 0 and sc.id_project != -1 /*не EA проекты*/
group by sc.id_project,
		 iif(ip.jdp_flags & 1 = 0 and ip.jdp_flags & 2 = 0, 1, 0)
having count (distinct case when sc.job_destination = 1 then sc.id end) > 10000 /*more than 10000 clicks per month*/
   and count (distinct case when (sc.job_destination != 1
    or sc.job_destination is null)
   and sjd.flags& 8192 = 0 /*not sample*/ then sc.id end) < 300 /*less than 300 views per month*/
   and avg (case when sc.job_destination = 1 then sc.click_price*ic.value_to_eur end) <= 0.167 /*click price less than 0.167 euro*/
   and iif(ip.jdp_flags & 1 = 0
   and ip.jdp_flags & 2 = 0, 1, 0) = 0 /*not ugly jobs*/
