declare @dt_begin int = datediff(day,0,dbo.ex_getdate()-7),
		@dt_end int = datediff(day,0,dbo.ex_getdate()-1)

select substring(db_name(), 5, 2)                                                                          as country,
       cast(dateadd(DAY, s.date_diff, '1900-01-01') as datetime)                                           as dt,
       sjd.flags & 1 /*1 - mobile*/                                                                        as device,
       sign(s.flags & 2)                                                                                   as is_returned,
       case when sjd.job_id_project = -1 then 1 else 0 end                                                 as is_ea,
       sjd.job_id_project                                                                                  as id_project,
       sign(sjd.flags & 256/*is premium*/)                                                                 as is_premium,
       sign(sjd.flags & 1024/*has simplified apply*/)                                                      as no_cv_vacancy,
       case
           when sjd.flags & 4 = 4 /* easy apply form */ then 1 /* easy apply form */
           when sjd.flags & 8 = 8 /* mapped apply form */ then 2 /* mapped apply form */
           else 0 end                                                                                      as jdp_response_type_id,
       s.id_traf_source,
       count(distinct sjd.id)                                                                              as views,
       count(distinct case
                          when (sja.type in (1/*respond*/) and sja.flags & 2 = 2 /*easy_apply*/) or sja.type in (29/*apply_no_cv_click*/)
                              then sjd.id end)                                                             as apply_clicks,
       count(distinct case
                          when sja.type in (13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                            34/*view_multiply_employer_phone*/ )
                              then sjd.id end)                                                             as call_clicks,
       count(distinct case
                          when sja.type in (1/*respond*/) and sja.flags & 2 = 0 /*away*/
                              then sjd.id end)                                                             as away_clicks,
       count(distinct case
                          when sja.type in
                               (30/*apply_no_cv_submit*/, 2/*easyapply_form_submit*/, 15/*mappedapply_form_submit*/) and
                               sja.flags & 1 = 1 /*successful submit*/ then sjd.id end)                    as applies,
       count(distinct case
                          when sja.type in
                               (1/*respond*/, 29/*apply_no_cv_click*/, 13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                34/*view_multiply_employer_phone*/ )
                              then sjd.id end)                                                             as intention_to_contact
from session s with(nolock)
join session_jdp sjd with (nolock) on sjd.date_diff = s.date_diff and sjd.id_session = s.id
join session_click sc with (nolock) on sc.id = sjd.id_click and sc.date_diff = sjd.date_diff and sc.uid_job = sjd.uid_job
left join session_jdp_action sja with (nolock) on sjd.date_diff = sja.date_diff and sjd.id = sja.id_jdp and sja.type in (1/*respond*/, 29/*apply_no_cv_click*/, 30/*apply_no_cv_submit*/, 2/*easyapply_form_submit*/, 15/*mappedapply_form_submit*/, 13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/, 34/*view_multiply_employer_phone*/ )
where s.date_diff between @dt_begin and @dt_end
  and s.flags & 1 = 0 /* without bots */
  and s.flags & 4 = 0 /* without responds from pred-prod server */
  and sjd.flags & 8192 = 8192 /* Redirected from away only */
group by sjd.flags & 1,
		 sign(s.flags & 2),
		 cast (dateadd(DAY, s.date_diff, '1900-01-01') as datetime),
		 sign(sjd.flags & 1024),
		 sign(sjd.flags & 256),
		 case when sjd.job_id_project = -1 then 1 else 0 end,
		 s.id_traf_source,
		 sjd.job_id_project,
		 case when sjd.flags & 4 = 4 /* easy apply form */ then 1 /* easy apply form */ when sjd.flags & 8 = 8 /* mapped apply form */ then 2 /* mapped apply form */ else 0 end
