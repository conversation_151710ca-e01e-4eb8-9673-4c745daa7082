create or replace procedure email.insert_abandoned_profile_viber_messages()
	language plpgsql
as $$
begin

        truncate table email.abandoned_apply_viber_messages;

        insert into email.abandoned_profile_viber_messages(country_id, sent_message_datediff, sent_message_date, account_id,
                                                           status, profile_id, has_link_visit, has_submit_profile)
        with abandoned_profile_messages as (
        select country,
               date_diff,
               cast(date as date) as date_sent_message,
               id_account,
               status
        from imp.viber_message_sent
        where country = 1 and message_type = 2)

        select distinct apm.*,
               pa.profile_id,
               case when sp.id_profile is not null then 1 else 0 end as has_link_visit,
               case when spa.id_session is not null then 1 else 0 end as has_submit_profile
        from abandoned_profile_messages apm
        inner join profile.v_profile_account pa on apm.country=pa.country_id and apm.id_account=pa.account_id
        left join imp.session_profile sp on pa.country_id = sp.country and pa.profile_id = sp.id_profile and sp.source = 9
        left join imp.session_profile_action spa on sp.country = spa.country and sp.date_diff = spa.date_diff and sp.id_session = spa.id_session and spa.type = 4 and spa.screen = 2006;


        truncate table email.agg_abandoned_profile_viber_messages;

        insert into email.agg_abandoned_profile_viber_messages(sent_message_datediff, sent_message_date, message_sent_daily_cnt,
                                                               message_delivered_daily_cnt, has_link_visit_cnt, has_submit_profile_cnt)
        select distinct sent_message_datediff,
               sent_message_date,
               count(profile_id) over (partition by sent_message_datediff, country_id) as message_sent_daily_cnt,
               sum(case when status in (3,4,5) then 1 else 0 end) over (partition by sent_message_datediff, country_id) as message_delivered_daily_cnt,
               sum(has_link_visit) over (partition by sent_message_datediff, country_id) as has_link_visit_cnt,
               sum(has_submit_profile) over (partition by sent_message_datediff, country_id) as has_submit_profile_cnt
        from email.abandoned_profile_viber_messages;


    end;

$$;

alter procedure email.insert_abandoned_profile_viber_messages() owner to rlu;

call email.insert_abandoned_profile_viber_messages();
