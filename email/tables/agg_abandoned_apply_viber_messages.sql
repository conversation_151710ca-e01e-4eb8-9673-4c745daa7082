 with dist as (
    select country_id,
            sent_message_datediff,
            sent_message_date,
            account_id,
            status,
            job_uid,
            max(has_link_on_jdp) as has_link_on_jdp,
            max(has_apply_on_the_same_jdp) as has_apply_on_the_same_jdp,
            max(has_call_on_the_same_jdp) as has_call_on_the_same_jdp
    from email.abandoned_apply_viber_messages
    group by country_id, sent_message_datediff, sent_message_date, account_id,status, job_uid)

select distinct sent_message_datediff
    , sent_message_date
    , count(account_id) over (partition by sent_message_datediff, country_id) as message_sent_daily_cnt
    , sum(case when status in (3,4,5) then 1 else 0 end) over (partition by sent_message_datediff, country_id)
        as message_delivered_daily_cnt
    , sum(has_link_on_jdp) over (partition by sent_message_datediff, country_id) as has_link_on_jdp_cnt
    , sum(has_apply_on_the_same_jdp) over (partition by sent_message_datediff, country_id) as has_apply_on_the_same_jdp_cnt
    , sum(has_call_on_the_same_jdp) over (partition by sent_message_datediff, country_id) as has_call_on_the_same_jdp_cnt
from dist;
