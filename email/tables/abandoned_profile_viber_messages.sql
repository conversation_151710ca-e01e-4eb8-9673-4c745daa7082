with abandoned_profile_messages as (
        select country,
               date_diff,
               cast(date as date) as date_sent_message,
               id_account,
               status
        from imp.viber_message_sent
        where country = 1
            and message_type = 2
        )
        select distinct apm.*,
               pa.profile_id,
               case when sp.id_profile is not null then 1 else 0 end as has_link_visit,
               case when spa.id_session is not null then 1 else 0 end as has_submit_profile
        from abandoned_profile_messages apm
        inner join profile.v_profile_account pa on apm.country=pa.country_id and apm.id_account=pa.account_id
        left join imp.session_profile sp on pa.country_id = sp.country
                    and pa.profile_id = sp.id_profile
                    and sp.source=9
        left join imp.session_profile_action spa on sp.country = spa.country and sp.date_diff = spa.date_diff and sp.id_session = spa.id_session and spa.type = 4 and spa.screen = 2006;
