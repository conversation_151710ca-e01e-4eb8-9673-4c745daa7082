select es.date_diff as sent_datediff,
       es.country as country_id,
       es.id_message as message_id,
       sum(coalesce(m_samples.itc_per_view, m_all_away.itc_per_view)) as serp_ith
from imp.email_sent es
inner join imp.session_alertview_message sam on sam.id_message = es.id_message and sam.country = es.country
inner join imp.session_click sc on sc.job_destination = 1 and sc.id_session = sam.id_session and sc.country = sam.country
inner join dimension.countries c on c.id = sc.country
left join company.m_itc_jdp_samples_for_ab as m_samples on m_samples.country = c.alpha_2 and m_samples.id_project = sc.id_project
left join company.m_itc_jdp_away_projects_for_ab as m_all_away on m_all_away.country = c.alpha_2
where es.country <= 11 and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by
        es.date_diff,
        es.country,
        es.id_message
union all
select es.date_diff as sent_datediff,
       es.country as country_id,
       es.id_message as message_id,
       sum(coalesce(m_samples.itc_per_view, m_all_away.itc_per_view)) as serp_ith
from imp.email_sent es
inner join imp.session_click_message scm on scm.id_message = es.id_message and scm.country = es.country
inner join imp.session_click sc on sc.job_destination = 1 and  sc.id_session = scm.id_session and sc.country = scm.country
inner join dimension.countries c on c.id = sc.country
left join company.m_itc_jdp_samples_for_ab as m_samples on m_samples.country = c.alpha_2 and m_samples.id_project = sc.id_project
left join company.m_itc_jdp_away_projects_for_ab as m_all_away on m_all_away.country = c.alpha_2
where es.letter_type in (12,15) and es.country <= 11 and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by
        es.date_diff,
        es.country,
        es.id_message;
