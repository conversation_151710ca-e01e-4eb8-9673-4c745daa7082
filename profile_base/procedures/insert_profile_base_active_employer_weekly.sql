create procedure profile_base.insert_profile_base_active_employer_weekly(_dd_date date)
	language plpgsql
as $$
begin
            -- за останній тиждень
            create temp table active_employer as
            select week_start_datediff,
                   employer_id,
                   employer_action_cnt,
                   employer_account_id,
                   email,
                   employer_account_action_cnt
            from (
                    select week_start_datediff,
                           employer_id,
                           sum(employer_account_action_cnt) over (partition by week_start_datediff, employer_id) as employer_action_cnt,
                           employer_account_id,
                           email,
                           employer_account_action_cnt
                    from
                        (   select fn_get_date_diff(_dd_date) - 7  as week_start_datediff,
                                   id_employer as employer_id,
                                   employer_account_id,
                                   email,
                                   sum(action_cnt) as employer_account_action_cnt
                             from employer.employer_intention_to_contact itc
                                  left join imp_employer.employer_account ea
                                  on itc.employer_account_id = ea.id_account
                                     and ea.sources = 1

                                 left join imp_employer.account_service_account asa
                                 on asa.sources = ea.sources
                                    and asa.id = ea.id_account
                            where itc.country_id = 1
                              and itc.feature_id = 3    /* враховуємо тільки дії на Пошуку кандидатів*/
                              and itc.action_datediff between fn_get_date_diff(_dd_date) - 7  and fn_get_date_diff(_dd_date) - 1   /* за останній тиждень*/
                            group by week_start_datediff, id_employer, employer_account_id, email
                        ) itc
                         left join imp_employer.employer e
                         on e.sources = 1
                            and e.id = itc.employer_id
                    where e.country_code = 'ua'
                ) a
            where employer_action_cnt > 170;


            -- видалення тих записів, що з'явилися в результаті помилки
            with records_with_deleted_account as (
                select week_start_datediff,
                       employer_account_id,
                       employer_id
                from active_employer ae
                     left join imp_employer.employer_account ea
                     on ea.sources = 1
                        and ea.id_account = ae.employer_account_id
                        and ea.id_employer = ae.employer_id
                where ae.week_start_datediff >= fn_get_date_diff(ea.date_deleted)
            )
            delete
            from active_employer ae
                 using records_with_deleted_account
            where ae.week_start_datediff = records_with_deleted_account.week_start_datediff
              and ae.employer_account_id = records_with_deleted_account.employer_account_id
              and ae.employer_id = records_with_deleted_account.employer_id;


            insert into profile_base.active_employer_weekly
            select week_start_datediff,
                   employer_id,
                   null::text as registered_in_foreign_country,
                   null::smallint as employer_moderation_status,
                   null::numeric as active_vacancy_cnt,
                   null::numeric as employer_action_percent,
                   employer_action_cnt,
                   employer_account_id,
                   email as employer_account_email,
                   employer_account_action_cnt::numeric / employer_action_cnt as employer_account_action_percent,
                   null::numeric as is_email_verified,
                   /* Блок "Чи отримуємо гроші від цих роботодавців" */
                   null::numeric as employer_is_business_account,
                   null::numeric as has_payment,
                   /* Блок "Активність роботодавця" */
                   null::float       as profile_show_to_itc_conversion,
                   null::float       as message_itc_percent,
                   null::float       as phone_itc_percent,
                   null::float       as email_itc_percent,
                   null::float       as download_cv_itc_percent,
                   null::integer     as job_apply_itc_cnt,
                   null::integer     as add_to_favorite_cnt,
                   null::float       as apply_seen_percent,
                   /* Блок "Перевірка на масову розсилку повідомлень" */
                   null::smallint    as is_message_spammer,
                   /* Блок "Перевірка на сканування бази кандидатів" */
                   null::float       as empty_search_query_percent,
                   null::text        as search_keyword,
                   null::text        as vacancy,
                   null::float       as similarity_vacancy_and_search,
                   1 as database_source_id
            from active_employer;


            /* Заповнюємо порожні колонки */

            -- к-сть активних вакансій (максимальна досягнута за тиждень),
            update profile_base.active_employer_weekly aew
            set active_vacancy_cnt = a.active_vacancy_cnt
            from (
                select week_start_datediff,
                       employer_id,
                       max(j.active) as active_vacancy_cnt
                from profile_base.active_employer_weekly ae
                     left join imp_employer.employer_jobs j
                     on j.sources = 1
                        and j.id_employer = ae.employer_id
                        and j.date_diff between ae.week_start_datediff and ae.week_start_datediff+6
                where week_start_datediff = fn_get_date_diff(_dd_date) - 7
                group by week_start_datediff, employer_id
                ) as a
            where aew.employer_id = a.employer_id
              and aew.week_start_datediff = a.week_start_datediff;



            -- чи було підтверджено  email
            update profile_base.active_employer_weekly aew
            set is_email_verified = a.is_email_verified
            from (
                select employer_account_id,
                       (case when is_verified = true then 1 else 0 end) as is_email_verified
                from profile_base.active_employer_weekly ae
                     left join imp_employer.account_service_account a
                     on a.sources = 1
                        and ae.employer_account_id = a.id
                ) as a
            where aew.employer_account_id = a.employer_account_id;



            -- статус модерації
            update profile_base.active_employer_weekly aew
            set employer_moderation_status = a.moderation_status
            from (
                select employer_id, moderation_status
                from profile_base.active_employer_weekly ae
                     left join imp_employer.employer e
                     on e.sources = 1
                        and ae.employer_id = e.id
                ) as a
            where aew.employer_id = a.employer_id;


            -- відносний показник активності роботодавця - доля всіх ItC за період
            with a as (
             select ae.week_start_datediff,
                    employer_id,
                    coalesce(ae.employer_action_cnt::numeric / itc_agg.total_action_cnt, 0) as action_percent
                from profile_base.active_employer_weekly ae
                         left join (
                                        select fn_get_date_diff(_dd_date) - 7  as week_start_datediff,
                                               sum(action_cnt) as total_action_cnt
                                        from employer.employer_intention_to_contact itc
                                        where itc.country_id = 1
                                          and itc.action_datediff between fn_get_date_diff(_dd_date) - 7  and fn_get_date_diff(_dd_date) - 1
                                          and feature_id = 3
                                          and action_type_id in (1, 2, 3, 4, 6)
                                     ) as itc_agg
                         on ae.week_start_datediff = itc_agg.week_start_datediff
            )
            update profile_base.active_employer_weekly aew
               set employer_action_percent = a.action_percent
              from a
             where aew.employer_id = a.employer_id
               and aew.week_start_datediff = a.week_start_datediff;


            --  хоч раз поповнив баланс
            update profile_base.active_employer_weekly aew
               set has_payment = a.has_payment
            from (select ae.employer_id,
                         (case when bh.flags & 2 = 2 then 1 else 0 end) as has_payment
                  from profile_base.active_employer_weekly ae
                           left join imp_employer.employer_balance bh
                           on bh.sources = 1
                              and ae.employer_id = bh.id_employer
                 ) as a
            where aew.employer_id = a.employer_id;


            -- чи є бізнес-акаунтом
            with a as (
                select employer_id, type as is_business_account
                from profile_base.active_employer_weekly se
                     inner join imp_employer.employer e
                     on e.sources = 1
                        and e.id = se.employer_id
            )
            update profile_base.active_employer_weekly se
               set employer_is_business_account = a.is_business_account
              from a
              where se.employer_id = a.employer_id;


            -- інші країни, в яких наші роботодавці мають зареєстровані кабінети. Ідентифікуємо по назві
            with foreign_cabinets as (
                select employer_id,
                       string_agg(distinct country_code, ', ') as country_code_array
                from (
                         select employer_id, company_name
                         from profile_base.active_employer_weekly aew
                                  left join imp_employer.employer e
                                            on e.sources = 1
                                                and aew.employer_id = e.id
                                  left join imp_employer.employer_cdp ecdp
                                            on e.sources = ecdp.sources
                                                and e.id_cdp = ecdp.id
                     ) a
                     left join imp_employer.employer_cdp ecdp on ecdp.sources = 1 and a.company_name = ecdp.company_name
                     left join imp_employer.employer e on e.sources = ecdp.sources and e.id_cdp = ecdp.id
                where country_code <> 'ua'
                group by employer_id

            )
            update profile_base.active_employer_weekly aew
              set registered_in_foreign_country = foreign_cabinets.country_code_array
              from foreign_cabinets
            where aew.employer_id = foreign_cabinets.employer_id;



            ---------------

            -- 1. Конверсія з перегляду профіля в ItC
            -- 2. к-сть додавань у обрані
            with a as (
                        select week_start_datediff,
                               employer_account_id,
                               round(count(distinct case when psa.action in (1, 2, 4, 6) then psa.id_profile end)
                                         / count(distinct psa.id_profile)::numeric, 4) as profile_show_to_itc_conv,
                               count(distinct case when psa.action = 7 then psa.id_profile end)                          as add_to_favorite_cnt
                     from profile_base.active_employer_weekly aew
                              left join imp_employer.profile_search_action psa
                                        on psa.sources = 1
                                        and aew.employer_account_id = psa.id_account
                                            and psa.date_diff between week_start_datediff and week_start_datediff + 6
                    where week_start_datediff = fn_get_date_diff(_dd_date) - 7
                    group by week_start_datediff, employer_account_id
            )
            update profile_base.active_employer_weekly aesd
            set profile_show_to_itc_conversion = a.profile_show_to_itc_conv,
                add_to_favorite_cnt = a.add_to_favorite_cnt
            from a
            where aesd.week_start_datediff = a.week_start_datediff
              and aesd.employer_account_id = a.employer_account_id;


            -- структура ItC: 5 метрик одразу
            with a as (
                select week_start_datediff,
                       itc.employer_account_id,
                       sum(case when feature_id = 3 and itc.action_type_id = 4 then action_cnt else 0 end)::float / sum(action_cnt)  as message_itc_percent,
                       sum(case when feature_id = 3 and itc.action_type_id = 1 then action_cnt else 0 end)::float / sum(action_cnt)  as phone_itc_percent,
                       sum(case when feature_id = 3 and itc.action_type_id = 2 then action_cnt else 0 end)::float / sum(action_cnt)  as email_itc_percent,
                       sum(case when feature_id = 3 and itc.action_type_id = 6 then action_cnt else 0 end)::float / sum(action_cnt)  as download_cv_itc_percent,
                       coalesce(sum(case when feature_id in (1, 2) then action_cnt else 0 end), 0)  as job_apply_itc_cnt
                from employer.employer_intention_to_contact itc
                         inner join profile_base.active_employer_weekly aew
                         on itc.employer_account_id = aew.employer_account_id
                            and itc.action_datediff between week_start_datediff and week_start_datediff + 6
                where itc.country_id = 1
                  and feature_id = 3
                  and week_start_datediff = fn_get_date_diff(_dd_date) - 7
                group by week_start_datediff, itc.employer_account_id
            )
            update profile_base.active_employer_weekly aesd
            set message_itc_percent = a.message_itc_percent,
                phone_itc_percent = a.phone_itc_percent,
                email_itc_percent = a.email_itc_percent,
                download_cv_itc_percent = a.download_cv_itc_percent,
                job_apply_itc_cnt = a.job_apply_itc_cnt
            from a as a
            where aesd.week_start_datediff = a.week_start_datediff
              and  aesd.employer_account_id = a.employer_account_id
            ;


            --  % відгуків, що були переглянуті роботодавцем
            with apply_seen as (
                select week_start_datediff,
                       employer_account_id,
                       (case
                            when count(distinct ja.id) = 0 then null
                            else round(count(distinct case when date_seen is not null then ja.id end) /
                                       count(distinct ja.id)::numeric, 2)
                       end) as apply_seen_percent
                from profile_base.active_employer_weekly aew
                         left join imp_employer.job j
                         on j.sources = 1
                            and aew.employer_account_id = j.id_account

                         left join imp_employer.job_apply ja
                         on j.sources = ja.sources
                            and j.id = ja.id_job
                            and ja.dd_session_apply between week_start_datediff and week_start_datediff + 6
                where week_start_datediff = fn_get_date_diff(_dd_date) - 7
                group by week_start_datediff, employer_account_id
            )
            update profile_base.active_employer_weekly aesd
            set apply_seen_percent = a.apply_seen_percent
            from apply_seen a
            where aesd.week_start_datediff = a.week_start_datediff
              and aesd.employer_account_id = a.employer_account_id;


            -- 1. процент використань бази профілів без пошуку  2. всі пошукові запити (keywords)
            with empty_search as (
                select week_start_datediff,
                       employer_account_id,
                       count(case when ps.keywords is null then ps.id end) / cast(count(ps.id) as decimal) as empty_search_query_percent,
                       string_agg(distinct keywords, '; ') as search_keyword
                from profile_base.active_employer_weekly aew
                     left join imp_employer.profile_search ps
                     on ps.sources = 1
                        and ps.id_account = aew.employer_account_id
                        and ps.date_diff between week_start_datediff and week_start_datediff + 6
                where week_start_datediff = fn_get_date_diff(_dd_date) - 7
                group by week_start_datediff, employer_account_id
            )
            update profile_base.active_employer_weekly aesd
            set empty_search_query_percent = a.empty_search_query_percent,
                search_keyword = a.search_keyword
            from empty_search a
            where aesd.week_start_datediff = a.week_start_datediff
              and aesd.employer_account_id = a.employer_account_id;


            -- всі вакансії
            with jobs as (
                select week_start_datediff,
                       employer_account_id,
                       string_agg(distinct j.title, '; ') as vacancy_string_agg
                from profile_base.active_employer_weekly aew
                         left join imp_employer.job j
                                   on j.sources = 1
                                       and aew.employer_account_id = j.id_account
                where week_start_datediff = fn_get_date_diff(_dd_date) - 7
                group by week_start_datediff, employer_account_id
            )
            update profile_base.active_employer_weekly aew
            set vacancy = a.vacancy_string_agg
            from jobs a
            where aew.employer_account_id = a.employer_account_id
              and aew.week_start_datediff = a.week_start_datediff;


            drop table active_employer;

end;

$$;

alter procedure profile_base.insert_profile_base_active_employer_weekly(date) owner to rlu;

