with search as (
            select distinct id_account,
                   id_session,
                   count(id) over (partition by id_account, id_session, sources) as search_by_session_cnt,
                   device_type,
                   -- параметры поиска
                   id as id_search,
                   date as date_search,
                   trigger as type_query,
                   --, cast(date as date) as date_search
                   keywords,
                   id_region, -- add name region
                   additional_filters_count,
                   additional_filters_json, -- распарсить затем фильтры
                   results_total,
                   sources
            from imp_employer.profile_search),
            res_search_action as (
                -- все действия совершенные
                select distinct id_account,
                       id_search,
                       action,
                       sum(case when action=0 then 1 else 0 end) over (partition by id_search, sources) as view_by_search_cnt,
                       sources
                from imp_employer.profile_search_action),
            view_seach as (
                -- просмотры по результату поиска
                select distinct id_search,
                       view_by_search_cnt,
                       sources
                from res_search_action)

            select rel_acc.country_code,
                   rel_acc.account_type,
                   rel_acc.id_employer,
                   rel_acc.date_created_ea,
                   search.*,
                   coalesce(reg.display_name, '-99') as display_name,
                   coalesce(reg.name, '-99') as region_name,
                   coalesce(view_by_search_cnt, 0) as view_by_search_cnt
            from search
            inner join nsh.employer_related_account rel_acc on search.id_account = rel_acc.id_account and search.sources = rel_acc.sources
            left join view_seach on search.id_search = view_seach.id_search and search.sources = view_seach.sources
            left join dimension.info_region reg on reg.id = search.id_region;
