select j.country_id,
       j.jdp_viewed_datediff,
       count(distinct j.jdp_id) as jdp_with_apply_cnt,
       count(distinct sj.id) as jdp_with_show_on_map_cnt,
       count(distinct sja.id_jdp) as jdp_with_show_on_map_click_cnt
from apply.jdp j
left join imp.session_jdp sj on sj.country = j.country_id and sj.date_diff = j.jdp_viewed_datediff and sj.id = j.jdp_id
 and sj.flags & 262144 = 262144
left join imp.session_jdp_action sja on sja.country = sj.country and sja.date_diff = sj.date_diff and sja.id_jdp = sj.id
    and sja.type in ( 35, 46,  48)
where j.jdp_viewed_datediff = ${DT_NOW} - 1
   and j.country_id = 1
group by j.country_id,
       j.jdp_viewed_datediff;