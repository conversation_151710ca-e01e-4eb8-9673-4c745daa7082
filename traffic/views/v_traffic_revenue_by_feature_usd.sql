create view traffic.v_traffic_revenue_by_feature_usd(date_diff, session_traffic_source_id, session_traffic_source_group_id, traffic_source_name, apply_revenue_usd, profile_revenue_usd, call_revenue_usd, apply_paid_packet_revenue_usd, profile_paid_packet_revenue_usd, call_paid_packet_revenue_usd) as
	SELECT ptr.date_diff,
       ptr.session_traffic_source_id,
       ptr.session_traffic_source_group_id,
       ptr.traffic_source_name,
       COALESCE(sum(
                        CASE
                            WHEN ptr.feature_type_id = 1 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            WHEN ptr.feature_type_id = 1 AND ptr.packet_type_id <> 2 THEN 12::numeric
                            WHEN ptr.feature_type_id = 1 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id <> 1 THEN 12::numeric
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS apply_revenue_usd,
       COALESCE(sum(
                        CASE
                            WHEN (ptr.feature_type_id = ANY (ARRAY [2, 3])) AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            WHEN (ptr.feature_type_id = ANY (ARRAY [2, 3])) AND ptr.packet_type_id <> 2 THEN 12::numeric
                            WHEN (ptr.feature_type_id = ANY (ARRAY [2, 3])) AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id <> 1 THEN 12::numeric
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS profile_revenue_usd,
       COALESCE(sum(
                        CASE
                            WHEN ptr.feature_type_id = 4 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            WHEN ptr.feature_type_id = 4 AND ptr.packet_type_id <> 2 THEN 12::numeric
                            WHEN ptr.feature_type_id = 4 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id <> 1 THEN 12::numeric
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS call_revenue_usd,
       COALESCE(sum(
                        CASE
                            WHEN ptr.feature_type_id = 1 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS apply_paid_packet_revenue_usd,
       COALESCE(sum(
                        CASE
                            WHEN (ptr.feature_type_id = ANY (ARRAY [2, 3])) AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS profile_paid_packet_revenue_usd,
       COALESCE(sum(
                        CASE
                            WHEN ptr.feature_type_id = 4 AND ptr.packet_type_id = 2 AND
                                 ptr.packet_type_paid_result_id = 1 THEN ptr.open_contact_price
                            ELSE NULL::numeric
                            END) / cs.to_usd, 0::numeric) AS call_paid_packet_revenue_usd
FROM traffic.v_traffic_revenue_by_feature ptr
         JOIN imp_statistic.currency_source cs
              ON cs.currency = 'UAH'::bpchar AND ptr.date_diff = fn_get_date_diff(cs.date::timestamp without time zone)
GROUP BY ptr.date_diff, ptr.session_traffic_source_id, ptr.session_traffic_source_group_id, ptr.traffic_source_name,
         cs.to_usd;

alter table traffic.v_traffic_revenue_by_feature_usd owner to postgres;

grant select, update on traffic.v_traffic_revenue_by_feature_usd to readonly;

