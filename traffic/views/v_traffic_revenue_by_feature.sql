create view traffic.v_traffic_revenue_by_feature(date_diff, session_traffic_source_id, session_traffic_source_group_id, packet_type_paid_result_id, packet_type_id, jcoin_cnt, free_jcoin_cnt, open_contact_price, feature_type_id, traffic_source_name) as
	WITH revenue_real AS (
    SELECT apply_open_contact.apply_datediff          AS date_diff,
           apply_open_contact.session_traffic_source_id,
           apply_open_contact.session_traffic_source_group_id,
           apply_open_contact.packet_type_paid_result_id,
           apply_open_contact.packet_type_id,
           count(apply_open_contact.profile_id)       AS jcoin_cnt,
           count(
                   CASE
                       WHEN apply_open_contact.packet_type_id <> 2 OR
                            apply_open_contact.packet_type_id = 2 AND apply_open_contact.packet_type_paid_result_id <> 1
                           THEN apply_open_contact.profile_id
                       ELSE NULL::integer
                       END)                           AS free_jcoin_cnt,
           sum(apply_open_contact.open_contact_price) AS open_contact_price,
           1                                          AS feature_type_id
    FROM employer.apply_open_contact
    WHERE (fn_get_date_diff(apply_open_contact.open_contact_datetime) - apply_open_contact.apply_datediff) >= 0
      AND (fn_get_date_diff(apply_open_contact.open_contact_datetime) - apply_open_contact.apply_datediff) <= 7
    GROUP BY apply_open_contact.apply_datediff, apply_open_contact.session_traffic_source_id,
             apply_open_contact.session_traffic_source_group_id, apply_open_contact.packet_type_paid_result_id,
             apply_open_contact.packet_type_id
    UNION ALL
    SELECT digital_recruiter_open_contact.profile_submission_datediff,
           digital_recruiter_open_contact.session_traffic_source_id,
           digital_recruiter_open_contact.session_traffic_source_group_id,
           digital_recruiter_open_contact.packet_type_paid_result_id,
           digital_recruiter_open_contact.packet_type_id,
           count(digital_recruiter_open_contact.profile_id)       AS jcoin_cnt,
           count(
                   CASE
                       WHEN digital_recruiter_open_contact.packet_type_id <> 2 OR
                            digital_recruiter_open_contact.packet_type_id = 2 AND
                            digital_recruiter_open_contact.packet_type_paid_result_id <> 1
                           THEN digital_recruiter_open_contact.profile_id
                       ELSE NULL::integer
                       END)                                       AS free_jcoin_cnt,
           sum(digital_recruiter_open_contact.open_contact_price) AS open_contact_price,
           2                                                      AS feature_type_id
    FROM employer.digital_recruiter_open_contact
    WHERE (fn_get_date_diff(digital_recruiter_open_contact.open_contact_datetime) -
           digital_recruiter_open_contact.profile_submission_datediff) >= 0
      AND (fn_get_date_diff(digital_recruiter_open_contact.open_contact_datetime) -
           digital_recruiter_open_contact.profile_submission_datediff) <= 7
    GROUP BY digital_recruiter_open_contact.profile_submission_datediff,
             digital_recruiter_open_contact.session_traffic_source_id,
             digital_recruiter_open_contact.session_traffic_source_group_id,
             digital_recruiter_open_contact.packet_type_paid_result_id, digital_recruiter_open_contact.packet_type_id
    UNION ALL
    SELECT pss.submission_datediff,
           pbo.session_traffic_source_id,
           pbo.session_traffic_source_group_id,
           pbo.packet_type_paid_result_id,
           pbo.packet_type_id,
           count(pbo.profile_id)       AS jcoin_cnt,
           count(
                   CASE
                       WHEN pbo.packet_type_id <> 2 OR pbo.packet_type_id = 2 AND pbo.packet_type_paid_result_id <> 1
                           THEN pbo.profile_id
                       ELSE NULL::integer
                       END)            AS free_jcoin_cnt,
           sum(pbo.open_contact_price) AS open_contact_price,
           3                           AS feature_type_id
    FROM profile_base.profile_base_open_contact pbo
             JOIN profile.profile_submission_traffic_source pss
                  ON pbo.profile_id = pss.profile_id AND pbo.database_source_id = 1 AND pss.country_id = 1 AND
                     (pbo.open_contact_datediff - pss.submission_datediff) >= 0 AND
                     (pbo.open_contact_datediff - pss.submission_datediff) <= 7
    GROUP BY pss.submission_datediff, pbo.session_traffic_source_id, pbo.session_traffic_source_group_id,
             pbo.packet_type_paid_result_id, pbo.packet_type_id
    UNION ALL
    SELECT call_open_contact.call_datediff,
           call_open_contact.session_traffic_source_id,
           call_open_contact.session_traffic_source_group_id,
           call_open_contact.packet_type_paid_result_id,
           call_open_contact.packet_type_id,
           count(call_open_contact.session_id)       AS jcoin_cnt,
           count(
                   CASE
                       WHEN call_open_contact.packet_type_id <> 2 OR
                            call_open_contact.packet_type_id = 2 AND call_open_contact.packet_type_paid_result_id <> 1
                           THEN call_open_contact.session_id
                       ELSE NULL::bigint
                       END)                          AS free_jcoin_cnt,
           sum(call_open_contact.open_contact_price) AS open_contact_price,
           4                                         AS feature_type_id
    FROM profile.call_open_contact
    GROUP BY call_open_contact.call_datediff, call_open_contact.session_traffic_source_id,
             call_open_contact.session_traffic_source_group_id, call_open_contact.packet_type_paid_result_id,
             call_open_contact.packet_type_id
)
SELECT rr.date_diff,
       rr.session_traffic_source_id,
       rr.session_traffic_source_group_id,
       rr.packet_type_paid_result_id,
       rr.packet_type_id,
       rr.jcoin_cnt,
       rr.free_jcoin_cnt,
       rr.open_contact_price,
       rr.feature_type_id,
       uts.name AS traffic_source_name
FROM revenue_real rr
         LEFT JOIN dimension.u_traffic_source uts ON rr.session_traffic_source_id = uts.id AND uts.country = 1;

alter table traffic.v_traffic_revenue_by_feature owner to rlu;

grant select on traffic.v_traffic_revenue_by_feature to readonly;

grant select on traffic.v_traffic_revenue_by_feature to writeonly_product;

