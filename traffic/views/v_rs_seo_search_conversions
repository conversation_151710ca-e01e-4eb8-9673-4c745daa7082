create or replace view traffic.v_rs_seo_search_conversions
            (period, search_date, url, kw, reg_id, impressions, clicks, avg_position, ctr, potential_traffic,
             traffic_channel, search_wdata_cnt, search_away_cnt, away_cnt, jdp_click_cnt, conversion_cnt,
             phone_view_cnt, job_ea_cnt, job_cnt)
as
WITH dictionary AS (
    SELECT a.url,
           a.kw,
           a.reg_id
    FROM (SELECT pt.url,
                 COALESCE(pt.kw, ''::bpchar::text) AS kw,
                 pt.reg_id
          FROM traffic.ea_seo_urls_potential pt
          UNION ALL
          SELECT pt.url,
                 COALESCE(pt.kw, ''::bpchar) AS kw,
                 pt.reg_id
          FROM traffic.rs_potential_traffic_urls pt) a
    GROUP BY a.url, a.kw, a.reg_id
)
SELECT pt.period,
       "left"(pt.period, 10)::date       AS search_date,
       pt.url,
       COALESCE(pt.kw, ''::bpchar::text) AS kw,
       pt.reg_id,
       pt.impressions,
       pt.clicks,
       pt.avg_position,
       pt.ctr,
       pt.potential_traffic,
       NULL::character varying           AS traffic_channel,
       NULL::bigint                      AS search_wdata_cnt,
       NULL::bigint                      AS search_away_cnt,
       NULL::bigint                      AS away_cnt,
       NULL::bigint                      AS jdp_click_cnt,
       NULL::bigint                      AS conversion_cnt,
       NULL::bigint                      AS phone_view_cnt,
       NULL::bigint                      AS job_ea_cnt,
       NULL::bigint                      AS job_cnt
FROM traffic.ea_seo_urls_potential pt
UNION ALL
SELECT pt.period::text                   AS period,
       "left"(pt.period::text, 10)::date AS search_date,
       pt.url,
       COALESCE(pt.kw, ''::bpchar)       AS kw,
       pt.reg_id,
       pt.impressions,
       pt.clicks,
       pt.avg_position,
       pt.ctr,
       pt.potential_traffic,
       NULL::character varying           AS traffic_channel,
       NULL::bigint                      AS search_wdata_cnt,
       NULL::bigint                      AS search_away_cnt,
       NULL::bigint                      AS away_cnt,
       NULL::bigint                      AS jdp_click_cnt,
       NULL::bigint                      AS conversion_cnt,
       NULL::bigint                      AS phone_view_cnt,
       NULL::bigint                      AS job_ea_cnt,
       NULL::bigint                      AS job_cnt
FROM traffic.rs_potential_traffic_urls pt
UNION ALL
SELECT (to_char(date_trunc('month'::text, ic.dt::timestamp with time zone), 'YYYY-MM-DD'::text) || '<-->'::text) ||
       to_char((date_trunc('month'::text, ic.dt::timestamp with time zone) + '1 mon'::interval -
                '1 day'::interval)::date::timestamp with time zone, 'YYYY-MM-DD'::text) AS period,
       ic.dt                                                                            AS search_date,
       url.url,
       COALESCE(data.q_kw, ''::text)                                                    AS kw,
       data.q_id_region                                                                 AS reg_id,
       NULL::integer                                                                    AS impressions,
       NULL::integer                                                                    AS clicks,
       NULL::integer                                                                    AS avg_position,
       NULL::numeric                                                                    AS ctr,
       NULL::numeric                                                                    AS potential_traffic,
       data.traffic_channel,
       data.search_wdata_cnt,
       data.search_away_cnt,
       data.away_cnt,
       data.jdp_click_cnt,
       data.conversion_cnt,
       data.phone_view_cnt,
       data.job_ea_cnt,
       data.job_cnt
FROM traffic.ea_search_conversion data
         LEFT JOIN dimension.info_calendar ic ON ic.date_diff = data.date_diff
         LEFT JOIN dictionary url
                   ON COALESCE(data.q_kw, ''::text) = COALESCE(url.kw, ''::text) AND data.q_id_region = url.reg_id;

alter table traffic.v_rs_seo_search_conversions
    owner to vnov;
