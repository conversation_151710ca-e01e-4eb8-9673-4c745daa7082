SET NOCOUNT ON;

DECLARE @date_diff int = DATEDIFF(DAY, 0, GETDATE());

WITH click AS (SELECT bc.id_banner,
                      DATEDIFF(DAY, 0, bc.date_click) AS date_diff,
                      MAX(bc.date_click)              AS last_date_click,
                      COUNT(DISTINCT bc.id)           AS clicks

               FROM dbo.banner_click bc WITH (NOLOCK)
               WHERE DATEDIFF(DAY, 0, bc.date_click) = @date_diff
               GROUP BY bc.id_banner,
                        DATEDIFF(DAY, 0, bc.date_click)),

     show AS (SELECT bsh.id_banner,
                     DATEDIFF(DAY, 0, bsh.date_show) AS date_diff,
                     MAX(bsh.date_show)              AS last_date_show,
                     COUNT(DISTINCT bsh.id)          AS shows

              FROM dbo.banner_show bsh WITH (NOLOCK)
              WHERE DATEDIFF(DAY, 0, bsh.date_show) = @date_diff
              GROUP BY bsh.id_banner,
                       DATEDIFF(DAY, 0, bsh.date_show))

SELECT show.id_banner AS banner_id,
       show.date_diff,
       show.last_date_show,
       click.last_date_click,
       show.shows,
       click.clicks
FROM show
         LEFT JOIN click ON show.id_banner = click.id_banner
    AND show.date_diff = click.date_diff
;
