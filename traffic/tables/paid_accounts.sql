select id,
       original_id,
       name,
       counter_ads,
       counter_campaign,
       counter_adgroups_items,
       isNeedSync   as is_need_sync,
       sync_at,
       countryCode  as country_code,
       startUrl     as start_url,
       created_at,
       updated_at,
       isNeedUpload as is_need_upload,
       minBid       as min_bid,
       maxBid       as max_bid,
       defaultBid   as default_bid,
       counter_adgroups,
       isExported   as is_exported,
       isBider      as is_bider,
       bid_update_at,
       google_sync_at,
       sync_uid,
       sync_start_at,
       sync_error,
       time_zone,
       currency_code,
       priority,
       sync_api_version,
       status,
       budget_update_at,
       budget_sync_at
from [dbo].[accounts];
