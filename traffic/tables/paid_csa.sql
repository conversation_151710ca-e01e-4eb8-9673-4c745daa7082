select date     as date,
       country  as country,
       [source] as [type],
       [type]   as [source],
       device   as device,
       label    as label,
       sum(CSA) as csa,
       null     as csa_1
from (select [Дата]                                  date,
             replace([Клиентский канал], 'afc.', '') channel,
             sum([Расчетный доход (USD)])            CSA
      from PublicStatistic.dbo.adsense_csa_2018
      where year([Дата]) >= 2023
      group by [Дата],
               replace([Клиентский канал], 'afc.', '')
      having sum([Расчетный доход (USD)]) > 0) CSA
         inner join [Marketing].[legent].[labels_and_channels] labels
                    on CSA.channel = labels.channel_0 collate SQL_Latin1_General_CP1_CI_AS
where labels.label is not NULL
  and source not in ('yandex', 'taboola', 'criteo')
group by date, country, type, device, label, source
UNION
select date     as date,
       country  as country,
       [source] as [type],
       [type]   as [source],
       device   as device,
       label    as label,
       null     as csa,
       sum(CSA) as csa_1
from (select [Дата]                                  date,
             replace([Клиентский канал], 'afc.', '') channel,
             sum([Расчетный доход (USD)])            CSA
      from PublicStatistic.dbo.adsense_csa_2018
      where year([Дата]) >= 2023
      group by [Дата],
               replace([Клиентский канал], 'afc.', '')
      having sum([Расчетный доход (USD)]) > 0) CSA
         inner join [Marketing].[legent].[labels_and_channels] labels
                    on CSA.channel = labels.channel_1 collate SQL_Latin1_General_CP1_CI_AS
where labels.label is not NULL
  and source not in ('yandex', 'taboola', 'criteo')
group by date, country, type, device, label, source;
