        drop table if exists conversion_away_connection_tmp;
        create temp table conversion_away_connection_tmp AS
        SELECT sa.id_project,
               min(cac.date_diff) AS conversion_start
        FROM link_auction.conversion_away_connection cac
                 JOIN session_away sa ON cac.date_diff = sa.date_diff AND cac.id_session_away = sa.id
        WHERE sa.date_diff = _input_date_diff
        GROUP BY sa.id_project;

        drop table if exists temp_dis_conversion_away_connection;
        create temp table temp_dis_conversion_away_connection AS
        SELECT DISTINCT conversion_away_connection.id_session_away, date_diff as conversion_start
        FROM link_auction.conversion_away_connection;

        create temp table temp_u_traffic_source as
        select *
        from link_dbo.u_traffic_source;

        create temp table temp_info_project as
        select *
        from link_dbo.info_project;

        create temp table temp_campaign as
        select *
        from link_auction.campaign;

        create temp table temp_site as
        select *
        from link_auction.site;

        create temp table temp_user as
        select *
        from link_auction."user";

        drop table if exists temp_session_utm;
        create temp table temp_session_utm as
        select *
        FROM link_dbo.session_utm sutm
        where date_diff = _input_date_diff;

        drop table if exists temp_session_utm_agg;
        create temp table temp_session_utm_agg as
        select sutm.date_diff,
               sutm.id_session,
               min(sutm.utm_content)     AS utm_content
        FROM temp_session_utm sutm
        group by sutm.date_diff, sutm.id_session;


        drop table if exists away_revenue_union;
        create temp table away_revenue_union AS
        SELECT sa.date_diff,
               s.id_traf_source,
               s.id_current_traf_source,
               sa.id_session,
               sa.id                                       AS id_away,
               'aways'                                     AS metric,
               CASE
                   WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
                   WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
                   WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
                   ELSE 'Other'::text
                   END                                     AS away_type,
               sa.id_project,
               info_project.name                           AS project_name,
               ac.name                                     AS campaign_name,
               ac.id                                       AS id_campaign,
               0                                           AS click_price_usd,
               COALESCE(sa.letter_type, sj.letter_type)    AS letter_type,
               COALESCE(sc.id_recommend, scj.id_recommend) AS id_recommend,
               COALESCE(sc.id_alertview, scj.id_alertview) AS id_alertview,
               COALESCE(sc.id_search, scj.id_search)       AS id_search,
               ext.id                                      AS id_external,
               CASE
                   WHEN sa.click_price <> 0::numeric THEN 'paid'::text
                   WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
                   ELSE 'free'::text
                   END                                     AS is_paid,
               sign((s.flags & 16)::double precision)      AS is_mobile,
               sign((s.flags & 2)::double precision)       AS is_returned,
               s.session_create_page_type,
               CASE
                   WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                               FROM an.snap_campaign_log cl
                                               WHERE cl.date <= sa.date
                                                 AND cl.id_campaign = ac.id
                                               ORDER BY cl.date DESC
                                               LIMIT 1)) & 32) = 32 THEN 1
                   ELSE 0
                   END                                     AS is_paid_overflow,
               CASE
                   WHEN s.ip_cc::text = current_database() OR s.ip_cc::text = 'gb'::text AND current_database() = 'uk'::name
                       THEN 1
                   ELSE 0
                   END                                     AS is_local,
               iif((sa.flags & 512) <> 0, 1, 0)            AS is_duplicated,
               au.id                                       AS user_id,
               CASE
                   WHEN (sa.flags & 2048) = 2048 OR (COALESCE(ss.search_source, ssj.search_source) = ANY
                                                     (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                       THEN 1
                   WHEN (sa.flags & 4096) = 4096 OR COALESCE(ss.search_source, ssj.search_source) = 145 THEN 2
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [151, 152, 153, 154]) THEN 4
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [155, 156, 157, 158]) THEN 5
                   ELSE NULL::integer
                   END                                     AS add_placement,
               COALESCE(jh.id_category, j.id_category)     AS id_job_category
        FROM session_away sa
                 JOIN session s ON sa.date_diff = s.date_diff AND sa.id_session = s.id
                 LEFT JOIN temp_u_traffic_source uts ON s.id_traf_source = uts.id
                 LEFT JOIN temp_campaign ac ON ac.id = sa.id_campaign
                 LEFT JOIN temp_site ast ON ac.id_site = ast.id
                 LEFT JOIN temp_user au ON au.id = ast.id_user
                 LEFT JOIN session_click sc ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
                 LEFT JOIN session_jdp sj ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
                 LEFT JOIN session_click scj ON scj.date_diff = sj.date_diff AND scj.id = sj.id_click
                 LEFT JOIN session_external ext ON ext.date_diff = sa.date_diff AND ext.id_away = sa.id
                 LEFT JOIN session_search ss ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
                 LEFT JOIN session_search ssj ON ssj.date_diff = scj.date_diff AND ssj.id = scj.id_search
                 LEFT JOIN an.snap_job j ON sa.id_job = j.id
                 LEFT JOIN an.snap_job_history jh ON sa.uid_job = jh.uid
                 LEFT JOIN temp_info_project info_project ON sa.id_project = info_project.id
        WHERE sa.date_diff = _input_date_diff
          AND (COALESCE(s.flags, 0) & 1) = 0
          AND (COALESCE(sa.flags, 0) & 2) = 0 /* test campaign away */
          AND coalesce(lower(info_project.name), '') not like 'j-vers.%';
        --UNION ALL

        create temp table temp_revenue as
        SELECT sa.date_diff,
               s.id_traf_source,
               s.id_current_traf_source,
               s.id                                        AS id_session,
               NULL::bigint                                AS id_away,
               CASE
                   WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
                   WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
                   WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
                   ELSE 'Other'::text
                   END                                     AS away_type,
               sa.id_project,
               info_project.name                           AS project_name,
               ac.name                                     AS campaign_name,
               ac.id                                       AS id_campaign,
               sa.click_price * ic.value_to_usd            AS click_price_usd,
               COALESCE(sa.letter_type, sj.letter_type)    AS letter_type,
               COALESCE(sc.id_recommend, scj.id_recommend) AS id_recommend,
               COALESCE(sc.id_alertview, scj.id_alertview) AS id_alertview,
               COALESCE(sc.id_search, scj.id_search)       AS id_search,
               ext.id                                      AS id_external,
               CASE
                   WHEN sa.click_price <> 0::numeric THEN 'paid'::text
                   WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
                   ELSE 'free'::text
                   END                                     AS is_paid,
               sign((s.flags & 16)::double precision)      AS is_mobile,
               sign((s.flags & 2)::double precision)       AS is_returned,
               s.session_create_page_type,
               CASE
                   WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                               FROM an.snap_campaign_log cl
                                               WHERE cl.date <= sa.date
                                                 AND cl.id_campaign = ac.id
                                               ORDER BY cl.date DESC
                                               LIMIT 1)) & 32) = 32 THEN 1
                   ELSE 0
                   END                                     AS is_paid_overflow,
               CASE
                   WHEN s.ip_cc::text = current_database() OR s.ip_cc::text = 'gb'::text AND current_database() = 'uk'::name
                       THEN 1
                   ELSE 0
                   END                                     AS is_local,
               iif((sa.flags & 512) <> 0, 1, 0)            AS is_duplicated,
               au.id                                       AS user_id,
               CASE
                   WHEN (sa.flags & 2048) = 2048 OR (COALESCE(ss.search_source, ssj.search_source) = ANY
                                                     (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                       THEN 1
                   WHEN (sa.flags & 4096) = 4096 OR COALESCE(ss.search_source, ssj.search_source) = 145 THEN 2
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [151, 152, 153, 154]) THEN 4
                   WHEN COALESCE(ss.search_source, ssj.search_source) = ANY (ARRAY [155, 156, 157, 158]) THEN 5
                   ELSE NULL::integer
                   END                                     AS add_placement,
               0                                           as is_apply,
               COALESCE(jh.id_category, j.id_category)     AS id_job_category
        FROM session_away sa
                 JOIN session s ON sa.date_diff = s.date_diff AND sa.id_session = s.id
                 JOIN an.snap_info_currency ic ON ic.id = sa.id_currency
                 LEFT JOIN temp_campaign ac ON ac.id = sa.id_campaign
                 LEFT JOIN temp_site ast ON ac.id_site = ast.id
                 LEFT JOIN temp_user au ON au.id = ast.id_user
                 LEFT JOIN temp_u_traffic_source uts ON s.id_traf_source = uts.id
                 LEFT JOIN session_click sc ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
                 LEFT JOIN session_jdp sj ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
                 LEFT JOIN session_click scj ON scj.date_diff = sj.date_diff AND scj.id = sj.id_click
                 LEFT JOIN session_external ext ON ext.date_diff = sa.date_diff AND ext.id_away = sa.id
                 LEFT JOIN session_search ss ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
                 LEFT JOIN session_search ssj ON ssj.date_diff = scj.date_diff AND ssj.id = scj.id_search
                 LEFT JOIN an.snap_job j ON sa.id_job = j.id
                 LEFT JOIN an.snap_job_history jh ON sa.uid_job = jh.uid
                 LEFT JOIN temp_info_project info_project ON sa.id_project = info_project.id
        WHERE sa.date_diff = _input_date_diff
          AND (COALESCE(s.flags, 0) & 1) = 0
          AND (sa.id_campaign = 0 OR (au.flags & 2) = 0)
          AND (COALESCE(sa.flags, 0) & 2) = 0
          AND coalesce(lower(info_project.name), '') not like 'j-vers.%';
        --UNION ALL
        insert into temp_revenue
        SELECT sc.date_diff,
               s.id_traf_source,
               s.id_current_traf_source,
               s.id                                               AS id_session,
               NULL::bigint                                       AS id_away,
               CASE
                   WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
                   WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
                   WHEN COALESCE(sa.letter_type, sj.letter_type) IS NOT NULL THEN 'Away from LT8'::text
                   ELSE 'Other'::text
                   END                                            AS away_type,
               sc.id_project,
               info_project.name                                  AS project_name,
               ac.name                                            AS campaign_name,
               ac.id                                              AS id_campaign,
               sc.click_price * ic.value_to_usd                   AS click_price_usd,
               COALESCE(sa.letter_type, sj.letter_type)           AS letter_type,
               sc.id_recommend,
               sc.id_alertview,
               sc.id_search,
               ext.id                                             AS id_external,
               CASE
                   WHEN sc.click_price <> 0::numeric THEN 'paid'::text
                   WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
                   ELSE 'free'::text
                   END                                            AS is_paid,
               sign((s.flags & 16)::double precision)             AS is_mobile,
               sign((s.flags & 2)::double precision)              AS is_returned,
               s.session_create_page_type,
               CASE
                   WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                               FROM an.snap_campaign_log cl
                                               WHERE cl.date <= sa.date
                                                 AND cl.id_campaign = ac.id
                                               ORDER BY cl.date DESC
                                               LIMIT 1)) & 32) = 32 THEN 1
                   ELSE 0
                   END                                            AS is_paid_overflow,
               CASE
                   WHEN s.ip_cc::text = current_database() OR s.ip_cc::text = 'gb'::text AND current_database() = 'uk'::name
                       THEN 1
                   ELSE 0
                   END                                            AS is_local,
               iif((sc.flags & 4096) <> 0, 1, 0)                  AS is_duplicated,
               au.id                                              AS user_id,
               CASE
                   WHEN (sa.flags & 2048) = 2048 OR sj.source = 9 OR
                        (ss.search_source = ANY (ARRAY [118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144]))
                       THEN 1
                   WHEN (sa.flags & 4096) = 4096 OR ss.search_source = 145 THEN 2
                   WHEN ss.search_source = ANY (ARRAY [146, 147, 148, 149, 150]) THEN 3
                   WHEN ss.search_source = ANY (ARRAY [151, 152, 153, 154]) THEN 4
                   WHEN ss.search_source = ANY (ARRAY [155, 156, 157, 158]) THEN 5
                   ELSE NULL::integer
                   END                                            AS add_placement,
               case when sc.job_destination = 3 then 1 else 0 end as is_apply,
               COALESCE(jh.id_category, j.id_category)            AS id_job_category
        FROM session_click sc
                 JOIN session s ON sc.date_diff = s.date_diff AND sc.id_session = s.id
                 JOIN an.snap_info_currency ic ON ic.id = sc.id_currency
                 LEFT JOIN temp_campaign ac ON ac.id = sc.id_campaign
                 LEFT JOIN temp_site ast ON ac.id_site = ast.id
                 LEFT JOIN temp_user au ON au.id = ast.id_user
                 LEFT JOIN temp_u_traffic_source uts ON s.id_traf_source = uts.id
                 LEFT JOIN session_away sa ON sc.date_diff = sa.date_diff AND sc.id = sa.id_click
                 LEFT JOIN session_jdp sj ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
                 LEFT JOIN session_external ext ON ext.date_diff = sc.date_diff AND (ext.id_away = sa.id OR ext.id_jdp = sj.id)
                 LEFT JOIN session_search ss ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
                 LEFT JOIN an.snap_job j ON sc.id_job = j.id
                 LEFT JOIN an.snap_job_history jh ON sc.uid_job = jh.uid
                 LEFT JOIN temp_info_project info_project ON sc.id_project = info_project.id
        WHERE sc.date_diff = _input_date_diff
          AND (COALESCE(s.flags, 0) & 1) = 0
          AND (sc.id_campaign = 0 OR (au.flags & 2) = 2)
          AND (COALESCE(sc.flags, 0) & 16) = 0
          AND coalesce(lower(info_project.name), '') not like 'j-vers.%';
        --UNION ALL


        insert into temp_revenue
        SELECT scns.date_diff,
               s.id_traf_source,
               s.id_current_traf_source,
               s.id                                                 AS id_session,
               NULL::bigint                                         AS id_away,
               CASE
                   WHEN sa.id_jdp IS NOT NULL THEN 'Away from JDP'::text
                   WHEN sa.id_click IS NOT NULL THEN 'Away from SERP'::text
                   WHEN sa.letter_type IS NOT NULL THEN 'Away from LT8'::text
                   ELSE 'Other'::text
                   END                                              AS away_type,
               scns.id_project,
               info_project.name                                    AS project_name,
               ac.name                                              AS campaign_name,
               ac.id                                                AS id_campaign,
               scns.click_price * ic.value_to_usd                   AS click_price_usd,
               scns.letter_type,
               scns.id_recommend,
               NULL::bigint                                         AS id_alertview,
               NULL::bigint                                         AS id_search,
               ext.id                                               AS id_external,
               CASE
                   WHEN scns.click_price <> 0::numeric THEN 'paid'::text
                   WHEN (scns.flags & 128) = 128 THEN 'premium'::text
                   ELSE 'free'::text
                   END                                              AS is_paid,
               sign((s.flags & 16)::double precision)               AS is_mobile,
               sign((s.flags & 2)::double precision)                AS is_returned,
               s.session_create_page_type,
               CASE
                   WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                               FROM an.snap_campaign_log cl
                                               WHERE cl.date <= sa.date
                                                 AND cl.id_campaign = ac.id
                                               ORDER BY cl.date DESC
                                               LIMIT 1)) & 32) = 32 THEN 1
                   ELSE 0
                   END                                              AS is_paid_overflow,
               CASE
                   WHEN s.ip_cc::text = current_database() OR s.ip_cc::text = 'gb'::text AND current_database() = 'uk'::name
                       THEN 1
                   ELSE 0
                   END                                              AS is_local,
               iif((scns.flags & 4096) <> 0, 1, 0)                  AS is_duplicated,
               au.id                                                AS user_id,
               NULL::integer                                        AS add_placement,
               case when scns.job_destination = 3 then 1 else 0 end as is_apply,
               COALESCE(jh.id_category, j.id_category)              AS id_job_category
        FROM session_click_no_serp scns
                 JOIN session s ON scns.date_diff = s.date_diff AND scns.id_session = s.id
                 JOIN an.snap_info_currency ic ON ic.id = scns.id_currency
                 JOIN temp_campaign ac ON ac.id = scns.id_campaign
                 JOIN temp_site ast ON ac.id_site = ast.id
                 JOIN temp_user au ON au.id = ast.id_user
                 LEFT JOIN temp_u_traffic_source uts ON s.id_traf_source = uts.id
                 LEFT JOIN session_jdp sj ON  sj.date_diff = scns.date_diff AND sj.id_click_no_serp = scns.id
                 LEFT JOIN session_away sa ON sa.date_diff = scns.date_diff AND sa.id_click_no_serp = scns.id
                 LEFT JOIN session_external ext on ext.date_diff = scns.date_diff AND (ext.id_jdp = sj.id OR ext.id_away = sa.id)
                 LEFT JOIN an.snap_job j ON scns.id_job = j.id
                 LEFT JOIN an.snap_job_history jh ON scns.uid_job = jh.uid
                 LEFT JOIN temp_info_project info_project ON scns.id_project = info_project.id
        WHERE scns.date_diff = _input_date_diff
          AND (COALESCE(s.flags, 0) & 1) = 0
          AND (au.flags & 2) = 2
          AND (COALESCE(scns.flags, 0) & 16) = 0
          AND coalesce(lower(info_project.name), '') not like 'j-vers.%';


        insert into away_revenue_union
        select date_diff,
               id_traf_source,
               id_current_traf_source,
               id_session,
               id_away,
               case when is_apply = 0 then 'aways' else 'applies' end as metric,
               away_type,
               id_project,
               project_name,
               campaign_name,
               id_campaign,
               click_price_usd,
               letter_type,
               id_recommend,
               id_alertview,
               id_search,
               id_external,
               is_paid,
               is_mobile,
               is_returned,
               session_create_page_type,
               is_paid_overflow,
               is_local,
               is_duplicated,
               user_id,
               add_placement,
               id_job_category
        FROM temp_REVENUE;
        --UNION ALL


        insert into away_revenue_union
        SELECT sj.date_diff,
               s.id_traf_source,
               s.id_current_traf_source,
               s.id                                                    AS id_session,
               sj.id                                                   AS id_jdp,
               'applies'                                               AS metric,
               'JDP only'::text                                        AS away_type,
               sj.job_id_project                                       AS id_project,
               info_project.name                                       AS project_name,
               ac.name                                                 AS campaign_name,
               ac.id                                                   AS id_campaign,
               0                                                       AS click_price_usd,
               sj.letter_type,
               sc.id_recommend,
               sc.id_alertview,
               sc.id_search,
               ext.id                                                  AS id_external,
               CASE
                   WHEN COALESCE(sja.click_price, sc.click_price, scns.click_price) <> 0::numeric THEN 'paid'::text
                   WHEN (sc.flags & 128) = 128 OR (sj.flags & 256) = 256 THEN 'premium'::text
                   ELSE 'free'::text
                   END                                                 AS is_paid,
               sign((s.flags & 16)::double precision)                  AS is_mobile,
               sign((s.flags & 2)::double precision)                   AS is_returned,
               s.session_create_page_type,
               CASE
                   WHEN uts.is_paid = 1 AND (((SELECT cl.flags
                                               FROM an.snap_campaign_log cl
                                               WHERE cl.date <= sa.date
                                                 AND cl.id_campaign = ac.id
                                               ORDER BY cl.date DESC
                                               LIMIT 1)) & 32) = 32 THEN 1
                   ELSE 0
                   END                                                 AS is_paid_overflow,
               CASE
                   WHEN s.ip_cc::text = current_database() OR s.ip_cc::text = 'gb'::text AND current_database() = 'uk'::name
                       THEN 1
                   ELSE 0
                   END                                                 AS is_local,
               iif((COALESCE(sc.flags, scns.flags) & 4096) <> 0, 1, 0) AS is_duplicated,
               ast.id_user                                             AS user_id,
               case when sa.flags & 2048 = 2048 or sj.source = 9 or ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
                    when sa.flags & 4096 = 4096  or ss.search_source in (145) then 2--  category page
                    when ss.search_source in (146, 147, 148, 149, 150) then 3-- company page
                    when ss.search_source in (151, 152, 153, 154) then 4-- skill page
                    when ss.search_source in (155, 156, 157, 158) then 5-- JobDescription page
                   end as add_placement ,
               COALESCE(jh.id_category, j.id_category)                 AS id_job_category
        FROM session_jdp sj
                 JOIN session s ON sj.date_diff = s.date_diff AND sj.id_session = s.id
                 LEFT JOIN temp_u_traffic_source uts ON uts.id = s.id_traf_source
                 LEFT JOIN session_click sc ON sc.date_diff = sj.date_diff AND sc.id = sj.id_click
                 LEFT JOIN session_search ss ON ss.date_diff = sc.date_diff AND ss.id = sc.id_search
                 LEFT JOIN temp_campaign ac ON ac.id = sc.id_campaign
                 LEFT JOIN temp_site ast ON ac.id_site = ast.id
                 LEFT JOIN session_away sa ON sj.date_diff = sa.date_diff AND sj.id = sa.id_jdp
                 LEFT JOIN session_click_no_serp scns ON sj.date_diff = scns.date_diff AND sj.id_click_no_serp = scns.id
                 LEFT JOIN session_jdp_action sja ON sj.date_diff = sja.date_diff AND sj.id_ref_action = sja.id
                 LEFT JOIN session_external ext ON ext.date_diff = sj.date_diff AND (ext.id_away = sa.id OR ext.id_jdp = sj.id)
                 LEFT JOIN session_jdp_action sjaa ON sj.date_diff = sjaa.date_diff AND sj.id = sjaa.id_jdp
                 LEFT JOIN session_apply saa ON saa.date_diff = sjaa.date_diff AND saa.id_src_jdp_action = sjaa.id
                 LEFT JOIN an.snap_job_region jr ON sj.uid_job = jr.uid
                 LEFT JOIN an.snap_job j ON jr.id_job = j.id
                 LEFT JOIN an.snap_job_history jh ON sj.uid_job = jh.uid
                 LEFT JOIN temp_info_project info_project ON sj.job_id_project = info_project.id

        WHERE sj.date_diff = _input_date_diff
          --AND sa.id is null
          AND (COALESCE(s.flags, 0) & 1) = 0
          AND (COALESCE(sc.flags, 0) & 16) = 0
          AND coalesce(lower(info_project.name), '') not like 'j-vers.%';



        drop table if exists temp_click_revenue_conversion_raw;
        create temp table temp_click_revenue_conversion_raw as
        SELECT s1.date_diff,
               s1.id_traf_source,
               s1.id_current_traf_source,
               s1.id_session,
               s1.id_away,
               s1.metric                                            as conversion_type,
               s1.away_type,
               s1.click_price_usd,
               s1.click_price_usd * COALESCE((SELECT d.discount
                                              FROM link_dbo.vw_info_project_discount d
                                              WHERE d.id_project = s1.id_project
                                                AND ((date_part('year'::text, d.date) -
                                                      date_part('year'::text, '1900-01-01'::date)) * 12::double precision +
                                                     (date_part('month'::text, d.date) -
                                                      date_part('month'::text, '1900-01-01'::date))) <=
                                                    (date_trunc('month'::text, CURRENT_DATE::timestamp with time zone)::date -
                                                     '1900-01-01'::date)::double precision
                                              ORDER BY d.date DESC
                                              LIMIT 1), 0::numeric) AS revenue_usd_discount,
               s1.user_id,
               s1.id_project,
               s1.project_name,
               s1.campaign_name,
               s1.id_campaign,
               CASE
                   WHEN s1.add_placement = 1 THEN 'salary page'::text
                   WHEN s1.add_placement = 2 THEN 'category page'::text
                   WHEN s1.add_placement = 3 THEN 'company page'::text
                   WHEN s1.add_placement = 4 THEN 'skill page'::text
                   WHEN s1.add_placement = 5 THEN 'job description page'::text
                   --WHEN (s1.flags & 64) = 64 OR (s1.flags & 128) = 128 THEN 'mobile app'::text
                   WHEN info_project.hide_in_search = 1 THEN 'ad exchange'::text
                   WHEN COALESCE(s1.letter_type, email_sent.letter_type) IS NOT NULL THEN concat('letter type ',
                                                                                                 COALESCE(s1.letter_type, email_sent.letter_type))
                   WHEN s1.id_recommend IS NOT NULL THEN 'recommendations'::text
                   WHEN s1.id_alertview IS NOT NULL THEN 'other letter types'::text
                   WHEN s1.id_search IS NOT NULL THEN 'search'::text
                   WHEN s1.id_external IS NOT NULL THEN 'external'::text
                   ELSE 'other'::text
                   END                                              AS placement,
               s1.session_create_page_type,
               s1.is_local,
               s1.is_mobile,
               s1.is_paid,
               s1.is_returned,
               s1.is_paid_overflow,
               s1.is_duplicated,
               sutm.utm_content,
               s1.id_job_category,
               away_con.id_session_away                             AS id_session_away_conversion,
               CASE
                   WHEN s1.date_diff >= away_con.conversion_start THEN s1.click_price_usd
                   ELSE 0::numeric
                   END                                              AS conv_price_usd,
               CASE
                   WHEN s1.date_diff >= away_con.conversion_start THEN s1.id_away
                   ELSE 0::numeric
                   END                                              AS conv_away_id
        FROM away_revenue_union s1
                 LEFT JOIN temp_info_project info_project ON s1.id_project = info_project.id
                 LEFT JOIN (SELECT session_alertview_message.id_alertview,
                                   min(session_alertview_message.id_message::text) AS id_message
                            FROM session_alertview_message
                            WHERE session_alertview_message.date_diff = _input_date_diff--45193
                            GROUP BY session_alertview_message.id_alertview) sam ON sam.id_alertview = s1.id_alertview
                 LEFT JOIN an.email_sent ON email_sent.id_message::text = sam.id_message
                 LEFT JOIN temp_dis_conversion_away_connection away_con ON away_con.id_session_away = s1.id_away
                 LEFT JOIN temp_session_utm_agg sutm ON sutm.date_diff = s1.date_diff AND sutm.id_session = s1.id_session;

        ----
        /*delete from an.rpl_paid_metrics_by_utm
        where date_diff = _input_date_diff;*/
        ----

        /*insert into an.rpl_paid_metrics_by_utm(country_id, date_diff, id_traf_source, id_current_traf_source,
                                                user_id, project_id, project_name, campaign_name, campaign_id,
                                                placement, away_type, conversion_type, session_create_page_type,
                                                is_local, is_mobile, is_paid, is_returned, is_paid_overflow,
                                                is_duplicated, utm_content, job_category_id, revenue_usd,
                                                duplicated_revenue_usd, paid_overflow_revenue_usd, jdp_away_count,
                                                duplicated_count, paid_overflow_count, conversion_cnt,
                                                conv_away_cnt, conv_revenue_usd)*/
        select _country_id as country_id,
               r.date_diff,
               r.id_traf_source,
               r.id_current_traf_source,
               r.user_id,
               r.id_project as project_id,
               r.project_name,
               r.campaign_name,
               r.id_campaign as campaign_id,
               r.placement,
               r.away_type,
               r.conversion_type,
               r.session_create_page_type,
               r.is_local,
               r.is_mobile,
               r.is_paid,
               r.is_returned,
               r.is_paid_overflow,
               r.is_duplicated,
               r.utm_content,
               r.id_job_category as job_category_id,
               sum(iif(r.is_duplicated = 0, r.click_price_usd, 0))                  as revenue_usd,
               sum(iif(r.is_duplicated = 1, r.click_price_usd, 0))                  as duplicated_revenue_usd,
               sum(case
                       when r.is_duplicated = 0 and r.is_paid_overflow = 1 then r.click_price_usd
                       else 0 end)                                                                              as paid_overflow_revenue_usd,
               count(distinct case when r.is_duplicated = 0 then r.id_away end)                                 as jdp_away_count,
               count(distinct case when r.is_duplicated = 1 then r.id_away end)                                 as duplicated_count,
               count(distinct case
                       when r.is_duplicated = 0 and r.is_paid_overflow = 1 then r.id_away end)                 as paid_overflow_count,
               count(distinct r.id_session_away_conversion)                                                     as conversion_cnt,
               count(distinct r.conv_away_id)                                                                   as conv_away_cnt,
               sum(iif(r.is_duplicated = 0, r.conv_price_usd, 0::numeric))          as conv_revenue_usd
        from temp_click_revenue_conversion_raw r
        group by r.date_diff,
                 r.id_traf_source,
                 r.id_current_traf_source,
                 r.user_id,
                 r.id_project,
                 r.project_name,
                 r.campaign_name,
                 r.id_campaign,
                 r.placement,
                 r.away_type,
                 r.conversion_type,
                 r.session_create_page_type,
                 r.is_local,
                 r.is_mobile,
                 r.is_paid,
                 r.is_returned,
                 r.is_paid_overflow,
                 r.is_duplicated,
                 r.utm_content,
                 r.id_job_category;
