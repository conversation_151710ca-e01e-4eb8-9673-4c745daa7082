select id,
       lower(country) as country,
       case
           when (select lower(country)) = lower('UA') then 1
           when (select lower(country)) = lower('DE') then 2
           when (select lower(country)) = lower('UK') then 3
           when (select lower(country)) = lower('FR') then 4
           when (select lower(country)) = lower('CA') then 5
           when (select lower(country)) = lower('US') then 6
           when (select lower(country)) = lower('ID') then 7
           when (select lower(country)) = lower('RU') then 8
           when (select lower(country)) = lower('PL') then 9
           when (select lower(country)) = lower('HU') then 10
           when (select lower(country)) = lower('RO') then 11
           when (select lower(country)) = lower('ES') then 12
           when (select lower(country)) = lower('AT') then 13
           when (select lower(country)) = lower('BE') then 14
           when (select lower(country)) = lower('BR') then 15
           when (select lower(country)) = lower('CH') then 16
           when (select lower(country)) = lower('CZ') then 17
           when (select lower(country)) = lower('IN') then 18
           when (select lower(country)) = lower('IT') then 19
           when (select lower(country)) = lower('NL') then 20
           when (select lower(country)) = lower('TR') then 21
           when (select lower(country)) = lower('BY') then 22
           when (select lower(country)) = lower('CL') then 23
           when (select lower(country)) = lower('CO') then 24
           when (select lower(country)) = lower('GR') then 25
           when (select lower(country)) = lower('SK') then 26
           when (select lower(country)) = lower('TH') then 27
           when (select lower(country)) = lower('TW') then 28
           when (select lower(country)) = lower('VE') then 29
           when (select lower(country)) = lower('BG') then 30
           when (select lower(country)) = lower('HR') then 31
           when (select lower(country)) = lower('KZ') then 32
           when (select lower(country)) = lower('NO') then 33
           when (select lower(country)) = lower('RS') then 34
           when (select lower(country)) = lower('SE') then 35
           when (select lower(country)) = lower('NZ') then 36
           when (select lower(country)) = lower('NG') then 37
           when (select lower(country)) = lower('AR') then 38
           when (select lower(country)) = lower('MX') then 39
           when (select lower(country)) = lower('PE') then 40
           when (select lower(country)) = lower('CN') then 41
           when (select lower(country)) = lower('HK') then 42
           when (select lower(country)) = lower('KR') then 43
           when (select lower(country)) = lower('PH') then 44
           when (select lower(country)) = lower('PK') then 45
           when (select lower(country)) = lower('JP') then 46
           when (select lower(country)) = lower('CU') then 47
           when (select lower(country)) = lower('PR') then 48
           when (select lower(country)) = lower('SV') then 49
           when (select lower(country)) = lower('CR') then 50
           when (select lower(country)) = lower('AU') then 51
           when (select lower(country)) = lower('DO') then 52
           when (select lower(country)) = lower('UY') then 53
           when (select lower(country)) = lower('EC') then 54
           when (select lower(country)) = lower('SG') then 55
           when (select lower(country)) = lower('AZ') then 56
           when (select lower(country)) = lower('FI') then 57
           when (select lower(country)) = lower('BA') then 58
           when (select lower(country)) = lower('PT') then 59
           when (select lower(country)) = lower('DK') then 60
           when (select lower(country)) = lower('IE') then 61
           when (select lower(country)) = lower('MY') then 62
           when (select lower(country)) = lower('ZA') then 63
           when (select lower(country)) = lower('AE') then 64
           when (select lower(country)) = lower('QA') then 65
           when (select lower(country)) = lower('SA') then 66
           when (select lower(country)) = lower('KW') then 67
           when (select lower(country)) = lower('BH') then 68
           when (select lower(country)) = lower('EG') then 69
           when (select lower(country)) = lower('MA') then 70
           when (select lower(country)) = lower('UZ') then 71
           end        as country_id,
       jooble_id,
       name,
       utm,
       direct_ad_channel,
       return_ad_channel,
       direct_afc_channel,
       return_afc_channel,
       channel_0,
       channel_1,
       [channel_0_2 ] as channel_0_2,
       [channel_1_2 ] as channel_1_2,
       label,
       [source],
       device,
       [type]
from legent.labels_and_channels;
