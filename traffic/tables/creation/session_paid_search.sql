create table traffic.session_paid_search
(
	country_id smallint not null,
	session_datediff integer not null,
	session_id bigint not null,
	search_id bigint,
	search_date timestamp,
	keyword_name varchar(4000),
	region_id integer,
	region_original_name varchar(200),
	constraint session_paid_search_pk
		primary key (country_id, session_datediff, session_id)
);

alter table traffic.session_paid_search owner to dap;

grant select on traffic.session_paid_search to readonly;

