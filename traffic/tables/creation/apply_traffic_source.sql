create table traffic.apply_traffic_source
(
	country_id smallint not null,
	apply_id bigint not null,
	apply_datediff integer not null,
	session_id bigint,
	job_uid bigint,
	session_traffic_source_id integer,
	session_traffic_source_group_id integer,
	constraint pk_apply_traffic_source_id
		primary key (country_id, apply_datediff, apply_id)
);

alter table traffic.apply_traffic_source owner to postgres;

grant select on traffic.apply_traffic_source to readonly;

grant select on traffic.apply_traffic_source to writeonly_product;

