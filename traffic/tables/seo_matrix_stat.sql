select country_code as country_cc,
       date         as action_date,
       uniq_queries as seo_query,
       uniq_regions as region,
       pairs_wide   as matrix_wide,
       pairs        as actual_matrix,
       pairs_paid   as actual_paid_matrix,
       avg_job_count,
       total_query_job_count_paid,
       queries_not_in_matrix,
       queries_not_in_wide,
       uniq_regions_wide,
       category_pairs_wide,
       category_avg_pairs,
       category_avg_job_count,
       category_avg_job_count_paid,
       regions_avg_pairs,
       regions_avg_pairs_wide,
       pairs_paid_5
from SeoQueries.utils.matrix_stat with (nolock)
where date = :to_sql_date_start
;
