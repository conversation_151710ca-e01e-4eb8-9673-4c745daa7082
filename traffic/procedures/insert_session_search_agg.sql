create procedure traffic.insert_session_search_agg()
	language plpgsql
as $$
begin

	insert into traffic.session_search_agg(country_id, search_datediff, session_id, session_search_cnt)
	select ss.country            as country_id,
		   ss.date_diff          as search_datediff,
		   ss.id_session         as session_id,
		   count(distinct ss.id) as session_search_cnt
	from imp.session_search as ss
	where ss.country in (1, 10)
	  and ss.date_diff = fn_get_date_diff(current_date - 1)
	group by 1, 2, 3;



end;
$$;

alter procedure traffic.insert_session_search_agg() owner to yiv;

