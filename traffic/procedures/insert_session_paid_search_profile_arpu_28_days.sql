create or replace procedure traffic.insert_session_paid_search_profile_arpu_28_days(_datediff integer)
    language plpgsql
as
$$
begin

            truncate table traffic.session_paid_search_profile_arpu_28_days;

            insert into traffic.session_paid_search_profile_arpu_28_days(country_id, submission_datediff, session_id, search_id, keyword_name,
                                                                         region_id, region_original_name, profile_id, session_traffic_source_id,
                                                                         profile_base_open_contact_price, profile_base_open_contact_jcoin_cnt,
                                                                         apply_open_contact_price, apply_open_contact_jcoin_cnt, digital_recruiter_open_contact_price,
                                                                         digital_recruiter_open_contact_jcoin_cnt, call_open_contact_price, call_open_contact_jcoin_cnt)
            select sps.country_id,
                   tpa.submission_datediff,
                   sps.session_id,
                   sps.search_id,
                   sps.keyword_name,
                   sps.region_id,
                   sps.region_original_name,
                   tpa.profile_id,
                   tpa.session_traffic_source_id,
                   tpa.profile_base_open_contact_price,
                   tpa.profile_base_open_contact_jcoin_cnt,
                   tpa.apply_open_contact_price,
                   tpa.apply_open_contact_jcoin_cnt,
                   tpa.digital_recruiter_open_contact_price,
                   tpa.digital_recruiter_open_contact_jcoin_cnt,
                   tpa.call_open_contact_price,
                   tpa.call_open_contact_jcoin_cnt
            from traffic.session_paid_search sps
            join profile.session_profile_submission sps1 on sps.country_id = sps1.country_id and sps.session_datediff = sps1.session_datediff and sps.session_id = sps1.session_id
            join profile.traffic_profile_arpu_28_days tpa on sps1.country_id = tpa.country_id and sps1.profile_id = tpa.profile_id and sps1.session_datediff = tpa.submission_datediff;


end;
$$;

alter procedure traffic.insert_session_paid_search_profile_arpu_28_days(integer) owner to postgres;
