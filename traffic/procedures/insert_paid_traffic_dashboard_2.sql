create procedure traffic.insert_paid_traffic_dashboard_2()
	language plpgsql
as $$
begin
-- рахую реальне revenue з DWH
-- об'єдную разом revenue з бази профілів та цифрового рекрутера, оскільки в google ads рахуємо ревеню загальне по профілям
-- revenue перевела в USD
	create or replace view traffic.v_traffic_revenue_by_feature_usd as
	select date_diff,
		   session_traffic_source_id,
		   session_traffic_source_group_id,
		   traffic_source_name,
		   coalesce(sum(case
							when feature_type_id = 1 and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price
							when feature_type_id = 1 and packet_type_id != 2 then 12
							when feature_type_id = 1 and packet_type_id = 2 and packet_type_paid_result_id != 1 then 12
			   end) / to_usd :: numeric,
					0) as apply_revenue_usd,


		   coalesce(sum(case
							when feature_type_id in (2, 3) and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price
							when feature_type_id in (2, 3) and packet_type_id != 2 then 12
							when feature_type_id in (2, 3) and packet_type_id = 2 and packet_type_paid_result_id != 1
								then 12
			   end) / to_usd :: numeric,
					0) as profile_revenue_usd,

		   coalesce(sum(case
							when feature_type_id = 4 and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price
							when feature_type_id = 4 and packet_type_id != 2 then 12
							when feature_type_id = 4 and packet_type_id = 2 and packet_type_paid_result_id != 1 then 12
			   end) / to_usd :: numeric,
					0) as call_revenue_usd,

		   coalesce(sum(case
							when feature_type_id = 1 and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price end) / to_usd :: numeric,
					0) as apply_paid_packet_revenue_usd,

		   coalesce(sum(case
							when feature_type_id in (2, 3) and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price end) / to_usd :: numeric,
					0) as profile_paid_packet_revenue_usd,
		   coalesce(sum(case
							when feature_type_id = 4 and packet_type_id = 2 and packet_type_paid_result_id = 1
								then open_contact_price end) / to_usd :: numeric,
					0) as call_paid_packet_revenue_usd

	from traffic.v_traffic_revenue_by_feature ptr
			 join imp_statistic.currency_source cs
				  on cs.currency = 'UAH'
					  and ptr.date_diff = fn_get_date_diff(cs.date)
	group by date_diff,
			 session_traffic_source_id,
			 session_traffic_source_group_id,
			 traffic_source_name,
			 to_usd;








-- рахую предікт revenue з google ads
-- revenue перевела з cop в USD
	create or replace view traffic.v_paid_traffic_google_ads_revenue as
	select uac.date,
		   COALESCE(substr(name, position('_cpc' in name) + 1, 4), 'other') as traffic_source_name,
		   case
			   when uac.name like '%_Desktop_%' then 'Desktop'
			   when uac.name like '%_Mobile_%' then 'Mobile'
			   else 'not_defined' end                                       as device_type,
		   coalesce(sum(uac.cnt_ring), 0)                                   as call_cnt,
		   coalesce(sum(uac.cnt_apply), 0)                                  as apply_cnt,
		   coalesce(sum(uac.cnt_profile), 0)                                as profile_cnt,
		   coalesce(sum(uac.val_apply / to_usd ::numeric), 0)               as apply_revenue_usd,
		   coalesce(sum(uac.val_profile / to_usd ::numeric), 0)             as profile_revenue_usd,
		   coalesce(sum(uac.val_ring / to_usd ::numeric), 0)                as call_revenue_usd
	from imp_statistic.UA_conversions uac
			 join imp_statistic.currency_source cs
				  on cs.currency = 'COP'
					  and cs.date = uac.date
	where uac.date between '2021-08-01' and current_date-1
	group by 1, 2, 3, to_usd;







-- рахую витрати з google ads
-- cost перевела в USD
	create or replace view traffic.v_paid_traffic_cost as
	select a.day                                                     as date,
		   substr(a.campaign, position('_cpc' in a.campaign) + 1, 4) as traffic_source_name,
		   case
			   when a.campaign like '%_Desktop_%' then 'Desktop'
			   when a.campaign like '%_Mobile_%' then 'Mobile'
			   else 'not_defined' end                                as is_phone,
		   sum(impressions)                                          as impressions_cnt,
		   sum(clicks)                                                  clicks_cnt,
		   sum(cost) / cs.to_usd :: numeric                          as cost_usd,
		   sum(cost)                                                 as cost_cop

	from imp_statistic.m_adwords a
			 join imp_statistic.currency_source cs
				  on cs.currency = 'COP'
					  and a.day = cs.date

	where a.campaign like 'UA%'
	  and campaign not like '%Employer%'
	  and a.day between '2021-08-01' and current_date - 1
	group by 1,2,3, to_usd;





-- рахую витрати / прибуток з google ads + internal data
	create or replace view traffic.v_paid_traffic_revenue_profit as
	with google_ads_revenue as
			 (select date,
					 sum(apply_revenue_usd + profile_revenue_usd + call_revenue_usd) as google_ads_revenue_usd
			  from traffic.v_paid_traffic_google_ads_revenue
			  group by date),

		 cost as
			 (select date,
					 sum(cost_usd) as cost_usd
			  from traffic.v_paid_traffic_cost
			  group by date
			 ),
		 real_revenue as
			 (
				 select date(fn_get_timestamp_from_date_diff(date_diff))                as date,
						sum(apply_revenue_usd + profile_revenue_usd + call_revenue_usd) as revenue_usd
				 from traffic.v_traffic_revenue_by_feature_usd
				 where session_traffic_source_group_id = 2
				 group by 1
			 ),
		 real_paid_revenue as
			 (
				 select date(fn_get_timestamp_from_date_diff(date_diff)) as date,
						sum(apply_paid_packet_revenue_usd + profile_paid_packet_revenue_usd +
							call_paid_packet_revenue_usd)                    as revenue_paid_packet_usd
				 from traffic.v_traffic_revenue_by_feature_usd
				 where session_traffic_source_group_id = 2
				 group by 1
			 )

	select google_ads_revenue.date,
		   google_ads_revenue.google_ads_revenue_usd,
		   real_revenue.revenue_usd,
		   real_paid_revenue.revenue_paid_packet_usd,
		   cost.cost_usd

	from google_ads_revenue
			 join cost using (date)
		join real_revenue using (date)
		join real_paid_revenue using (date);


end
$$;

alter procedure traffic.insert_paid_traffic_dashboard_2() owner to yiv;

