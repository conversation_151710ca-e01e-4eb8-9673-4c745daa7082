create view freshdesk_external.v_parent_dictionaries(table_name, key, value) as
select
    'agents'::text                         as table_name,
    agents.id_freshdesk::character varying as key,
    agents.id::character varying           as value
from
    freshdesk_external.agents
union all
select
    'groups'::text                         as table_name,
    groups.id_freshdesk::character varying as key,
    groups.id::character varying           as value
from
    freshdesk_external.groups
union all
select
    'statuses'::text               as table_name,
    statuses.id::character varying as key,
    statuses.name                  as value
from
    freshdesk_external.statuses
union all
select
    'channels'::text               as table_name,
    channels.id::character varying as key,
    channels.name                  as value
from
    freshdesk_external.channels
union all
select
    'priorities'::text               as table_name,
    priorities.id::character varying as key,
    priorities.name                  as value
from
    freshdesk_external.priorities
union all
select
    'client_types'::text               as table_name,
    client_types.name                  as key,
    client_types.id::character varying as value
from
    freshdesk_external.client_types
union all
select
    'ticket_types'::text               as table_name,
    ticket_types.name                  as key,
    ticket_types.id::character varying as value
from
    freshdesk_external.ticket_types
union all
select
    'job_complaint_actions'::text               as table_name,
    job_complaint_actions.name                  as key,
    job_complaint_actions.id::character varying as value
from
    freshdesk_external.job_complaint_actions;