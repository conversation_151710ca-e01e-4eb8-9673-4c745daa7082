create view v_user_retention
            (country_id, blue_collar_score, user_blue_collar_type, activation_datediff, is_address_used,
             is_profile_apply_used, is_profile_created, is_add_quest_used, user_retained_1day_cnt,
             user_retained_1_7days_cnt, user_retained_1_28days_cnt, user_retained_29_98days_cnt, user_cnt)
as
SELECT s.country_id,
       s.score                   AS blue_collar_score,
       CASE
           WHEN s.score >= 0::double precision AND s.score <= 0.34::double precision THEN 'white collars'::text
           WHEN s.score >= 0.35::double precision AND s.score <= 0.64::double precision THEN 'white-blue collars'::text
           WHEN s.score >= 0.65::double precision AND s.score <= 1::double precision THEN 'blue collars'::text
           ELSE 'undefined'::text
           END                   AS user_blue_collar_type,
       s.activation_datediff,
       CASE
           WHEN u_address. usage_first_datetime <= s.activation_datetime THEN 1
           ELSE 0
           END                   AS is_address_used,
       CASE
           WHEN u_pa. usage_first_datediff = s.activation_datediff THEN 1
           ELSE 0
           END                   AS is_profile_apply_used,
       CASE
           WHEN u_pc. usage_first_datediff <= s.activation_datediff THEN 1
           ELSE 0
           END                   AS is_profile_created,
       CASE
           WHEN u_add_q. usage_first_datediff = s.activation_datediff THEN 1
           ELSE 0
           END                   AS is_add_quest_used,
       count(DISTINCT
             CASE
                 WHEN s.is_retained_1day = 1 THEN s.user_id
                 ELSE NULL::character varying
                 END)            AS user_retained_1day_cnt,
       count(DISTINCT
             CASE
                 WHEN s.is_retained_1_7days = 1 THEN s.user_id
                 ELSE NULL::character varying
                 END)            AS user_retained_1_7days_cnt,
       count(DISTINCT
             CASE
                 WHEN s.is_retained_1_28days = 1 THEN s.user_id
                 ELSE NULL::character varying
                 END)            AS user_retained_1_28days_cnt,
       count(DISTINCT
             CASE
                 WHEN s.is_retained_29_98days = 1 THEN s.user_id
                 ELSE NULL::character varying
                 END)            AS user_retained_29_98days_cnt,
       count(DISTINCT s.user_id) AS user_cnt
FROM archive. user_retention s
LEFT JOIN archive. user_address_show_on_map u_address
          ON u_address. user_id::text = s.user_id::text AND u_address. user_type_id = s.user_type_id AND
             u_address. country_id = s.country_id
LEFT JOIN archive. user_additional_questions u_add_q
          ON u_add_q. user_id::text = s.user_id::text AND u_add_q. user_type_id = s.user_type_id AND
             u_add_q. country_id = s.country_id
LEFT JOIN job_seeker. user_profile_apply u_pa
          ON u_pa. user_id::text = s.user_id::text AND u_pa. user_type_id = s.user_type_id AND
             u_pa. country_id = s.country_id
LEFT JOIN archive. user_profile_created u_pc
          ON u_pc. user_id::text = s.user_id::text AND u_pc. user_type_id = s.user_type_id AND
             u_pc. country_id = s.country_id AND u_pc. usage_cnt = 1
GROUP BY s.score, s.activation_datediff,
         (
             CASE
                 WHEN u_address. usage_first_datetime <= s.activation_datetime THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN u_add_q. usage_first_datediff = s.activation_datediff THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN u_pc. usage_first_datediff <= s.activation_datediff THEN 1
                 ELSE 0
                 END),
         (
             CASE
                 WHEN u_pa. usage_first_datediff = s.activation_datediff THEN 1
                 ELSE 0
                 END), s.country_id,
         (
             CASE
                 WHEN s.score >= 0::double precision AND s.score <= 0.34::double precision THEN 'white collars'::text
                 WHEN s.score >= 0.35::double precision AND s.score <= 0.64::double precision THEN 'white-blue collars'::text
                 WHEN s.score >= 0.65::double precision AND s.score <= 1::double precision THEN 'blue collars'::text
                 ELSE 'undefined'::text
                 END);

alter table v_user_retention
    owner to rlu;
