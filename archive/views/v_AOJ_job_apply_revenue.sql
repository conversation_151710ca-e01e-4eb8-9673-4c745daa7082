create view archive."v_AOJ_job_apply_revenue"
            (id_project, project_name, country_code, country_name, job_datediff, session_date, job_type_job, job_cnt,
             unique_job_cnt, job_type_apply, job_view_cnt, apply_cnt, new_cv_cnt, apply_click_cnt, project_id, alpha_2,
             away_revenue)
as
WITH vpjta AS (SELECT pjta.id_project,
                      c.alpha_2                AS country_code,
                      c.name_country_eng       AS country_name,
                      pjta.job_datediff,
                      pjta.job_type,
                      ip.name                  AS project_name,
                      sum(pjta.job_cnt)        AS job_cnt,
                      sum(pjta.unique_job_cnt) AS unique_job_cnt
               FROM aggregation.project_job_type_agg pjta
               LEFT JOIN dimension.countries c ON c.id = pjta.country_id
               LEFT JOIN dimension.info_project ip ON ip.country = pjta.country_id AND ip.id = pjta.id_project
               WHERE pjta.job_datediff >= (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 90)
               GROUP BY pjta.id_project, c.alpha_2, pjta.job_datediff, pjta.job_type, c.name_country_eng, ip.name),
     vpjaa AS (SELECT pjaa.id_project,
                      c.alpha_2                 AS country_code,
                      pjaa.job_view_datediff,
                      pjaa.job_type,
                      c.name_country_eng        AS country_name,
                      ip.name                   AS project_name,
                      sum(pjaa.apply_cnt)       AS apply_cnt,
                      sum(pjaa.apply_click_cnt) AS apply_click_cnt,
                      sum(pjaa.job_view_cnt)    AS job_view_cnt,
                      sum(pjaa.new_cv_cnt)      AS new_cv_cnt
               FROM aggregation.project_job_apply_agg pjaa
               LEFT JOIN dimension.countries c ON c.id = pjaa.country_id
               LEFT JOIN dimension.info_project ip ON ip.country = pjaa.country_id AND ip.id = pjaa.id_project
               LEFT JOIN dimension.u_traffic_source uts
                         ON pjaa.country_id = uts.country AND pjaa.current_traf_source_id = uts.id
               WHERE pjaa.job_view_datediff >= (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 90)
               GROUP BY pjaa.id_project, c.alpha_2, pjaa.job_view_datediff, pjaa.job_type, c.name_country_eng, ip.name),
     con AS (SELECT con_d.project_id,
                    con_d.session_date,
                    dc.alpha_2,
                    sum(con_d.away_revenue) AS away_revenue
             FROM aggregation.project_conversions_daily con_d
             LEFT JOIN (SELECT dc_1.id,
                               dc_1.alpha_2
                        FROM dimension.countries dc_1) dc ON con_d.country_id = dc.id
             WHERE con_d.session_date >= '2023-02-01'::date
             GROUP BY con_d.project_id, con_d.session_date, dc.alpha_2)
SELECT vpjta.id_project,
       vpjta.project_name,
       vpjta.country_code,
       vpjta.country_name,
       vpjta.job_datediff,
       con.session_date,
       vpjta.job_type AS job_type_job,
       vpjta.job_cnt,
       vpjta.unique_job_cnt,
       vpjaa.job_type AS job_type_apply,
       vpjaa.job_view_cnt,
       vpjaa.apply_cnt,
       vpjaa.new_cv_cnt,
       vpjaa.apply_click_cnt,
       con.project_id,
       con.alpha_2,
       con.away_revenue
FROM vpjta
LEFT JOIN vpjaa ON vpjta.id_project = vpjaa.id_project AND vpjta.country_code::text = vpjaa.country_code::text AND
                   vpjta.job_datediff = vpjaa.job_view_datediff AND vpjta.job_type = vpjaa.job_type AND
                   vpjta.country_name::text = vpjaa.country_name::text AND vpjta.project_name::text = vpjaa.project_name::text
LEFT JOIN con ON vpjta.id_project = con.project_id AND vpjta.country_code::text = con.alpha_2::text AND
                 (vpjta.job_datediff + '1900-01-01'::date) = con.session_date;

alter table archive."v_AOJ_job_apply_revenue"
    owner to atsy;

grant select on archive."v_AOJ_job_apply_revenue" to readonly;

grant select on archive."v_AOJ_job_apply_revenue" to math;

