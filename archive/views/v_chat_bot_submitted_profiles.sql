create view v_chat_bot_submitted_profiles (date_diff, submitted_profiles_with_chat_bot_cnt, submitted_profiles_cnt) as
WITH profiles_submitted_daily AS (SELECT p. date_submitted     AS date_diff,
                                         count(DISTINCT p. id) AS profile_submitted_cnt
                                  FROM imp. profiles p
                                  WHERE p. country = 1
                                    AND p. is_submitted = true
                                  GROUP BY p. date_submitted),
     profile_submitted AS (SELECT profiles_submitted_daily. date_diff,
                                  profiles_submitted_daily. profile_submitted_cnt,
                                  sum(profiles_submitted_daily. profile_submitted_cnt)
                                  OVER (ORDER BY profiles_submitted_daily. date_diff ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS submitted_profiles_cnt
                           FROM profiles_submitted_daily
                           ORDER BY profiles_submitted_daily. date_diff),
     profile_submitted_with_bot AS (SELECT cb. date_diff,
                                           count(DISTINCT
                                                 CASE
                                                     WHEN cb. is_deleted_bot = 0 THEN p. id
                                                     ELSE NULL::integer
                                                     END) AS submitted_profiles_with_chat_bot_cnt
                                    FROM archive. chat_bot_user_info cb
                                    JOIN imp. profiles p ON concat('+', cb. phone) = p. phone::text
                                    WHERE p. country = 1
                                      AND p. is_submitted = true
                                      AND fn_get_date_diff(p. date_is_submitted) <= cb. date_diff
                                    GROUP BY cb. date_diff)
SELECT psb. date_diff,
       psb. submitted_profiles_with_chat_bot_cnt,
       ps. submitted_profiles_cnt
FROM profile_submitted_with_bot psb
JOIN profile_submitted ps USING (date_diff);

alter table v_chat_bot_submitted_profiles
    owner to postgres;
