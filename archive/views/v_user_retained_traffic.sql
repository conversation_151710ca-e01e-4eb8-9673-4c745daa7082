create view v_user_retained_traffic
            (id, blue_collar_score, user_type_id, user_id, activation_datediff, traf_source_id, is_paid_traf,
             retention_day_name, apply_cnt, traf_source)
as
SELECT urt. id,
       urt. blue_collar_score,
       urt. user_type_id,
       urt. user_id,
       urt. activation_datediff,
       urt. traf_source_id,
       urt. is_paid_traf,
       urt. retention_day_name,
       urt. apply_cnt,
       if.name AS traf_source
FROM archive. user_retained_traffic urt
JOIN product. info_traffic_source if ON if.id = urt. traf_source_id AND if.country::text = 'UA'::text;

alter table v_user_retained_traffic
    owner to dap;
