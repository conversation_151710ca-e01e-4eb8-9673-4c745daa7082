create view archive.v_agg_abandoned_profile_viber_messages
            (date_diff, date_sent_message, message_sent_daily_cnt, message_delivered_daily_cnt, has_link_visit_cnt,
             has_submit_profile_cnt)
as
SELECT DISTINCT v_abandoned_profile_viber_messages.date_diff,
                v_abandoned_profile_viber_messages.date_sent_message,
                count(v_abandoned_profile_viber_messages.profile_id)
                OVER (PARTITION BY v_abandoned_profile_viber_messages.date_diff, v_abandoned_profile_viber_messages.country) AS message_sent_daily_cnt,
                sum(
                CASE
                    WHEN v_abandoned_profile_viber_messages.status = ANY (ARRAY [3, 4, 5]) THEN 1
                    ELSE 0
                    END)
                OVER (PARTITION BY v_abandoned_profile_viber_messages.date_diff, v_abandoned_profile_viber_messages.country) AS message_delivered_daily_cnt,
                sum(v_abandoned_profile_viber_messages.has_link_visit)
                OVER (PARTITION BY v_abandoned_profile_viber_messages.date_diff, v_abandoned_profile_viber_messages.country) AS has_link_visit_cnt,
                sum(v_abandoned_profile_viber_messages.has_submit_profile)
                OVER (PARTITION BY v_abandoned_profile_viber_messages.date_diff, v_abandoned_profile_viber_messages.country) AS has_submit_profile_cnt
FROM archive.v_abandoned_profile_viber_messages;

alter table archive.v_agg_abandoned_profile_viber_messages
    owner to nsh;

grant select on archive.v_agg_abandoned_profile_viber_messages to readonly;

grant select on archive.v_agg_abandoned_profile_viber_messages to math;

grant select on archive.v_agg_abandoned_profile_viber_messages to writeonly_pyscripts;

grant select on archive.v_agg_abandoned_profile_viber_messages to "pavlo.kvasnii";

