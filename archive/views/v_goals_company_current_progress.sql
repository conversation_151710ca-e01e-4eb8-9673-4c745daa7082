create view v_goals_company_current_progress
            (goal_name, dt_month, progress_daily, goal_daily, goal_monthly, days_passed, days_in_months) as
SELECT a.goal_name,
       max(a.dt_month)      AS dt_month,
       CASE
           WHEN a.goal_name = 'Смысл'::text THEN exp(sum(ln(a.progress_daily + 1::double precision))) -
                                                 1::double precision
           ELSE sum(a.progress_daily)
           END              AS progress_daily,
       CASE
           WHEN a.goal_name = 'Смысл'::text THEN exp(sum(ln(power(a.goal_monthly + 1::double precision,
                                                                  a.days_passed::numeric::double precision /
                                                                  a.days_in_month::numeric::double precision)))) -
                                                 1::double precision
           ELSE sum(a.goal_monthly * a.days_passed::numeric::double precision /
                    a.days_in_month::numeric::double precision)
           END              AS goal_daily,
       CASE
           WHEN a.goal_name = 'Смысл'::text THEN exp(sum(ln(a.goal_monthly + 1::double precision))) -
                                                 1::double precision
           ELSE sum(a.goal_monthly)
           END              AS goal_monthly,
       sum(a.days_passed)   AS days_passed,
       sum(a.days_in_month) AS days_in_months
FROM (SELECT v_goals_bp_plan_fact.dt_month,
             sum(
                     CASE
                         WHEN v_goals_bp_plan_fact.plan_fact_type = 'progress'::text
                             THEN v_goals_bp_plan_fact.business_points
                         ELSE 0::numeric
                         END)      AS progress_daily,
             sum(
                     CASE
                         WHEN v_goals_bp_plan_fact.plan_fact_type = 'goal'::text
                             THEN v_goals_bp_plan_fact.business_points
                         ELSE 0::numeric
                         END)      AS goal_monthly,
             sum(
                     CASE
                         WHEN v_goals_bp_plan_fact.plan_fact_type = 'goal'::text THEN days_in_month_passed(
                                 v_goals_bp_plan_fact.dt_month::date, (CURRENT_DATE - '1 day'::interval)::date)
                         ELSE NULL::integer
                         END)      AS days_passed,
             sum(
                     CASE
                         WHEN v_goals_bp_plan_fact.plan_fact_type = 'goal'::text
                             THEN days_in_month(v_goals_bp_plan_fact.dt_month::date)
                         ELSE NULL::integer
                         END)      AS days_in_month,
             'Трансформация'::text AS goal_name
      FROM company.v_goals_bp_plan_fact
      WHERE v_goals_bp_plan_fact.dt_month <= CURRENT_DATE
      GROUP BY v_goals_bp_plan_fact.dt_month
      UNION
      SELECT v_goals_itc_plan_fact.dt_month,
             sum(
                     CASE
                         WHEN v_goals_itc_plan_fact.plan_fact_type = 'progress'::text
                             THEN v_goals_itc_plan_fact.ab_growth - 1::numeric::double precision
                         ELSE 0::numeric::double precision
                         END) AS progress_daily,
             sum(
                     CASE
                         WHEN v_goals_itc_plan_fact.plan_fact_type = 'goal'::text
                             THEN v_goals_itc_plan_fact.ab_growth - 1::numeric::double precision
                         ELSE 0::numeric::double precision
                         END) AS goal_monthly,
             sum(
                     CASE
                         WHEN v_goals_itc_plan_fact.plan_fact_type = 'goal'::text THEN days_in_month_passed(
                                 v_goals_itc_plan_fact.dt_month::date, (CURRENT_DATE - '1 day'::interval)::date)
                         ELSE NULL::integer
                         END) AS days_passed,
             sum(
                     CASE
                         WHEN v_goals_itc_plan_fact.plan_fact_type = 'goal'::text
                             THEN days_in_month(v_goals_itc_plan_fact.dt_month::date)
                         ELSE NULL::integer
                         END) AS days_in_month,
             'Смысл'::text    AS goal_name
      FROM company.v_goals_itc_plan_fact
      WHERE v_goals_itc_plan_fact.dt_month <= CURRENT_DATE
      GROUP BY v_goals_itc_plan_fact.dt_month
      UNION
      SELECT v_goals_revenue_dte_plan_fact.dt_month,
             sum(
                     CASE
                         WHEN v_goals_revenue_dte_plan_fact.plan_fact_type = 'progress'::text
                             THEN v_goals_revenue_dte_plan_fact.revenue
                         ELSE 0::numeric
                         END) AS progress_daily,
             sum(
                     CASE
                         WHEN v_goals_revenue_dte_plan_fact.plan_fact_type = 'goal'::text
                             THEN v_goals_revenue_dte_plan_fact.revenue
                         ELSE 0::numeric
                         END) AS goal_monthly,
             sum(
                     CASE
                         WHEN v_goals_revenue_dte_plan_fact.plan_fact_type = 'goal'::text THEN days_in_month_passed(
                                 v_goals_revenue_dte_plan_fact.dt_month::date, (CURRENT_DATE - '1 day'::interval)::date)
                         ELSE NULL::integer
                         END) AS days_passed,
             sum(
                     CASE
                         WHEN v_goals_revenue_dte_plan_fact.plan_fact_type = 'goal'::text
                             THEN days_in_month(v_goals_revenue_dte_plan_fact.dt_month::date)
                         ELSE NULL::integer
                         END) AS days_in_month,
             'Вызов'::text    AS goal_name
      FROM company.v_goals_revenue_dte_plan_fact
      WHERE v_goals_revenue_dte_plan_fact.dt_month <= CURRENT_DATE
      GROUP BY v_goals_revenue_dte_plan_fact.dt_month
      UNION
      SELECT v_goals_revenue_non_dte_plan_fact.dt_month,
             sum(
                     CASE
                         WHEN v_goals_revenue_non_dte_plan_fact.plan_fact_type = 'progress'::text
                             THEN v_goals_revenue_non_dte_plan_fact.revenue
                         ELSE 0::numeric
                         END) AS progress_daily,
             sum(
                     CASE
                         WHEN v_goals_revenue_non_dte_plan_fact.plan_fact_type = 'goal'::text
                             THEN v_goals_revenue_non_dte_plan_fact.revenue
                         ELSE 0::numeric
                         END) AS goal_monthly,
             sum(
                     CASE
                         WHEN v_goals_revenue_non_dte_plan_fact.plan_fact_type = 'goal'::text THEN days_in_month_passed(
                                 v_goals_revenue_non_dte_plan_fact.dt_month::date,
                                 (CURRENT_DATE - '1 day'::interval)::date)
                         ELSE NULL::integer
                         END) AS days_passed,
             sum(
                     CASE
                         WHEN v_goals_revenue_non_dte_plan_fact.plan_fact_type = 'goal'::text
                             THEN days_in_month(v_goals_revenue_non_dte_plan_fact.dt_month::date)
                         ELSE NULL::integer
                         END) AS days_in_month,
             'Здоровье'::text AS goal_name
      FROM company.v_goals_revenue_non_dte_plan_fact
      WHERE v_goals_revenue_non_dte_plan_fact.dt_month <= CURRENT_DATE
      GROUP BY v_goals_revenue_non_dte_plan_fact.dt_month
      UNION
      SELECT v_goals_traffic_plan_fact.dt_month,
             sum(
                     CASE
                         WHEN v_goals_traffic_plan_fact.plan_fact_type = 'progress'::text
                             THEN v_goals_traffic_plan_fact.sessions::numeric
                         ELSE 0::numeric
                         END)  AS progress_daily,
             sum(
                     CASE
                         WHEN v_goals_traffic_plan_fact.plan_fact_type = 'goal'::text
                             THEN v_goals_traffic_plan_fact.sessions::numeric
                         ELSE 0::numeric
                         END)  AS goal_monthly,
             sum(
                     CASE
                         WHEN v_goals_traffic_plan_fact.plan_fact_type = 'goal'::text THEN days_in_month_passed(
                                 v_goals_traffic_plan_fact.dt_month::date, (CURRENT_DATE - '1 day'::interval)::date)
                         ELSE NULL::integer
                         END)  AS days_passed,
             sum(
                     CASE
                         WHEN v_goals_traffic_plan_fact.plan_fact_type = 'goal'::text
                             THEN days_in_month(v_goals_traffic_plan_fact.dt_month::date)
                         ELSE NULL::integer
                         END)  AS days_in_month,
             'Экспансия'::text AS goal_name
      FROM company.v_goals_traffic_plan_fact
      WHERE v_goals_traffic_plan_fact.dt_month <= CURRENT_DATE
      GROUP BY v_goals_traffic_plan_fact.dt_month) a
GROUP BY a.goal_name;

alter table v_goals_company_current_progress
    owner to dap;

grant select on v_goals_company_current_progress to npo;

grant select on v_goals_company_current_progress to readonly;

grant select on v_goals_company_current_progress to writeonly_product;

grant select on v_goals_company_current_progress to ksha;

grant select on v_goals_company_current_progress to math;

grant select on v_goals_company_current_progress to writeonly_pyscripts;

grant select on v_goals_company_current_progress to "pavlo.kvasnii";

