create view v_product_performance_ab_itc
            (test, countries, team_name, test_type, date_launch, growth_bp, growth_itc, task_link) as
SELECT i_ab_test. test,
       string_agg(
               CASE
                   WHEN res. metric::text = 'intention_to_contact_per_user'::text THEN res. country
                   ELSE NULL::character varying
                   END::text, ','::text ORDER BY res. country)                                      AS countries,
       (SELECT ipt. name
        FROM product. info_product_teams ipt
        WHERE ipt. id = i_ab_test. team_id)                                                          AS team_name,
       i_ab_test. test_type,
       max(i_ab_test. date_launch)                                                                  AS date_launch,
       1::double precision + COALESCE(sum(
                                              CASE
                                                  WHEN res. metric::text = 'EA_Business_Metric_2020_per_user'::text THEN
                                                      CASE
                                                          WHEN res. b_ci_lower > res. a_ci_upper OR res. b_ci_upper < res. a_ci_lower
                                                              THEN (res. b_mean / res. a_mean - 1::double precision) * ats. scalability_bp
                                                          ELSE 0::double precision
                                                          END
                                                  ELSE NULL::double precision
                                                  END), 0::double precision) / 8::double precision AS growth_bp,
       1::double precision + COALESCE(sum(
                                              CASE
                                                  WHEN res. metric::text = 'intention_to_contact_per_user'::text THEN
                                                      CASE
                                                          WHEN res. b_ci_lower > res. a_ci_upper OR res. b_ci_upper < res. a_ci_lower
                                                              THEN (res. b_mean / res. a_mean - 1::double precision) * ats. scalability_itc
                                                          ELSE 0::double precision
                                                          END
                                                  ELSE NULL::double precision
                                                  END), 0::double precision) / 8::double precision AS growth_itc,
       i_ab_test. task_link
FROM product. info_ab_tests i_ab_test
LEFT JOIN product. ab_tests_scalability ats ON ats. test_id = i_ab_test. test AND ats. test_type::text = i_ab_test. test_type::text
LEFT JOIN product. ab_tests_results res ON res. test = ats. test_id AND res. test_type::text = ats. test_type::text AND
                                          res. country::text = ats. country::text AND
                                          "substring"(res. groups_vs::text, length(res. groups_vs::text) - 1,
                                                      length(res. groups_vs::text))::integer = ats. group_in_prod AND
                                          res. type_test::text = 'AB'::text AND ((res. country::text = ANY
                                                                                 (ARRAY ['DE'::character varying::text, 'UK'::character varying::text, 'FR'::character varying::text, 'CA'::character varying::text, 'ES'::character varying::text, 'ID'::character varying::text, 'RU'::character varying::text, 'PL'::character varying::text])) AND
                                                                                i_ab_test. date_launch <=
                                                                                '2020-03-31'::date OR
                                                                                (res. country::text = ANY
                                                                                 (ARRAY ['DE'::character varying::text, 'UK'::character varying::text, 'FR'::character varying::text, 'CA'::character varying::text, 'US'::character varying::text, 'ID'::character varying::text, 'RU'::character varying::text, 'PL'::character varying::text])) AND
                                                                                i_ab_test. date_launch >
                                                                                '2020-03-31'::date)
WHERE i_ab_test. is_in_prod = true
  AND i_ab_test. exclude_from_year_goal = 0
  AND (i_ab_test. team_id = ANY (ARRAY [1, 2, 3, 7]))
  AND res. dt_calc = ((SELECT max(dcalc. dt_calc) AS max
                      FROM product. ab_tests_results dcalc
                      WHERE res. test = dcalc. test
                        AND res. test_type::text = dcalc. test_type::text
                        AND res. groups_vs::text = dcalc. groups_vs::text
                        AND res. country::text = dcalc. country::text))
GROUP BY i_ab_test. test, i_ab_test. team_id, i_ab_test. test_type, i_ab_test. task_link
UNION
SELECT v_ab_test_results_ds. test,
       string_agg(v_ab_test_results_ds. country::text, ','::text
                  ORDER BY (v_ab_test_results_ds. country::text))    AS countries,
       v_ab_test_results_ds. team_name,
       v_ab_test_results_ds. test_type,
       v_ab_test_results_ds. date_launch,
       sum(
               CASE
                   WHEN v_ab_test_results_ds. bp_growth_significance = true
                       THEN v_ab_test_results_ds. bp_growth * v_ab_test_results_ds. bp_scalability
                   ELSE 0::double precision
                   END) / 8::double precision + 1::double precision AS growth_bp,
       sum(
               CASE
                   WHEN v_ab_test_results_ds. itc_growth_significance = true
                       THEN v_ab_test_results_ds. itc_growth * v_ab_test_results_ds. itc_scalability
                   ELSE 0::double precision
                   END) / 8::double precision + 1::double precision AS growth_itc,
       NULL::character varying(300)                                 AS task_link
FROM product. v_ab_test_results_ds
WHERE ((v_ab_test_results_ds. country::text = ANY
        (ARRAY ['DE'::character varying::text::character varying, 'UK'::character varying::text::character varying, 'FR'::character varying::text::character varying, 'CA'::character varying::text::character varying, 'ES'::character varying::text::character varying, 'ID'::character varying::text::character varying, 'RU'::character varying::text::character varying, 'PL'::character varying::text::character varying]::text[])) AND
       v_ab_test_results_ds. date_launch <= '2020-03-31'::date OR (v_ab_test_results_ds. country::text = ANY
                                                                  (ARRAY ['DE'::character varying::text::character varying, 'UK'::character varying::text::character varying, 'FR'::character varying::text::character varying, 'CA'::character varying::text::character varying, 'US'::character varying::text::character varying, 'ID'::character varying::text::character varying, 'RU'::character varying::text::character varying, 'PL'::character varying::text::character varying]::text[])) AND
                                                                 v_ab_test_results_ds. date_launch > '2020-03-31'::date)
  AND v_ab_test_results_ds. is_in_prod = true
GROUP BY v_ab_test_results_ds. test, v_ab_test_results_ds. team_name, v_ab_test_results_ds. test_type,
         v_ab_test_results_ds. date_launch;

alter table v_product_performance_ab_itc
    owner to postgres;

grant delete, insert, references, select, trigger, truncate, update on v_product_performance_ab_itc to dap;
