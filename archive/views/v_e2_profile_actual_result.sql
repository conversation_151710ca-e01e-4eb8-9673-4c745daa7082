create view v_e2_profile_actual_result(profession_name, day_to_call, last_step, has_job, profile_cnt) as
SELECT p. profession_name,
       p. day_to_call,
       e2.last_step,
       e2.has_job,
       count(*) AS profile_cnt
FROM archive. e2_call_result e2
JOIN archive. e2_profile p ON p. phone::text = ('+'::text || e2.phone::character varying::text)
GROUP BY p. profession_name, p. day_to_call, e2.has_job, e2.last_step;

alter table v_e2_profile_actual_result
    owner to dap;
