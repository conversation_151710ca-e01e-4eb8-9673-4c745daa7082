create or replace procedure affiliate.p_insert_account_metrics_agg()
    language plpgsql
as
$$
BEGIN

    truncate affiliate.account_metrics_agg;

    insert into affiliate.account_metrics_agg(country_id, action_datediff, partner, metric_name, metric_value)
    select ab.country_id,
           ab.action_datediff,
           tsf.name             as partner,
           ab.metric_name,
           sum(ab.metric_value) as metric_value
    from aggregation.session_abtest_agg ab
             join dimension.u_traffic_source tsf on tsf.country = ab.country_id and tsf.id = ab.traffic_source_id
    where ab.metric_name in ('new_account_cnt', 'new_alert_cnt', 'new_verified_account_cnt')
      and ab.action_datediff >= fn_get_date_diff('2021-10-01'::date)
      and tsf.channel = 'Affiliate'
    group by ab.country_id,
             ab.action_datediff,
             tsf.name,
             ab.metric_name;

end;
$$;

alter procedure affiliate.p_insert_account_metrics_agg() owner to yiv;
