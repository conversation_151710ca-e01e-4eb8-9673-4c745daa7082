declare @date_diff int = ${dt_begin},
        @country_id int = ${country_id},
        @country_code varchar(2) = lower(substring(db_name(), 5, 2));
;

begin
        select st.groups                               as groups,
               s.id                                    as id_session,
               s.date_diff                             as date_diff,
               case
                   when s.flags & 2 = 2 then 1
                   else 0
                   end                                 as is_returned,
               case
                   when s.flags & 64 = 64 then 2 /*mobile app*/
                   when s.flags & 16 = 16 then 1 /*mobile web*/
                   else 0                        /*desktop web*/
                   end                                 as device_type_id,
               case
                   when s.ip_cc = @country_code
                       or s.ip_cc = 'gb' and @country_code = 'uk'
                       then 1
                   else 0 end                          as is_local,
               coalesce(s.session_create_page_type, 0) as session_create_page_type,
               s.id_traf_source                        as id_traffic_source,
               s.id_current_traf_source                as id_current_traffic_source,
               s.cookie_label                          as cookie_label,
               s.start_date
        into #test_sessions
        from dbo.session s with (nolock)
        left join dbo.session_test_agg st with (nolock)on st.date_diff = s.date_diff and s.id = st.id_session
        where s.flags & 1 = 0 and s.date_diff = @date_diff

       select s1.click_price_usd,
              s1.id_session,
              s1.groups,
              s1.date_diff,
              s1.is_returned,
              s1.device_type_id,
              s1.is_local,
              s1.session_create_page_type,
              s1.id_traffic_source,
              case
              when s1.letter_type = 8 then 1
              when s1.id_recommend is not null then 2
              when s1.id_alertview is not null then 3
              when s1.id_search is not null then 4
              when s1.id_external is not null then 5
              else 6
              end                                                                             placement,
              case when s1.date_diff >= s1.conversion_start then s1.click_price_usd else 0 end as conv_click_price_usd
       into #revenue_raw
       from (select s.groups,
              s.date_diff,
              s.is_returned,
              s.device_type_id,
              s.is_local,
              s.session_create_page_type,
              s.id_traffic_source,
              s.id_session,
              sa.click_price * ic.value_to_usd          click_price_usd,
              isnull(sa.letter_type, sj.letter_type)    letter_type,
              isnull(sc.id_recommend, scj.id_recommend) id_recommend,
              isnull(sc.id_alertview, scj.id_alertview) id_alertview,
              isnull(sc.id_search, scj.id_search)       id_search,
              ext.id                                    id_external,
              cs.conversion_start
       from dbo.session_away sa (nolock)
                     inner join #test_sessions s (nolock) on sa.date_diff = s.date_diff and sa.id_session = s.id_session
                     inner join dbo.info_currency ic (nolock) on ic.id = sa.id_currency
                     left join auction.campaign ac (nolock) on ac.id = sa.id_campaign
                     left join auction.site ast (nolock) on ac.id_site = ast.id
                     left join auction.[user] au (nolock) on au.id = ast.id_user
                     left join dbo.session_click sc (nolock) on sc.date_diff = sa.date_diff and sc.id = sa.id_click
                     left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
                     left join dbo.session_click scj (nolock) on scj.date_diff = sj.date_diff and scj.id = sj.id_click
                     left join dbo.session_external ext (nolock) on ext.date_diff = sa.date_diff and ext.id_away = sa.id
                     left join (
              select id_project,
                     min(cac.date_diff) as conversion_start
              from auction.conversion_away_connection cac with (nolock)
                     join dbo.session_away sa with (nolock)
                            on cac.date_diff = sa.date_diff
                                   and cac.id_session_away = sa.id
              group by id_project
       ) cs
                            on sa.id_project = cs.id_project
       where sa.date_diff = @date_diff
              and (sa.id_campaign = 0 or au.flags & 2 = 0)
              and isnull(sa.flags, 0) & 2 = 0
              and sa.flags & 512 = 0
       union all
       select s.groups,
              s.date_diff,
              s.is_returned,
              s.device_type_id,
              s.is_local,
              s.session_create_page_type,
              s.id_traffic_source,
              s.id_session,
              sc.click_price * ic.value_to_usd       click_price_usd,
              isnull(sa.letter_type, sj.letter_type) letter_type,
              sc.id_recommend,
              sc.id_alertview,
              sc.id_search,
              ext.id                                 id_external,
              cs.conversion_start
       from dbo.session_click sc (nolock)
                     inner join #test_sessions s (nolock) on sc.date_diff = s.date_diff and sc.id_session = s.id_session
                     inner join dbo.info_currency ic (nolock) on ic.id = sc.id_currency
                     left join auction.campaign ac (nolock) on ac.id = sc.id_campaign
                     left join auction.site ast (nolock) on ac.id_site = ast.id
                     left join auction.[user] au (nolock) on au.id = ast.id_user
                     left join dbo.session_away sa (nolock) on sc.date_diff = sa.date_diff and sc.id = sa.id_click
                     left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
                     left join dbo.session_external ext (nolock)
                            on ext.date_diff = sc.date_diff and (ext.id_away = sa.id or ext.id_jdp = sj.id)
                     left join (
              select id_project,
                     min(cac.date_diff) as conversion_start
              from auction.conversion_away_connection cac with (nolock)
                     join dbo.session_away sa with (nolock)
                            on cac.date_diff = sa.date_diff
                                   and cac.id_session_away = sa.id
              group by id_project
       ) cs
                            on sa.id_project = cs.id_project
       where sc.date_diff = @date_diff
              and (sc.id_campaign = 0 or au.flags & 2 = 2)
              and isnull(sc.flags, 0) & 16 = 0
              and sc.flags & 4096 = 0
       union all
       select s.groups,
              s.date_diff,
              s.is_returned,
              s.device_type_id,
              s.is_local,
              s.session_create_page_type,
              s.id_traffic_source,
              s.id_session,
              scns.click_price * ic.value_to_usd click_price_usd,
              scns.letter_type,
              scns.id_recommend,
              null   as                          id_alertview,
              null   as                          id_search,
              ext.id as                          id_external,
              cs.conversion_start
       from dbo.session_click_no_serp scns (nolock)
                     inner join #test_sessions s (nolock) on scns.date_diff = s.date_diff and scns.id_session = s.id_session
                     inner join dbo.info_currency ic (nolock) on ic.id = scns.id_currency
                     left join auction.campaign ac (nolock) on ac.id = scns.id_campaign
                     left join auction.site ast (nolock) on ac.id_site = ast.id
                     left join auction.[user] au (nolock) on au.id = ast.id_user
                     left join dbo.session_away sa (nolock) on scns.date_diff = sa.date_diff and scns.id = sa.id_click_no_serp
                     left join dbo.session_jdp sj (nolock) on scns.date_diff = sj.date_diff and scns.id = sj.id_click_no_serp
                     left join dbo.session_external ext (nolock)
                            on ext.date_diff = scns.date_diff and (ext.id_away = sa.id or ext.id_jdp = sj.id)
                     left join (
              select id_project,
                     min(cac.date_diff) as conversion_start
              from auction.conversion_away_connection cac with (nolock)
                     join dbo.session_away sa with (nolock)
                            on cac.date_diff = sa.date_diff
                                   and cac.id_session_away = sa.id
              group by id_project
       ) cs
                            on scns.id_project = cs.id_project
       where (scns.click_flags & 256 > 0 or scns.click_flags & 512 > 0)
              and scns.date_diff = @date_diff
              and au.flags & 2 = 2
              and isnull(scns.flags, 0) & 16 = 0
              and scns.flags & 4096 = 0
       ) s1

        select ts.id_session,
               sum(rr.click_price_usd) as revenue_usd,
               sum(rr.conv_click_price_usd) as conv_click_price_usd
        into #revenue_session
        from #test_sessions ts
        left join #revenue_raw rr on rr.date_diff = ts.date_diff and rr.id_session = ts.id_session
        group by ts.id_session


        select id_session,
               revenue_usd,
               conv_click_price_usd,
               row_number() over (order by revenue_usd desc) rank,
               count(id_session) over () as                  cnt
        into #revenue_session_ranked
        from #revenue_session revenue_session
        where revenue_usd > 0

        select t.id_session,
               groups,
               t.date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               cookie_label,
               start_date,
               case
                   when rank <= round(cnt * 0.05, 0) and r.revenue_usd > 0 then 1
                   else 0
                   end as is_anomalistic,
               coalesce(revenue_usd,0) as revenue_usd,
               coalesce(conv_click_price_usd,0) as conv_click_price_usd
        into #session_info
        from  #test_sessions t
        left join #revenue_session_ranked r
        on r.id_session = t.id_session


        select s.id_session,
               s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.cookie_label,
               s.is_anomalistic,
               sac.id_account,
               new_acc.id                                                                                                     as new_account_id,
               row_number()
                       over (partition by new_acc.id order by s.start_date - new_acc.date_add)                                as new_account_rank,
               account_contact.verify_date,
               new_alert.id                                                                                                   as new_alert_id,
               new_alert.id_account                                                                                           as new_alert_account_id,
               row_number()
                       over (partition by new_alert.id_account, new_alert.id order by new_alert.date_add - s.start_date)      as new_alert_rank
        into #session_acc_info
        from #session_info s
        left join dbo.session_account sac with (nolock) on sac.date_diff = s.date_diff and sac.id_session = s.id_session
        left join dbo.account new_acc with (nolock) on s.date_diff = datediff(day, 0, new_acc.date_add) and new_acc.id = sac.id_account and s.start_date <= new_acc.date_add
        left join dbo.account_contact with (nolock) on account_contact.id_account = new_acc.id
        left join dbo.email_alert new_alert with (nolock) on datediff(day, '1900-01-01', new_alert.date_add) = s.date_diff and sac.id_account = new_alert.id_account and s.start_date <= new_alert.date_add

        select groups,
               session_acc_info.date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               is_anomalistic,
               count(distinct id_session)                                                                  as session_cnt,
               count(distinct cookie_label)                                                                as user_cnt,
               count(distinct id_session)                                                                  as session_account_cnt,
               count(distinct iif(new_account_rank = 1, new_account_id, null))                             as new_account_cnt,
               count(distinct iif(new_account_rank = 1 and verify_date is not null, new_account_id,
                                  null))                                                                   as new_verified_account_cnt,
               count(distinct iif(new_alert_rank = 1, new_alert_id, null))                                 as new_alert_cnt,
               count(distinct iif(new_alert_rank = 1, new_alert_account_id, null))                         as new_alert_account_cnt,
               count(distinct
                     iif(new_alert_rank = 1 and new_alert_id is not null, id_session, null))               as new_alert_session_cnt,
               sum(distinct iif(new_account_rank = 1, account_revenue.total_revenue, null))                as new_account_revenue
        into #session_acc_agg
        from #session_acc_info session_acc_info
        left join dbo.account_revenue with(nolock)
        on session_acc_info.new_account_id = account_revenue.id_account
        and account_revenue.date_diff = @date_diff
        group by groups,
                 session_acc_info.date_diff,
                 is_returned,
                 device_type_id,
                 is_local,
                 session_create_page_type,
                 id_traffic_source,
                 id_current_traffic_source,
                 is_anomalistic


        select date_diff,
               id,
               id_session
        into #session_search
        from dbo.session_search ss with (nolock)
        where ss.date_diff = @date_diff

        select date_diff,
               id,
               id_session
        into #session_alertview
        from dbo.session_alertview with (nolock)
        where date_diff = @date_diff


        select date_diff,
               id,
               id_search_prev,
               action,
               id_search
        into #session_filter_action
        from dbo.session_filter_action sfa with (nolock)
        where date_diff = @date_diff


        select date_diff,
               id,
               id_session,
               type
        into #session_action
        from dbo.session_action sact with (nolock)
        where date_diff = @date_diff;


        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct ss.id)                                           as search_cnt,
               count(distinct sav.id)                                          as alertview_cnt,
               count(distinct iif(sfa.action is not null, sfa.id, null))       as session_filter_action_cnt,
               count(distinct ss.id_session)                                   as search_session_cnt,
               count(distinct sav.id_session)                                  as alertview_session_cnt,
               count(distinct iif(sfa.action is not null, s.id_session, null)) as session_filter_action_session_cnt
        into #top_actions_agg
        from #session_info s
        left join #session_search ss with (nolock) on ss.date_diff = s.date_diff and ss.id_session = s.id_session
        left join #session_alertview sav with (nolock) on sav.date_diff = s.date_diff and sav.id_session = s.id_session
        left join #session_filter_action sfa with (nolock) on sfa.date_diff = ss.date_diff and sfa.id_search_prev = ss.id
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic


        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               cast(floor(sact.type / 100.0) as varchar) as action_type,
               count(distinct sact.id)                   as session_action_cnt,
               count(distinct s.id_session)              as session_action_session_cnt
        into #session_action_agg
        from #session_info s
        join #session_action sact with (nolock) on sact.date_diff = s.date_diff and sact.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic,
                 floor(sact.type / 100.0)
        union
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               cast(-1 as int)              as action_type,
               count(distinct sact.id)      as session_action_cnt,
               count(distinct s.id_session) as session_action_session_cnt
        from #session_info s
        join #session_action sact with (nolock) on sact.date_diff = s.date_diff and sact.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic


        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sfa.id_search) as session_filter_search_cnt,
               count(distinct s.id_session)  as session_filter_search_session_cnt
        into #filter_search_agg
        from #session_filter_action sfa with (nolock)
        join dbo.session_search ss_prev with (nolock) on ss_prev.date_diff = sfa.date_diff and ss_prev.id = sfa.id_search_prev
        join #session_info s_prev with (nolock) on s_prev.date_diff = ss_prev.date_diff and s_prev.id_session = ss_prev.id_session
        join dbo.session_search ss_curr with (nolock) on ss_curr.date_diff = sfa.date_diff and ss_curr.id = sfa.id_search
        join #session_info s with (nolock) on s.date_diff = ss_curr.date_diff and s.id_session = ss_curr.id_session and s_prev.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic

        --drop table #jdp_metrics_agg
        --declare @date_diff int = datediff(day, '1900-01-01', getdate() - 1);
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sj.id)                                       as jdp_cnt,
               count(distinct case when sj.flags & 4 = 4 then sj.id end )  as apply_jdp_cnt,
               count(distinct sj.id_session)                               as jdp_session_cnt,
               count(distinct sja.id)                                      as session_jdp_action_cnt,
               count(distinct s.id_session)                                as session_jdp_action_session_cnt,
               count(distinct sap.id)                                      as apply_cnt,
               count(distinct iif(sap.id is not null, s.id_session, null)) as apply_session_cnt
        into #jdp_metrics_agg
        from dbo.session_jdp sj with (nolock)
        join #session_info s with (nolock) on s.date_diff = sj.date_diff and s.id_session = sj.id_session
        left join dbo.session_jdp_action sja with (nolock) on sj.date_diff = sja.date_diff and sj.id = sja.id_jdp
        left join dbo.session_apply sap with (nolock) on sja.date_diff = sap.date_diff and sja.id = sap.id_src_jdp_action
        where sj.date_diff = @date_diff
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic

        --select * from #session_info -- drop table #away_agg
        --declare @date_diff int = datediff(day, '1900-01-01', getdate() - 1);
        -- AWAYS BY PLACEMENT
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sa.id)                                                 as away_cnt,
               count(distinct sa.id_session)                                         as away_session_cnt,
               count(distinct sj.id)                                                 as jdp_away_cnt,
               count(distinct sj.id_session)                                         as jdp_away_session_cnt,
               count(distinct away_con.id_session_away)                              as conversion_cnt,
               count(distinct
                     iif(away_con.id_session_away is not null, sa.id_session, null)) as conversion_session_cnt,
               case
                   when isnull(sa.letter_type, sj.letter_type) = 8
                       then 1
                   when isnull(sc.id_recommend, scj.id_recommend) is not null
                       then 2
                   when isnull(sc.id_alertview, scj.id_alertview) is not null
                       then 3
                   when isnull(sc.id_search, scj.id_search) is not null
                       then 4
                   when ext.id is not null
                       then 5
                   else 6
                   end                                                               as placement,
                   count(distinct case when sa.date_diff> cs.conversion_start then sa.id end )  as conversion_away_cnt
        into #away_agg
        from dbo.session_away sa with (nolock)
        join #session_info s with (nolock) on s.date_diff = sa.date_diff and s.id_session = sa.id_session
        left join dbo.session_click sc (nolock) on sc.date_diff = sa.date_diff and sc.id = sa.id_click
        left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
        left join dbo.session_click scj (nolock) on scj.date_diff = sj.date_diff and scj.id = sj.id_click
        left join dbo.session_external ext (nolock) on ext.date_diff = sa.date_diff and ext.id_away = sa.id
        left join (select distinct id_session_away from auction.conversion_away_connection with (nolock)) away_con on away_con.id_session_away = sa.id
        left join (
               select  id_project,
                       min(cac.date_diff) as conversion_start
               from auction.conversion_away_connection cac with (nolock)
                     join dbo.session_away sa with (nolock)
                on cac.date_diff  = sa.date_diff
                and cac.id_session_away = sa.id
                group by id_project
               ) cs
              on sa.id_project = cs.id_project
        where sa.date_diff = @date_diff
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic,
                 case
                     when isnull(sa.letter_type, sj.letter_type) = 8
                         then 1
                     when isnull(sc.id_recommend, scj.id_recommend) is not null
                         then 2
                     when isnull(sc.id_alertview, scj.id_alertview) is not null
                         then 3
                     when isnull(sc.id_search, scj.id_search) is not null
                         then 4
                     when ext.id is not null
                         then 5
                     else 6
                     end
        union
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sa.id)                                                 as away_cnt,
               count(distinct sa.id_session)                                         as away_session_cnt,
               count(distinct iif(sj.id is not null, sa.id, null))                   as jdp_away_cnt,
               count(distinct iif(sj.id is not null, sa.id_session, null))           as jdp_away_session_cnt,
               count(distinct away_con.id_session_away)                              as conversion_cnt,
               count(distinct
                     iif(away_con.id_session_away is not null, sa.id_session, null)) as conversion_session_cnt,
               7                                                                     as placement,
               count(distinct case when sa.date_diff> cs.conversion_start then sa.id end )  as conversion_away_cnt
        from dbo.session_away sa with (nolock)
        join #session_info s with (nolock) on s.date_diff = sa.date_diff and s.id_session = sa.id_session
        left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
        left join (select distinct id_session_away from auction.conversion_away_connection with (nolock)) away_con on away_con.id_session_away = sa.id
        left join (
               select  id_project,
                       min(cac.date_diff) as conversion_start
               from auction.conversion_away_connection cac with (nolock)
                     join dbo.session_away sa with (nolock)
                on cac.date_diff  = sa.date_diff
                and cac.id_session_away = sa.id
                group by id_project
               ) cs
              on sa.id_project = cs.id_project
        where sa.date_diff = @date_diff
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic

        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               r.placement,
               s.is_anomalistic,
               sum(r.click_price_usd)                                         as revenue_usd,
               count(distinct iif(r.click_price_usd > 0, r.id_session, null)) as session_with_revenue_count,
               sum(r.conv_click_price_usd) as conv_revenue_usd
        into #revenue
        from #revenue_raw r
        join #session_info s on s.id_session = r.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 r.placement,
                 s.is_anomalistic
        union
        select groups,
               date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               7                                                      as placement,
               is_anomalistic,
               sum(revenue_usd)                                       as revenue_usd,
               count(distinct iif(revenue_usd > 0, id_session, null)) as session_with_revenue_count,
               sum(conv_click_price_usd) as conv_revenue_usd
        from #session_info
        group by groups,
                 date_diff,
                 is_returned,
                 device_type_id,
                 is_local,
                 session_create_page_type,
                 id_traffic_source,
                 id_current_traffic_source,
                 is_anomalistic;


--intention-to-contact table

        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sc.id)*
               (select itc_per_view
                from dwh.postgres.company.m_itc_jdp_away_projects_for_ab as m_all_away
                where lower(m_all_away.country) = @country_code
               ) as intention_to_contact_from_serp_cnt,
	   count(distinct case when sja.type in (1/*respond*/,13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/, 34/*view_multiply_employer_phone*/ )  then sjd.id end) as intention_to_contact_from_jdp_cnt
into #intention_to_contact
from #session_info s with(nolock)
left join  session_click sc  with (nolock)
	   on sc.job_destination = 1 /*away*/ and
	      sc.id_session = s.id_session
	     and sc.date_diff  = s.date_diff
left join session_jdp sjd with(nolock)
	   on sjd.date_diff = s.date_diff
      and sjd.id_session = s.id_session

left join session_jdp_action sja with(nolock)
	   on sjd.date_diff = sja.date_diff
	  and sjd.id = sja.id_jdp
	  and sja.type in (1/*respond*/,
					  13 /*click_to_call*/,
					  19/*show_phone_number*/,
					  21/*view_employer_phone*/,
					  34/*view_multiply_employer_phone*/
					  )
where  s.date_diff = @date_diff
group by   s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic

;


        with final as (
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null          as attribute_name,
                        null          as attribute_value,
                        'session_cnt' as metric,
                        session_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                  as attribute_name,
                        null                  as attribute_value,
                        'session_account_cnt' as metric,
                        session_account_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null              as attribute_name,
                        null              as attribute_value,
                        'new_account_cnt' as metric,
                        new_account_cnt   as value
                 from #session_acc_agg session_acc_agg
                  union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null              as attribute_name,
                        null              as attribute_value,
                        'new_account_revenue' as metric,
                        new_account_revenue   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                       as attribute_name,
                        null                       as attribute_value,
                        'new_verified_account_cnt' as metric,
                        new_verified_account_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null            as attribute_name,
                        null            as attribute_value,
                        'new_alert_cnt' as metric,
                        new_alert_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                    as attribute_name,
                        null                    as attribute_value,
                        'new_alert_session_cnt' as metric,
                        new_alert_session_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                    as attribute_name,
                        null                    as attribute_value,
                        'new_alert_account_cnt' as metric,
                        new_alert_account_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null       as attribute_name,
                        null       as attribute_value,
                        'user_cnt' as metric,
                        user_cnt   as value
                 from #session_acc_agg session_acc_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null         as attribute_name,
                        null         as attribute_value,
                        'search_cnt' as metric,
                        search_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                 as attribute_name,
                        null                 as attribute_value,
                        'search_session_cnt' as metric,
                        search_session_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null            as attribute_name,
                        null            as attribute_value,
                        'alertview_cnt' as metric,
                        alertview_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                    as attribute_name,
                        null                    as attribute_value,
                        'alertview_session_cnt' as metric,
                        alertview_session_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'action type'        as attribute_name,
                        action_type          as attribute_value,
                        'session_action_cnt' as metric,
                        session_action_cnt   as value
                 from #session_action_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'action type'                as attribute_name,
                        action_type                  as attribute_value,
                        'session_action_session_cnt' as metric,
                        session_action_session_cnt   as value
                 from #session_action_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                        as attribute_name,
                        null                        as attribute_value,
                        'session_filter_action_cnt' as metric,
                        session_filter_action_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                                as attribute_name,
                        null                                as attribute_value,
                        'session_filter_action_session_cnt' as metric,
                        session_filter_action_session_cnt   as value
                 from #top_actions_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                        as attribute_name,
                        null                        as attribute_value,
                        'session_filter_search_cnt' as metric,
                        session_filter_search_cnt   as value
                 from #filter_search_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                                as attribute_name,
                        null                                as attribute_value,
                        'session_filter_search_session_cnt' as metric,
                        session_filter_search_session_cnt   as value
                 from #filter_search_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null      as attribute_name,
                        null      as attribute_value,
                        'jdp_cnt' as metric,
                        jdp_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null      as attribute_name,
                        null      as attribute_value,
                        'apply_jdp_cnt' as metric,
                        apply_jdp_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null              as attribute_name,
                        null              as attribute_value,
                        'jdp_session_cnt' as metric,
                        jdp_session_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null        as attribute_name,
                        null        as attribute_value,
                        'apply_cnt' as metric,
                        apply_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                as attribute_name,
                        null                as attribute_value,
                        'apply_session_cnt' as metric,
                        apply_session_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                     as attribute_name,
                        null                     as attribute_value,
                        'session_jdp_action_cnt' as metric,
                        session_jdp_action_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                             as attribute_name,
                        null                             as attribute_value,
                        'session_jdp_action_session_cnt' as metric,
                        session_jdp_action_session_cnt   as value
                 from #jdp_metrics_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement' as attribute_name,
                        placement   as attribute_value,
                        'away_cnt'  as metric,
                        away_cnt    as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'        as attribute_name,
                        placement          as attribute_value,
                        'away_session_cnt' as metric,
                        away_session_cnt   as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'    as attribute_name,
                        placement      as attribute_value,
                        'jdp_away_cnt' as metric,
                        jdp_away_cnt   as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'            as attribute_name,
                        placement              as attribute_value,
                        'jdp_away_session_cnt' as metric,
                        jdp_away_session_cnt   as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'      as attribute_name,
                        placement        as attribute_value,
                        'conversion_cnt' as metric,
                        conversion_cnt   as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'              as attribute_name,
                        placement                as attribute_value,
                        'conversion_session_cnt' as metric,
                        conversion_session_cnt   as value
                 from #away_agg
                     union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'              as attribute_name,
                        placement                as attribute_value,
                        'conversion_away_cnt' as metric,
                        conversion_away_cnt   as value
                 from #away_agg
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'                as attribute_name,
                        placement                  as attribute_value,
                        'session_revenue_cnt'      as metric,
                        session_with_revenue_count as value
                 from #revenue
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement' as attribute_name,
                        placement   as attribute_value,
                        'revenue'   as metric,
                        revenue_usd as value
                 from #revenue
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        'placement'                as attribute_name,
                        placement                  as attribute_value,
                        'conv_revenue_usd'      as metric,
                        conv_revenue_usd as value
                 from #revenue
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                     as attribute_name,
                        null                     as attribute_value,
                        'intention_to_contact_from_jdp_cnt' as metric,
                        intention_to_contact_from_jdp_cnt   as value
                 from #intention_to_contact
                 union
                 select groups,
                        date_diff,
                        is_returned,
                        device_type_id,
                        is_local,
                        session_create_page_type,
                        id_traffic_source,
                        id_current_traffic_source,
                        is_anomalistic,
                        null                     as attribute_name,
                        null                     as attribute_value,
                        'intention_to_contact_from_serp_cnt' as metric,
                        intention_to_contact_from_serp_cnt   as value
                 from #intention_to_contact

                 )

        select  @country_id as country_id,
               groups,
               date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               is_anomalistic,
               attribute_name,
               attribute_value,
               metric,
               value
        from final
        where value != 0

        drop table #test_sessions;
        drop table #session_search;
        drop table #revenue_raw;
        drop table #revenue_session;
        drop table #revenue_session_ranked;
        drop table #session_info;
        drop table #session_acc_info;
        drop table #session_acc_agg;
        drop table #session_alertview;
        drop table #session_filter_action;
        drop table #session_action;
        drop table #top_actions_agg;
        drop table #session_action_agg;
        drop table #filter_search_agg;
        drop table #jdp_metrics_agg;
        drop table #away_agg;
        drop table #revenue;
        drop table #intention_to_contact;

end

