select es.date_diff as sent_datediff,
       es.country as country_id,
       es.id_message as message_id,
       sum(coalesce(away.click_price, 0) * info_currency.value_to_eur) as open_away_revenue,
       count(distinct away.id) as away_open_cnt
from imp.email_sent es
inner join imp.session_alertview_message sam on sam.id_message = es.id_message and sam.country = es.country
inner join imp.session_away away on away.id_session = sam.id_session and away.country = sam.country
left join dimension.info_currency on info_currency.id = away.id_currency and away.country = info_currency.country

where es.country <= 11 and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by es.date_diff,
         es.country,
         es.id_message
union all
select es.date_diff as sent_datediff,
       es.country as country_id,
       es.id_message as message_id,
       sum(coalesce(away.click_price, 0) * info_currency.value_to_eur) as open_away_revenue,
       count(distinct away.id) as open_away_cnt
from imp.email_sent es
inner join imp.session_click_message scm on scm.id_message = es.id_message and scm.country = es.country
inner join imp.session_away away on away.id_session = scm.id_session and away.country = scm.country
left join dimension.info_currency on info_currency.id = away.id_currency and away.country = info_currency.country
where es.country <= 11 and es.letter_type in (12,15) and es.date_diff between ${DT_NOW} - 2 and ${DT_NOW} - 1
group by es.date_diff,
         es.country,
         es.id_message
