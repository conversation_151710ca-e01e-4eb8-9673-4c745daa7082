SET NOCOUNT ON;

DECLARE @datediff_start int = :to_sqlcode_date_or_datediff_start,
        @datediff_end int = :to_sqlcode_date_or_datediff_end;

SELECT c.id_project,
       au.id                                                               AS id_user,
       ac.name                                                             AS campaign_name,
       c.id_campaign,
       CONVERT(VARCHAR(10), DATEADD(DAY, s.date_diff, '1900-01-01'), 120)  AS date,
       SUM(IIF(destination = 0, 1, 0))                                     AS jdp_bot,
       SUM(IIF(destination = 1, 1, 0))                                     AS away_bot,
       MAX(IIF(jh.id_category IS NOT NULL, jh.id_category, j.id_category)) AS child_category_id
FROM dbo.session s (NOLOCK)
         INNER JOIN
     (SELECT sc.id_job,
             sc.uid_job,
             sc.date_diff,
             sc.id_project,
             sc.id_campaign,
             sc.id_session,
             IIF(sc.job_destination = 1, 1, 0) AS destination
      FROM dbo.session_click sc (NOLOCK)

      UNION ALL

      SELECT ns.id_job,
             ns.uid_job,
             ns.date_diff,
             ns.id_project,
             ns.id_campaign,
             ns.id_session,
             IIF(ns.job_destination = 1, 1, 0) AS destination
      FROM dbo.session_click_no_serp ns (NOLOCK)) c ON s.date_diff = c.date_diff AND s.id = c.id_session
         LEFT JOIN dbo.job_history jh (NOLOCK) ON c.uid_job = jh.uid
         LEFT JOIN dbo.job j (NOLOCK) ON c.id_job = j.id
         INNER JOIN auction.campaign ac (NOLOCK) ON c.id_campaign = ac.id
         INNER JOIN auction.site asi (NOLOCK) ON ac.id_site = asi.id -- and asi.site_status = 0
         INNER JOIN auction.[user] au (NOLOCK) ON asi.id_user = au.id -- and au.flags & 32 <> 32 and au.flags & 8 = 8
         INNER JOIN dbo.info_project ip (NOLOCK) ON c.id_project = ip.id -- and ip.is_active = 1
WHERE s.date_diff BETWEEN @datediff_start AND @datediff_end
  AND s.flags & 1 = 1
GROUP BY c.id_project,
         au.id,
         ac.name,
         c.id_campaign,
         CONVERT(VARCHAR(10), DATEADD(DAY, s.date_diff, '1900-01-01'), 120)
;
