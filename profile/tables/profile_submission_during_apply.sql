-- insert procedure - traffic.insert_paid_traffic_dashboard_1

select country_id,
       employer_apply_id,
       job_seeker_apply_id,
       apply_datediff,
       apply_date    as apply_datetime,
       profile_id,
       submission_datediff,
       sa.uid_job,
       sj.id         as jdp_id,
       sj.id_session as session_id
-- profile submission during apply
from profile.job_seeker_apply_without_profile awp
join imp.session_apply sa on awp.country_id = sa.country and awp.job_seeker_apply_id = sa.id and awp.apply_datediff = sa.date_diff
join imp.session_jdp_action sja on sja.country = sa.country and sja.date_diff = sa.date_diff and sja.id = sa.id_src_jdp_action
join imp.session_jdp sj on sj.country = sja.country and sj.date_diff = sja.date_diff and sj.id = sja.id_jdp and sj.job_id_project = -1
where awp.country_id = 1 and sa.date_diff = _datediff;