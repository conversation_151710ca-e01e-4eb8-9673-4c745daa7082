create table profile.traffic_profile_profession_arpu_28_days
(
	country_id integer,
	profile_id integer,
	profession_id integer,
	profession_name varchar(2048),
	parrent_profession_id integer,
	parrent_profession_name varchar(2048),
	submission_datediff integer,
	session_traffic_source_id integer,
	session_traffic_source_group_id integer,
	profile_base_open_contact_price numeric,
	profile_base_open_contact_jcoin_cnt bigint,
	apply_open_contact_price numeric,
	apply_open_contact_jcoin_cnt bigint,
	digital_recruiter_open_contact_price numeric,
	digital_recruiter_open_contact_jcoin_cnt bigint,
	call_open_contact_price numeric,
	call_open_contact_jcoin_cnt bigint
);

alter table profile.traffic_profile_profession_arpu_28_days owner to postgres;

grant select on profile.traffic_profile_profession_arpu_28_days to readonly;

