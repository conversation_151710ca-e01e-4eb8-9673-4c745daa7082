select pjr.country_id,
       pjr.profile_id,
       pjr.jdp_viewed_datediff,
       sj.uid_job as job_uid,
       sj.date as jdp_viewed_datetime,
       1 as feature_id,
       1 as order_action_id,
       1 as action_type_id,
       vps.last_profile_id
from profile.profile_jdp_respond pjr
join imp.session_jdp sj on sj.country = pjr.country_id and sj.id = pjr.jdp_id and sj.date_diff = pjr.jdp_viewed_datediff
join profile.profile_submitted_with_double vps on vps.country_id = pjr.country_id and vps.profile_id = pjr.profile_id and pjr.jdp_viewed_datediff >= vps.submission_datediff
where is_call_click = 1 and jdp_flag & 1 = 1 and pjr.jdp_viewed_datediff = ${DT_NOW} - 1;
