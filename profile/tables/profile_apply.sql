select sa.country          as country_id,
       ja.id               as employer_apply_id,
       ja.id_session_apply as job_seeker_apply_id,
       sa.date_diff        as apply_datediff,
       sa.apply_date       as apply_datetime,
       pa.profile_id       as profile_id,
       j.id_account        as employer_account_id,
       sa.uid_job as job_uid
from imp.session_apply sa
         join profile.v_profile_account pa on pa.country_id = sa.country and pa.account_id = sa.id_account
         join imp_employer.job_apply ja
              on ja.sources = 1 and ja.dd_session_apply = sa.date_diff and ja.id_session_apply = sa.id
         join imp_employer.job_apply_profile jap on jap.sources = ja.sources and jap.id_apply = ja.id
         join imp_employer.job j on j.id = ja.id_job and j.sources = ja.sources
where sa.country in (1, 10) and sa.date_diff = ${DT_NOW} - 1;
