select distinct sub.country_id,
       sub.employer_apply_id,
       sub.job_seeker_apply_id,
       sub.apply_datediff,
       sub.apply_datetime,
       sub.profile_id,
       sub.employer_account_id,
       sub.job_uid,
       sub.registration_source_id,
       sub.first_apply_datetime,
       sub.feature_id,
       1 as order_action_id,
       2 as action_type_id
from (  select pa.country_id,
               pa.employer_apply_id,
               pa.job_seeker_apply_id,
               pa.apply_datediff,
               pa.apply_datetime,
               pa.profile_id,
               pa.employer_account_id,
               pa.job_uid,
               min(pa.apply_datetime) over (partition by pa.country_id,pa.profile_id) as first_apply_datetime,
               (select prs.registration_source_id
                from profile.profile_registration_source prs
                where prs.country_id = pa.country_id and prs.profile_id = pa.profile_id) as registration_source_id,
               1 as feature_id
         from profile.profile_apply pa
         join profile.profile_submitted_with_double ps on ps.country_id = pa.country_id and ps.profile_id = pa.profile_id and pa.apply_datediff >= ps.submission_datediff
     ) sub
left join profile.job_seeker_apply_without_profile w on w.country_id = sub.country_id and w.job_seeker_apply_id = sub.job_seeker_apply_id
                                                            and w.apply_datediff = sub.apply_datediff and w.profile_id = sub.profile_id
where w.job_seeker_apply_id is null;
