select country_id,
       profile_id,
       employer_account_id,
       1 as platform_user_type_id,
       order_action_id,
       action_datediff,
       sum(action_to_contact_prob) as action_to_contact_prob
from profile.job_seeker_action_to_contact js_atc
where action_datediff = ${DT_NOW} - 1
group by country_id,
         profile_id,
         action_datediff,
         order_action_id,
         employer_account_id
union
select country_id,
       profile_id,
       employer_account_id,
       2 as platform_user_type_id,
       order_action_id,
       action_datediff,
       sum(action_to_contact_prob) as action_to_contact_prob
from employer.employer_action_to_contact
where action_datediff = ${DT_NOW} - 1
group by country_id,
       profile_id,
       order_action_id,
       action_datediff,
       employer_account_id
;
