with profile_alive_levels as (
select ${DT_NOW} - 1  as report_datediff,
       vpca.profile_id,
       max(case when vpca.creation_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 then 1 else 0 end) as is_created_last_7_days,
       max(case when s.date_diff > vpca.creation_datediff then 1 else 0 end) as is_session_after_creation_last_7_days,
       max(case when vpca.activation_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 then 1 else 0 end) as is_activated_last_7_days,
       max(case when s.date_diff > vpca.activation_datediff then 1 else 0 end) as is_session_after_activation_last_7_days,
       max(case when vpca.last_active_datediff between ${DT_NOW} - 7 and ${DT_NOW} - 1 and last_active_datediff > activation_datediff then 1 else 0 end) as is_returned_apply_last_7_days
from dap.v_profile_creation_activation vpca
left join imp.profile_cookie_labels pcl
     on pcl.country = vpca.country_id
    and pcl.id_profile = vpca.profile_id
left join imp.session s
      on s.cookie_label = pcl.cookie_labels
    and s.country = pcl.country
    and s.date_diff between ${DT_NOW} - 7 and ${DT_NOW} - 1
    and s.date_diff > vpca.creation_datediff
where creation_datediff between 44034 and ${DT_NOW} - 1
group by vpca.profile_id)
select report_datediff,
       count(distinct profile_id) as profile_cnt,
       count(distinct case when is_created_last_7_days + is_session_after_creation_last_7_days + is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days > 0 then profile_id end) as alive_profile_cnt,
       count(distinct case when is_created_last_7_days = 1 and is_session_after_creation_last_7_days + is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_created_profile_cnt,
       count(distinct case when is_session_after_creation_last_7_days =1 and is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_returned_profile_cnt,
       count(distinct case when is_activated_last_7_days = 1 and is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_activated_profile_cnt,
       count(distinct case when is_session_after_activation_last_7_days = 1 and is_returned_apply_last_7_days =0 then profile_id end) as only_activated_and_returned_profile_cnt,
       count(distinct case when is_returned_apply_last_7_days =1 then profile_id end) as apply_returned_profile_cnt
       from profile_alive_levels pal
group by report_datediff;