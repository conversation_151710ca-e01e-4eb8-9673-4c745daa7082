create temp table profile_value_action as
select atc_s.country_id,
       action_datediff,
       action_datetime,
       atc_s.profile_id,
       employer_account_id,
       platform_user_type_id,
       feature_id                as feature_type_id,
       action_type_id,
       1 /*'action to contact'*/ as value_type,
       action_to_contact_prob    as value
from profile.v_action_to_contact_structure atc_s
     join profile.profile_submitted_with_double ps
     on atc_s.country_id = ps.country_id
         and atc_s.profile_id = ps.profile_id
         and action_datetime >= coalesce(ps.submission_datetime, fn_get_timestamp_from_date_diff(submission_datediff))

union

select ps.country_id,
       action_datediff,
       action_datetime,
       poc.profile_id,
       j.id_account  as employer_account_id,
       2             as platform_user_type_id, /*employer*/
       feature_type_id,
       9             as action_type_id,
       2 /*'jcoin'*/ as value_type,
       1::numeric    as value
from employer.v_profile_open_contact_with_packet poc
     join profile.profile_submitted_with_double ps
     on poc.database_source_id = 1
         and ps.country_id = 1
         and poc.profile_id = ps.profile_id
         and
        poc.action_datetime >= coalesce(ps.submission_datetime, fn_get_timestamp_from_date_diff(submission_datediff))
     join imp_employer.job_apply ja
     on poc.database_source_id = ja.sources
         and poc.apply_id = ja.id
     join imp_employer.job j
     on j.sources = ja.sources
         and j.id = ja.id_job
where database_source_id = 1;


alter table profile_value_action
    add primary key (country_id, profile_id, employer_account_id, action_datetime, platform_user_type_id,
                     feature_type_id,
                     action_type_id, value_type);


create table profile.profile_value_with_status as
with
    a as (
        select v.country_id,
               v.profile_id,
               v.employer_account_id,
               v.action_datediff,
               v.action_datetime,
               v.platform_user_type_id,
               v.feature_type_id,
               v.value_type,
               sum(v.value)          as value,
               max(ast.status_flags) as status_flags
        from profile_value_action v
             left join profile.profile_activity_status ast
             on v.profile_id = ast.profile_id
                 and v.action_datetime >= ast.status_change_datetime
                 and v.action_datetime < coalesce(ast.next_status_change_datetime, now())
        where v.action_datediff <= fn_get_date_diff(current_date) - 1
        group by v.country_id, v.profile_id, v.employer_account_id, v.action_datediff, v.action_datetime,
                 v.platform_user_type_id,
                 v.feature_type_id, v.value_type
    )
select a.*,
       coalesce(max(ast_7_days.status_flags), a.status_flags) as next_7_days_max_status_flags
from a
     left join profile.profile_activity_status ast_7_days
     on 1 = ast_7_days.country_id
         and a.profile_id = ast_7_days.profile_id
         and ast_7_days.status_change_datetime - a.action_datetime > '0 days'::interval
         and ast_7_days.status_change_datetime - a.action_datetime <= '7 days'::interval
group by a.country_id,
         a.profile_id,
         a.employer_account_id,
         a.action_datediff,
         a.action_datetime,
         a.platform_user_type_id,
         a.feature_type_id,
         a.value_type,
         a.value,
         a.status_flags;

alter table profile.profile_value_with_status
    add primary key (country_id, profile_id, employer_account_id, action_datetime, platform_user_type_id,
                     feature_type_id,
                     value_type);