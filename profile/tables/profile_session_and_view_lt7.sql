select p.country_id                                                                            as country_id,
       p.id                                                                                    as profile_id,
       p.creation_datediff::int                                                                as creation_datediff,
       count(distinct case when s.date_diff < p.creation_datediff + 7 then s.id end)           as lt7_session_cnt,
       count(distinct case when j.jdp_viewed_datediff < p.creation_datediff + 7 then j.id end) as lt7_dte_jdp_view_cnt
from profile.v_profile_submitted p
left join imp.profile_cookie_labels pcl on pcl.country = p.country_id and pcl.id_profile = p.id
left join imp.session s on s.country = pcl.country and s.cookie_label = pcl.cookie_labels and s.date_diff::int >= p.creation_datediff
left join apply.jdp j on j.country_id = s.country and j.session_id = s.id and j.jdp_viewed_datediff = s.date_diff
where p.country_id = 1 and p.creation_datediff between ${DT_NOW} - 10 and ${DT_NOW} - 1
group by p.country_id, p.id, p.creation_datediff;