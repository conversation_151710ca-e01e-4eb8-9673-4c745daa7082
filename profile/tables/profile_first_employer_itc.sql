create table profile.profile_first_employer_itc as
    select database_source_id,
           1 as country_id,
           ps.profile_id,
           ps.submission_datetime,
           min(action_datetime) as first_itc_datetime
from employer.v_profile_open_contact_with_packet v
join profile.profile_submitted_with_double ps
 on ps.country_id = 1 and
    ps.profile_id = v.profile_id
where action_datediff is not null
group by database_source_id,
         ps.submission_datetime,
         ps.profile_id;

alter table profile.profile_first_employer_itc
	add constraint profile_first_employer_itc_pk
		primary key (country_id, database_source_id, profile_id);


grant select on profile.profile_first_employer_itc to readonly;

grant select on profile.profile_first_employer_itc to readonly_ds;
