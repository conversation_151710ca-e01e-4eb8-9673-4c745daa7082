-- call from job seeker
select pc.country_id,
       pc.profile_id,
       pc.jdp_action_datediff        as action_datediff,
       min(pc.jdp_action_datetime)   as action_datetime,
       1                          as action_type_id,
       count(distinct pc.job_uid) as action_to_contact_prob,
       1                          as feature_id,
       1                          as order_action_id,
       j.id_account               as employer_account_id
from profile.profile_call pc
join profile.profile_submitted_with_double ps
    on ps.country_id = pc.country_id and
       ps.profile_id = pc.profile_id and
       pc.jdp_action_datediff >= ps.submission_datediff
         left join imp_employer.job_to_uid_mapping jtum
                   on jtum.sources = 1
                       and jtum.uid_job = pc.job_uid
         left join imp_employer.job j
                   on j.sources = 1
                       and jtum.id_job = j.id
where pc.country_id = 1
  and pc.jdp_action_datediff >= 44407
  and pc.jdp_action_datediff = ${DT_NOW}-1
  and not exists(select 1
                 from profile.job_seeker_action_to_contact_apply atc_a
                 where atc_a.country_id = pc.country_id
                   and atc_a.profile_id = pc.profile_id
                   and atc_a.job_uid = pc.job_uid)
group by pc.country_id,
         pc.profile_id,
         pc.jdp_action_datediff,
         j.id_account

UNION ALL

--job_seeker_action_to_contact
-- call clicks from job seeker
select atc_cc.country_id,
       atc_cc.profile_id,
       atc_cc.jdp_viewed_datediff as action_datediff,
       atc_cc.jdp_viewed_datetime as action_datetime,
       atc_cc.action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = atc_cc.action_type_id)) as action_to_contact_prob,
	    atc_cc.feature_id,
        atc_cc.order_action_id,
        j.id_account as employer_account_id
from profile.job_seeker_action_to_contact_call_click  atc_cc
    join profile.profile_submitted_with_double ps
    on ps.country_id = atc_cc.country_id and
       ps.profile_id = atc_cc.profile_id and
       atc_cc.jdp_viewed_datediff >= ps.submission_datediff
left join imp_employer.job_to_uid_mapping jtum
 on jtum.sources = 1
and jtum.uid_job = atc_cc.job_uid
left join imp_employer.job j
     on j.sources = 1
    and jtum.id_job = j.id
where jdp_viewed_datediff < 44407
 and jdp_viewed_datediff = ${DT_NOW} - 1
and not exists (select 1 from profile.job_seeker_action_to_contact_apply atc_a
                where atc_a.country_id = atc_cc.country_id
                 and atc_a.profile_id = atc_cc.profile_id
                 and atc_a.job_uid = atc_cc.job_uid)
group by atc_cc.country_id,
       atc_cc.profile_id,
       atc_cc.jdp_viewed_datediff,
       atc_cc.jdp_viewed_datetime,
       atc_cc.action_type_id,
	   atc_cc.feature_id,
        atc_cc.order_action_id,
        j.id_account


union all
-- applies from job seeker
select atc_a.country_id,
       atc_a.profile_id,
       atc_a.apply_datediff as action_datediff,
       atc_a.apply_datetime as action_datetime,
       atc_a.action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = atc_a.action_type_id)) as action_to_contact_prob,
	    atc_a.feature_id,
        atc_a.order_action_id,
       atc_a.employer_account_id
from profile.job_seeker_action_to_contact_apply atc_a
where apply_datediff = ${DT_NOW} - 1
group by atc_a.country_id,
       atc_a.profile_id,
       atc_a.apply_datediff,
       atc_a.apply_datetime,
       atc_a.action_type_id,
	   atc_a.feature_id,
        atc_a.order_action_id,
       atc_a.employer_account_id,
         ps.last_profile_id

union all
-- message from job_seeker
select atc_m.country_id,
       atc_m.profile_id,
       atc_m.action_datediff as action_datediff,
       atc_m.action_datetime as action_datetime,
       atc_m.action_type_id,
       sum((select conversion
        from profile.job_seeker_action_to_contact_conversion jsatcc
        where jsatcc.intention_to_contact_type_id = action_type_id)) as action_to_contact_prob,
	    atc_m.feature_id,
        atc_m.order_action_id,
       atc_m.employer_account_id
from profile.action_to_contact_message atc_m
where action_datediff = ${DT_NOW} - 1
  and platform_user_type_id = 1 /*job_seeker*/
group by atc_m.country_id,
       atc_m.profile_id,
       atc_m.action_datediff,
       atc_m.action_datetime,
       atc_m.action_type_id,
	   atc_m.feature_id,
        atc_m.order_action_id,
         atc_m.employer_account_id
;
