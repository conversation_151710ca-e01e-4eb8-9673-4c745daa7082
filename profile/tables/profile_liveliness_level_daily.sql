select report_datediff,
       registration_source_id,
       profile_blue_collar_type_id,
       count(distinct pal.profile_id) as profile_cnt,
       count(distinct case when is_created_last_7_days + is_session_after_creation_last_7_days + is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days > 0 then profile_id end) as alive_profile_cnt,
       count(distinct case when is_created_last_7_days = 1 and is_session_after_creation_last_7_days + is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_created_profile_cnt,
       count(distinct case when is_session_after_creation_last_7_days =1 and is_activated_last_7_days + is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_returned_profile_cnt,
       count(distinct case when is_activated_last_7_days = 1 and is_session_after_activation_last_7_days + is_returned_apply_last_7_days =0 then profile_id end) as only_activated_profile_cnt,
       count(distinct case when is_session_after_activation_last_7_days = 1 and is_returned_apply_last_7_days =0 then profile_id end) as only_activated_and_returned_profile_cnt,
       count(distinct case when is_returned_apply_last_7_days =1 then profile_id end) as apply_returned_profile_cnt,
       pal.country_id,
       vps.submission_datediff,
       vps.creation_datediff
from profile.profile_liveliness_level_daily_detailed pal
left join profile.v_profile_submitted vps on pal.country_id = vps.country_id and pal.profile_id = vps.id
where report_datediff = ${DT_NOW} - 1
group by report_datediff,
         profile_blue_collar_type_id,
         registration_source_id,
         pal.country_id,
         vps.submission_datediff,
         vps.creation_datediff;