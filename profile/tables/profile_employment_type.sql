create temp table temp_profiles_employment_type as
select country,
       id,
       cast(data as jsonb) ->> 'employmentTypes' as employmentTypes
from imp.profiles
where data is not null;

insert into profile.profile_employment_type (country_id, profile_id, employment_type)
select country as country_id,
       id as profile_id,
       jsonb_array_elements_text(employmentTypes::jsonb)::int as employment_type
from temp_profiles_employment_type;
