create table profile.m_action_to_contact_per_submitted_profile as
select country_id,
       profile_id,
       seeker_cycle_first_datediff,
       action_to_contact_28_days_cnt,
       action_to_contact_call_28_days_cnt,
       action_to_contact_apply_28_days_cnt,
       action_to_contact_employer_28_days_cnt,
       action_to_contact_14_days_cnt,
       action_to_contact_call_14_days_cnt,
       action_to_contact_apply_14_days_cnt,
       action_to_contact_employer_14_days_cnt,
       action_to_contact_7_days_cnt,
       action_to_contact_call_7_days_cnt,
       action_to_contact_apply_7_days_cnt,
       action_to_contact_employer_7_days_cnt,
       action_to_contact_1_days_cnt,
       action_to_contact_call_1_days_cnt,
       action_to_contact_apply_1_days_cnt,
       action_to_contact_employer_1_days_cnt,
       action_to_contact_2_days_cnt,
       seeker_cycle_start_type_id

from profile.v_action_to_contact_per_profile
;

alter table profile.m_action_to_contact_per_submitted_profile
    add constraint m_action_to_contact_per_submitted_profile_pk
        primary key (country_id, profile_id, seeker_cycle_first_datediff)
;
