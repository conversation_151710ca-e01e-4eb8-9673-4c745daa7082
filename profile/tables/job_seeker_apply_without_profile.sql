with mix_source_data as (
    select country_id::smallint,
           submission_datediff::int,
           profile_id::int,
           apply_datediff::int,
           job_seeker_apply_id::bigint,
           employer_apply_id::bigint,
           apply_datetime::timestamp,
           1 as source_flag
    from (
             select row_number() over (partition by ps.profile_id order by apply_datetime) as apply_num,
                    pa.country_id,
                    pa.employer_apply_id,
                    pa.job_seeker_apply_id,
                    pa.apply_datediff,
                    pa.apply_datetime,
                    ps.profile_id                                                               as profile_id,
                    ps.submission_datediff
             from profile.profile_submitted_with_double ps
                      join profile.profile_apply pa on ps.country_id = pa.country_id and ps.profile_id = pa.profile_id and
                                                       pa.apply_datediff = ps.submission_datediff
                      left join imp.session_apply sa
                                on pa.country_id = sa.country and pa.apply_datediff = sa.date_diff and
                                   pa.job_seeker_apply_id = sa.id
                      left join imp.session_jdp_action sja
                                on sa.country = sja.country and sja.date_diff = sa.date_diff and
                                   sa.id_src_jdp_action = sja.id
             where ps.country_id = 1
               and ps.submission_datediff >= 44255
               and sja.flags & 64 <> 64) as d
    where apply_num = 1
    union all
    select country_id::smallint,
           submission_datediff::int,
           profile_id::int,
           apply_datediff::int,
           job_seeker_apply_id::bigint,
           employer_apply_id::bigint,
           apply_datetime::timestamp,
           2 as source_flag
    from (
             select pa.country_id,
                    ps.submission_datediff,
                    pa.profile_id,
                    pa.apply_datediff,
                    pa.job_seeker_apply_id,
                    pa.employer_apply_id,
                    pa.apply_datetime,
                    min(pa.apply_datetime)
                    over (partition by pa.country_id, pa.profile_id) as min_profile_apply_datetime
             from profile.profile_registration_source prs
                      join profile.profile_submitted_with_double ps
                           on ps.country_id = prs.country_id
                               and ps.profile_id = prs.profile_id
                      join profile.profile_apply pa
                           on pa.country_id = prs.country_id
                               and pa.profile_id = prs.profile_id
                               and pa.apply_datediff between ps.submission_datediff - 1 and ps.submission_datediff + 1
             where prs.registration_source_id = 24
         ) a
    where min_profile_apply_datetime = apply_datetime
)
select country_id, employer_apply_id, job_seeker_apply_id, apply_datediff, apply_datetime, profile_id, submission_datediff,
           sum(source_flag) as source_flag
from mix_source_data
group by country_id, employer_apply_id, job_seeker_apply_id, apply_datediff, apply_datetime, profile_id, submission_datediff;
