create or replace view profile.v_profile_rolling_retention as
select country_id,
       profile_id,
       profile_blue_collar_type_id,
       seeker_cycle_start_type_id,
       seeker_cycle_first_datediff,
       action_datediff,
       day_from_cycle_start,

       action_to_contact_cnt,
       session_cnt,
       search_cnt,
       jdp_dte_view_cnt,
       jdp_agg_view_cnt,

       sum(action_to_contact_cnt)
       over (partition by country_id, seeker_cycle_first_datediff, profile_id order by day_from_cycle_start
           rows between current row and unbounded following) as atc_today_and_later_cnt,
       sum(session_cnt)
       over (partition by country_id, seeker_cycle_first_datediff, profile_id order by day_from_cycle_start
           rows between current row and unbounded following) as session_today_and_later_cnt,
       sum(search_cnt)
       over (partition by country_id, seeker_cycle_first_datediff, profile_id order by day_from_cycle_start
           rows between current row and unbounded following) as search_today_and_later_cnt,
       sum(jdp_dte_view_cnt)
       over (partition by country_id, seeker_cycle_first_datediff, profile_id order by day_from_cycle_start
           rows between current row and unbounded following) as jdp_dte_view_today_and_later_cnt,
       sum(jdp_agg_view_cnt)
       over (partition by country_id, seeker_cycle_first_datediff, profile_id order by day_from_cycle_start
           rows between current row and unbounded following) as jdp_agg_view_today_and_later_cnt
from profile.profile_cycle_activity_daily;

alter table profile.v_profile_rolling_retention
    owner to pbi;
