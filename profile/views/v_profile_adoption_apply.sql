create or replace view profile.v_profile_adoption_apply(apply_datediff, apply_cnt, apply_with_profile_cnt) as
	SELECT jdp.jdp_viewed_datediff                            AS apply_datediff,
       count(DISTINCT application_form_conversion.jdp_id) AS apply_cnt,
       count(DISTINCT
             CASE
                 WHEN profile_submitted.id IS NOT NULL THEN application_form_conversion.jdp_id
                 ELSE NULL::bigint
                 END)                                     AS apply_with_profile_cnt
FROM apply.jdp
         JOIN apply.application_form_conversion ON application_form_conversion.country_id = jdp.country_id AND
                                                   application_form_conversion.jdp_id = jdp.jdp_id AND
                                                   application_form_conversion.jdp_viewed_datediff =
                                                   jdp.jdp_viewed_datediff AND
                                                   application_form_conversion.is_success = 1
         LEFT JOIN imp.profile_accounts
                   ON profile_accounts.id_accounts = jdp.account_id AND jdp.country_id = profile_accounts.country
         LEFT JOIN profile.profile_submitted ON profile_submitted.id = profile_accounts.id_profile AND
                                                profile_submitted.country_id = profile_accounts.country AND
                                                jdp.jdp_viewed_datediff::numeric >=
                                                profile_submitted.submission_datediff::numeric
WHERE jdp.jdp_viewed_datediff > 44028
  AND jdp.country_id = 1
GROUP BY jdp.jdp_viewed_datediff;

alter table profile.v_profile_adoption_apply owner to postgres;

grant select on profile.v_profile_adoption_apply to npo;

grant select on profile.v_profile_adoption_apply to readonly;

grant select on profile.v_profile_adoption_apply to nsh;

grant select on profile.v_profile_adoption_apply to writeonly_product;

grant select on profile.v_profile_adoption_apply to readonly_ds;

