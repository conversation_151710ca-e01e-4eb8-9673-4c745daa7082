create or replace view profile.v_action_to_contact_per_profile_structure_agg as
select country_id,
       seeker_cycle_first_datediff,
       blue_collar_type_name,
       seeker_cycle_start_type_name,
       initiator_name,
       platform_user_type_id,
       platform_user_type_name,
        case when platform_user_type_id = 1 and feature_id = 1 then action_type_name
                when platform_user_type_id = 1 and feature_id in (2,3,4) then 'Message Answer'
                when platform_user_type_id = 2 and feature_id = 1 then 'Apply Answer'
                when platform_user_type_id = 2 and feature_id = 2 then 'Recommendation Answer'
                when platform_user_type_id = 2 and feature_id = 3 then 'Profile Base Answer'
                    else 'Unknown'
        end as feature_name,
        profile_id,
       sum(action_to_contact_1_days_cnt) as action_to_contact_1_days_cnt,
       sum(action_to_contact_2_days_cnt) as action_to_contact_2_days_cnt,
       sum(action_to_contact_7_days_cnt) as action_to_contact_7_days_cnt
from profile.v_action_to_contact_per_profile_structure
group by country_id,
       seeker_cycle_first_datediff,
       blue_collar_type_name,
       seeker_cycle_start_type_name,
       initiator_name,
       platform_user_type_id,
       platform_user_type_name,
        case when platform_user_type_id = 1 and feature_id = 1 then action_type_name
                when platform_user_type_id = 1 and feature_id in (2,3,4) then 'Message Answer'
                when platform_user_type_id = 2 and feature_id = 1 then 'Apply Answer'
                when platform_user_type_id = 2 and feature_id = 2 then 'Recommendation Answer'
                when platform_user_type_id = 2 and feature_id = 3 then 'Profile Base Answer'
                    else 'Unknown'
        end,
         profile_id;

grant select on profile.v_action_to_contact_per_profile_structure_agg to readonly;
grant select on profile.v_action_to_contact_per_profile_structure_agg to readonly_ds;
grant select on profile.v_action_to_contact_per_profile_structure_agg to writeonly_product;
