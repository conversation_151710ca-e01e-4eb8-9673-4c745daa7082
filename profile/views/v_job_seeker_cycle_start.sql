create view v_job_seeker_cycle_start
            (country_id, profile_id, seeker_cycle_first_datediff, seeker_cycle_num, blue_collar_type_name,
             seeker_cycle_start_type_name, initiator_name)
as
SELECT jscs.country_id,
       jscs.profile_id,
       jscs.seeker_cycle_first_datediff,
       rank()
       OVER (PARTITION BY jscs.country_id, jscs.profile_id ORDER BY jscs.seeker_cycle_first_datediff) AS seeker_cycle_num,
       (SELECT (SELECT pbct.type_name
                FROM dimension.profile_blue_collar_type pbct
                WHERE pbs.profile_blue_collar_type_id = pbct.id) AS blue_collar_type_name
        FROM profile.profile_blue_score pbs
        WHERE pbs.country_id = jscs.country_id
          AND pbs.profile_id = jscs.profile_id)                                                       AS blue_collar_type_name,
       scst.type_name                                                                                 AS seeker_cycle_start_type_name,
       put.type_name                                                                                  AS initiator_name
FROM profile.job_seeker_cycle_start jscs
         LEFT JOIN dimension.seeker_cycle_start_type scst ON scst.id = jscs.seeker_cycle_start_type_id
         LEFT JOIN dimension.platform_user_type put ON put.id = scst.initiator_type_id;
