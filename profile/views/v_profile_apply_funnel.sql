create or replace view profile.v_profile_apply_funnel(country_id, jdp_viewed_datediff, device_type, is_dte, is_premium, is_cv_apply, is_add_quest_apply, is_profile_apply, is_apply_success, is_apply_viewed, is_in_offer_status, is_in_rejected_status, blue_collar_score, jdp_view_cnt) as
	SELECT m_profile_apply_funnel.country_id,
       m_profile_apply_funnel.jdp_viewed_datediff,
       m_profile_apply_funnel.device_type,
       m_profile_apply_funnel.is_dte,
       m_profile_apply_funnel.is_premium,
       m_profile_apply_funnel.is_cv_apply,
       m_profile_apply_funnel.is_add_quest_apply,
       m_profile_apply_funnel.is_profile_apply,
       m_profile_apply_funnel.is_apply_success,
       m_profile_apply_funnel.is_apply_viewed,
       m_profile_apply_funnel.is_in_offer_status,
       m_profile_apply_funnel.is_in_rejected_status,
       m_profile_apply_funnel.blue_collar_score,
       m_profile_apply_funnel.jdp_view_cnt
FROM apply.m_profile_apply_funnel;

alter table profile.v_profile_apply_funnel owner to postgres;

grant select on profile.v_profile_apply_funnel to npo;

grant select on profile.v_profile_apply_funnel to readonly;

grant select on profile.v_profile_apply_funnel to writeonly_product;

grant select on profile.v_profile_apply_funnel to readonly_ds;

