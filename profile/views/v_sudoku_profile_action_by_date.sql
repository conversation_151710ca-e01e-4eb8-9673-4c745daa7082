create or replace view profile.v_sudoku_profile_action_by_date(initial_datediff, has_sudoku_call, profile_cnt, sum) as
	SELECT vabs.initial_datediff,
       vabs.has_sudoku_call,
       sppcnt.profile_cnt,
       sum(vabs.action_to_contact_prob) AS sum
FROM profile.v_sudoku_action vabs
         JOIN profile.v_sudoku_profile_count_by_initial_date sppcnt
              ON vabs.initial_datediff = sppcnt.initial_datediff AND vabs.has_sudoku_call = sppcnt.has_sudoku_call
WHERE (vabs.action_datediff - vabs.initial_datediff) >= 0
GROUP BY vabs.initial_datediff, vabs.has_sudoku_call, sppcnt.profile_cnt;

alter table profile.v_sudoku_profile_action_by_date owner to rlu;

grant select on profile.v_sudoku_profile_action_by_date to readonly;

grant select on profile.v_sudoku_profile_action_by_date to writeonly_product;

grant select on profile.v_sudoku_profile_action_by_date to readonly_ds;

