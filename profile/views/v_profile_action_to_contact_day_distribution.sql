create or replace view profile.v_profile_action_to_contact_day_distribution(country_id, profile_id, seeker_cycle_first_datediff, action_from_seeker_cycle_start_day_interval, action_to_contact_28_days_cnt, blue_collar_type_name, seeker_cycle_start_type_name, initiator_name) as
	SELECT jscs.country_id,
       jscs.profile_id,
       jscs.seeker_cycle_first_datediff,
       vpatcfjs.action_datediff - jscs.seeker_cycle_first_datediff AS action_from_seeker_cycle_start_day_interval,
       sum(vpatcfjs.action_to_contact_prob)                        AS action_to_contact_28_days_cnt,
       (SELECT (SELECT pbct.type_name
                FROM dimension.profile_blue_collar_type pbct
                WHERE pbs.profile_blue_collar_type_id = pbct.id) AS blue_collar_type_name
        FROM profile.v_profile_blue_score pbs
        WHERE pbs.country_id = jscs.country_id
          AND pbs.profile_id = jscs.profile_id)                    AS blue_collar_type_name,
       scst.type_name                                              AS seeker_cycle_start_type_name,
       put.type_name                                               AS initiator_name
FROM profile.job_seeker_cycle_start jscs
         JOIN profile.action_to_contact vpatcfjs
              ON vpatcfjs.country_id = jscs.country_id AND vpatcfjs.profile_id = jscs.profile_id AND
                 vpatcfjs.action_datediff >= jscs.seeker_cycle_first_datediff AND
                 vpatcfjs.action_datediff <= (jscs.seeker_cycle_first_datediff + 27)
         LEFT JOIN dimension.seeker_cycle_start_type scst ON scst.id = jscs.seeker_cycle_start_type_id
         LEFT JOIN dimension.platform_user_type put ON put.id = scst.initiator_type_id
GROUP BY jscs.country_id, jscs.profile_id, jscs.seeker_cycle_first_datediff,
         (vpatcfjs.action_datediff - jscs.seeker_cycle_first_datediff), put.type_name, scst.type_name;

alter table profile.v_profile_action_to_contact_day_distribution owner to dap;

grant select on profile.v_profile_action_to_contact_day_distribution to readonly;

grant select on profile.v_profile_action_to_contact_day_distribution to readonly_ds;

