create or replace view profile.v_profile_from_first_update(country, profile_id, email, phone, creation_datediff, is_submitted, submission_datediff) as
	SELECT p.country,
       p.id                 AS profile_id,
       p.email,
       p.phone,
       p.date_diff::integer AS creation_datediff,
       p.is_submitted,
       p.date_submitted     AS submission_datediff
FROM imp.profiles p
WHERE p.data IS NOT NULL;

alter table profile.v_profile_from_first_update owner to dap;

grant select on profile.v_profile_from_first_update to npo;

grant select on profile.v_profile_from_first_update to readonly;

grant select on profile.v_profile_from_first_update to readonly_ds;

