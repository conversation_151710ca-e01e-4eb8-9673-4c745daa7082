create or replace view profile.v_sudoku_profiles_submitted_retention(date_submitted, profiles_submitted_cnt, retention_1_14days, retention_1_7days, group_id) as
	SELECT ps.date_submitted,
       ps.profiles_submitted_cnt,
       psrt14.retention_1_14days,
       psrt7.retention_1_7days,
       2 AS group_id
FROM profile.v_sudoku_profiles_submitted_0day ps
         JOIN profile.v_sudoku_profile_submitted_retention_14days psrt14 ON ps.date_submitted = psrt14.date_submitted
         JOIN profile.v_sudoku_profile_submitted_retention_7days psrt7 ON psrt14.date_submitted = psrt7.date_submitted;

alter table profile.v_sudoku_profiles_submitted_retention owner to rlu;

grant select on profile.v_sudoku_profiles_submitted_retention to readonly;

grant select on profile.v_sudoku_profiles_submitted_retention to writeonly_product;

grant select on profile.v_sudoku_profiles_submitted_retention to readonly_ds;

