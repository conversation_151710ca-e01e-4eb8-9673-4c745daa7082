create or replace view profile.v_sudoku_retention_and_organic_all(initial_datediff, retention_1_7days, retention_1_14days, day_0_profiles_count, group_id) as
	SELECT fn_get_timestamp_from_date_diff(prj.date_submitted) AS initial_datediff,
       prj.retention_1_7days,
       prj.retention_1_14days,
       prj.profiles_submitted_cnt                          AS day_0_profiles_count,
       prj.group_id
FROM profile.v_sudoku_profiles_submitted_retention prj
UNION ALL
SELECT fn_get_timestamp_from_date_diff(srj.initial_datediff) AS initial_datediff,
       srj.day_7_profile_cnt                                 AS retention_1_7days,
       srj.day_14_profile_cnt                                AS retention_1_14days,
       srj.day_0_profile_cnt                                 AS day_0_profiles_count,
       srj.group_id
FROM profile.v_sudoku_retention srj;

alter table profile.v_sudoku_retention_and_organic_all owner to rlu;

grant select on profile.v_sudoku_retention_and_organic_all to readonly;

grant select on profile.v_sudoku_retention_and_organic_all to writeonly_product;

grant select on profile.v_sudoku_retention_and_organic_all to readonly_ds;

