create procedure profile.insert_ajpu7_per_job_search_cycle_part_1(_datediff integer)
	language plpgsql
as $$
begin


            truncate table profile.jcoin_profile_structure;

            insert into profile.jcoin_profile_structure(database_source_id, country_id, employer_id, profile_id, feature_type_id,
                                                        profile_blue_collar_type_id, platform_user_type_id, is_paid_jcoin, action_datediff,
                                                        action_datetime, jcoin_cnt)

            select poc.database_source_id,
                   c.id                                as country_id,
                   poc.employer_id,
                   poc.profile_id,
                   poc.feature_type_id,
                   pbs.profile_blue_collar_type_id,
                   poc.platform_user_type_id,
                   poc.is_paid_jcoin,
                   poc.action_datediff,
                   poc.action_datetime,
                   coalesce(count(poc.employer_id), 0) as jcoin_cnt
            from employer.v_profile_open_contact_with_packet poc
            left join profile.profile_blue_score pbs on pbs.country_id = 1 and poc.database_source_id = 1 and pbs.profile_id = poc.profile_id
            left join imp_employer.employer e on e.sources = poc.database_source_id and e.id = poc.employer_id
            left join dimension.countries c on lower(e.country_code) = lower(c.alpha_2)
            where poc.profile_id is not null
            group by poc.database_source_id, c.id, poc.employer_id, poc.profile_id, poc.feature_type_id, poc.platform_user_type_id,
                     poc.is_paid_jcoin, poc.action_datediff, poc.action_datetime, pbs.profile_blue_collar_type_id;


end;

$$;

alter procedure profile.insert_ajpu7_per_job_search_cycle_part_1(integer) owner to rlu;

