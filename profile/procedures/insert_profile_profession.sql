create procedure insert_profile_profession(_datediff integer)
    language plpgsql
as
$$
begin

        --очищення фінальної таблиці
        truncate table profile.profile_profession;

        --формуємо таблицю з усіма профессіями з профіля
        create temp table temp_desired_sentinels as
        with temp_des_job as (
            select  id,
                    cast(data as jsonb) ->> 'desiredSentinels' as desiredSentinels
        from imp.profiles
        where data is not null)
        select 1 as country_id, id as profile_id, (json_populate_recordset(null::json_type_sentinel, cast(desiredSentinels as json)))."inferredForm" as profession_name,
               (json_populate_recordset(null::json_type_sentinel, cast(desiredSentinels as json)))."source" as profession_source,
               (json_populate_recordset(null::json_type_sentinel, cast(desiredSentinels as json)))."status" as profession_status
        from temp_des_job
        order by id;

        --обираємо тільик ті які обрав або підтвердив користув<PERSON>ч
        create temp table temp_desired_job as
        select country_id, profile_id, profession_name
        from temp_desired_sentinels
        where profession_source in (0, 1) and profession_status = 0;

        --визначаємо всі унакальні професії щоб не проходити декілька разів по одній і тій же профессії
        create temp table temp_unique_profession_name as
        select distinct profession_name
        from temp_desired_job;

        --костильне рішення щоб уникнути помилки
        /*update temp_unique_profession_name
        set profession_name = 'менеджер_по_жд_перевозкам'
        where profession_name = 'менеджер_по_ж/д_перевозкам';*/

        -- до кожної унікальної професії додаємо ідентифікатор
        create temp table temp_profession_identificator as
        select profession_name, dwh_test.get_job_id_from_sentinel(profession_name) as profession_id
        from temp_unique_profession_name;

        --скрипт який визначає ідентифікатор вже з попередньо створеного словника, та відразу йде запис до фінальної таблиці
        insert into profile.profile_profession(country_id, profile_id, profession_id)
        select t.country_id, t.profile_id, /*t.profession_name,*/ (select i.profession_id from temp_profession_identificator i where i.profession_name = t.profession_name) as profession_id
        from temp_desired_job t;

        drop table temp_desired_sentinels;
        drop table temp_desired_job;
        drop table temp_unique_profession_name;
        drop table temp_profession_identificator;

end;

$$;

alter procedure insert_profile_profession(integer) owner to rlu;
