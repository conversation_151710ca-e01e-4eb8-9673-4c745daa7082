create or replace procedure profile.insert_profile_activity(_datediff integer)
	language plpgsql
as $$
begin

            insert into profile.profile_activity (country_id, seeker_cycle_first_datediff, profile_id, seeker_cycle_start_type_id,
                                                  profile_blue_collar_type_id, activity_datediff, day_from_cycle_start, action_to_contact_cnt,
                                                  session_cnt, search_cnt, jdp_dte_view_cnt, jdp_agg_view_cnt)
            with atc_without_session_aggregated as (
                select jatc.country_id, action_datediff, jatc.profile_id,
                       sum(action_to_contact_prob) as action_to_contact_cnt
                from profile.job_seeker_action_to_contact jatc
                     left join profile.profile_session ps
                     on jatc.country_id = ps.country_id
                        and jatc.profile_id = ps.profile_id
                        and jatc.action_datediff = ps.session_datediff
                where jatc.action_datediff = _datediff - 1   /* за вчорашній день */
                  and ps.session_datediff is null
                group by jatc.country_id, action_datediff, jatc.profile_id
            ),
            profile_session_aggregated as (
                select country_id, session_datediff, profile_id,
                       count(distinct session_id) as session_cnt
                from profile.profile_session
                where session_datediff = _datediff - 1       /* за вчорашній день */
                group by country_id, session_datediff, profile_id
            ),
            jatc_aggregated as (
                select country_id, action_datediff, profile_id,
                       sum(action_to_contact_prob) as action_to_contact_cnt
                from profile.job_seeker_action_to_contact atc
                where action_datediff = _datediff - 1        /* за вчорашній день */
                group by country_id, action_datediff, profile_id
            )
            -- Тільки ті звернення, для яких була сесія в той же день
            select pr.country_id,
                   pr.seeker_cycle_first_datediff,
                   pr.profile_id,
                   scst.id as seeker_cycle_start_type_id, /* 1 - profile creation,
                                                             2 - action to contact from job seeker,
                                                             3 - action to contact from employer*/

                   pbs.profile_blue_collar_type_id,       /* 1 - white collars
                                                             3 - blue collars */
                   ps.session_datediff                                  as activity_datediff,
                   ps.session_datediff - pr.seeker_cycle_first_datediff as day_from_cycle_start,
                   coalesce(action_to_contact_cnt, 0)                   as action_to_contact_cnt,
                   coalesce(ps.session_cnt, 0)                          as session_cnt,
                   0                                                    as search_cnt,
                   0                                                    as jdp_dte_view_cnt,
                   0                                                    as jdp_agg_view_cnt
            from profile.v_action_to_contact_per_profile pr
                 left join dimension.seeker_cycle_start_type scst
                 on scst.type_name = pr.seeker_cycle_start_type_name

                 left join profile.profile_blue_score pbs
                 on pbs.country_id = pr.country_id
                    and pbs.profile_id = pr.profile_id

                 left join profile_session_aggregated ps
                 on pr.country_id = ps.country_id
                    and pr.profile_id = ps.profile_id
                    and ps.session_datediff between seeker_cycle_first_datediff and seeker_cycle_first_datediff + 27

                 left join jatc_aggregated atc
                 on pr.country_id = atc.country_id
                    and pr.profile_id = atc.profile_id
                    and atc.action_datediff = session_datediff  -- тільки ті звернення, для яких була сесія в цей день
            where _datediff - seeker_cycle_first_datediff between 1 and 28    /* Ми прослідковуємо всі дії за перші 0-27 днів з початку циклу.
                                                                                                     Тому треба дивитися і ті цикли, що почалися 28 днів тому */

            union all
            -- Додаємо ті звернення, для яких немає сесій в той же день
            select pr.country_id,
                   pr.seeker_cycle_first_datediff,
                   pr.profile_id,
                   scst.id as seeker_cycle_start_type_id, /* 1 - profile creation,
                                                             2 - action to contact from job seeker,
                                                             3 - action to contact from employer*/

                   pbs.profile_blue_collar_type_id,       /* 1 - white collars
                                                             3 - blue collars */
                   atc.action_datediff                                  as activity_datediff,
                   atc.action_datediff - pr.seeker_cycle_first_datediff as day_from_cycle_start,
                   coalesce(action_to_contact_cnt, 0)                   as action_to_contact_cnt,
                   0                                                    as session_cnt,
                   0                                                    as search_cnt,
                   0                                                    as jdp_dte_view_cnt,
                   0                                                    as jdp_agg_view_cnt
            from profile.v_action_to_contact_per_profile pr
                 left join dimension.seeker_cycle_start_type scst on scst.type_name = pr.seeker_cycle_start_type_name

                 left join profile.profile_blue_score pbs
                 on pbs.country_id = pr.country_id
                    and pbs.profile_id = pr.profile_id

                 inner join atc_without_session_aggregated atc
                 on pr.country_id = atc.country_id
                    and pr.profile_id = atc.profile_id
                    and atc.action_datediff between seeker_cycle_first_datediff and seeker_cycle_first_datediff + 27
            where _datediff - seeker_cycle_first_datediff between 1 and 28;

            -- delete unwanted duplications. 
            with a as (
                select country_id,
                    profile_id,
                    seeker_cycle_first_datediff
                from profile.profile_activity
                where _datediff - seeker_cycle_first_datediff between 1 and 28
                group by country_id, profile_id, seeker_cycle_first_datediff
                having sum(case when activity_datediff is null then 1 else 0 end) > 0
                and sum(case when activity_datediff is not null then 1 else 0 end) > 0
            )
            delete from profile.profile_activity pa
            using a
            where pa.country_id = a.country_id
              and pa.profile_id = a.profile_id
              and pa.seeker_cycle_first_datediff = a.seeker_cycle_first_datediff
              and pa.activity_datediff is null;


            -- Searches added
            with a as (
                select distinct country_id, search_datediff, profile_id,
                                            count(distinct search_id) as search_cnt
                from profile.profile_search
                where search_datediff = _datediff - 1  /* за вчорашній день */
                group by country_id, search_datediff, profile_id
            )
            update pbi.profile_activity pa
            set search_cnt = a.search_cnt
            from a
            where pa.country_id = a.country_id
              and pa.profile_id = a.profile_id
              and pa.activity_datediff = a.search_datediff;


            -- JDP views added
            with a as (
                select distinct country_id, jdp_viewed_datediff, profile_id,
                       count(distinct case when jdp_flag & 1 = 1 then jdp_id end) as jdp_dte_view_cnt,
                       count(distinct case when jdp_flag & 1 = 0 then jdp_id end) as jdp_agg_view_cnt
                from profile.profile_jdp
                where jdp_viewed_datediff = _datediff - 1  /* за вчорашній день */
                group by country_id, jdp_viewed_datediff, profile_id
            )
            update pbi.profile_activity pa
            set jdp_dte_view_cnt = a.jdp_dte_view_cnt,
                jdp_agg_view_cnt = a.jdp_agg_view_cnt
            from a
            where pa.country_id = a.country_id
              and pa.profile_id = a.profile_id
              and pa.activity_datediff = a.jdp_viewed_datediff;



    end;

$$;

alter procedure profile.insert_profile_activity(integer) owner to rlu;
