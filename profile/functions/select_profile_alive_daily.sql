create function profile.select_profile_alive_daily(_datediff integer) returns TABLE(report_datediff integer, profile_cnt bigint, profile_with_session_last_7_days_cnt bigint)
	language plpgsql
as $$
begin
return query
select _datediff -1  as report_datediff,
       count(distinct p.id) as profile_cnt,
       count(distinct case when j.id is not null or s.id is not null then p.id end) as profile_with_session_last_7_days_cnt
from imp.profiles p
left join imp.profile_accounts pa
    on pa.id_profile = p.id
   and pa.country = p.country
left join apply.jdp j
    on pa.id_accounts = j.account_id
    and j.country_id = pa.country
    and j.jdp_viewed_datediff between _datediff -7 and _datediff -1
    and j.jdp_viewed_datediff >= p.date_diff
left join imp.profile_cookie_labels pcl
     on pcl.country = p.country
    and pcl.id_profile = p.id
left join imp.session s
      on s.cookie_label = pcl.cookie_labels
    and s.country = pcl.country
    and s.date_diff between _datediff -7 and _datediff -1
    and s.date_diff >= p.date_diff
where p.country = 1
 and p.data is not null
 and p.is_submitted = true
  and p.date_diff between 44034 and _datediff -1;
end;
$$;

alter function profile.select_profile_alive_daily(integer) owner to rlu;

