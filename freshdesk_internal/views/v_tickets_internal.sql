create or replace view freshdesk_internal.v_tickets_internal
            (ticket_system, id, created_at, resolved_at, country, client_type, category_type, request_type, status_name,
             is_open_status, is_resolved_status, group_name, agent_name, resolve_time_min, is_reopened_ticket,
             reply_cnt, is_merged_ticket, is_support_agent, is_support_group, is_forwarded, tags,
             last_agent_assignment_datetime, business_unit, job_complaint_actions, employer_project_id,
             session_installation_id, count_per_session_installation_id)
as
with
    working_hours as (
        select
                '00:00:00'::time without time zone +
                business_hours.start_hour::double precision * '01:00:00'::interval as start_time,
                '00:00:00'::time without time zone + business_hours.end_hour::double precision * '01:00:00'::interval -
                '00:00:01'::interval                                               as end_time
        from
            freshdesk_internal.business_hours
        where
            business_hours.team_id = 1
    ),
    work_calendar_prep as (
        select
            t_1.x   as datetime,
            case
                when h.date is not null then 0
                when t_1.x::time without time zone >= wh.start_time and t_1.x::time without time zone <= wh.end_time and
                     date_part('dow'::text, t_1.x) >= 1::double precision and
                     date_part('dow'::text, t_1.x) <= 5::double precision then 1
                else 0
                end as is_working_hours
        from
            generate_series((current_date - 401)::timestamp with time zone,
                            (current_date + 1)::timestamp with time zone, '01:00:00'::interval) t_1(x)
            left join freshdesk_internal.holidays h
                on h.date = t_1.x::date
            cross join working_hours wh
    ),
    work_calendar as (
        select
            work_calendar_prep.datetime,
            work_calendar_prep.is_working_hours,
            lead(work_calendar_prep.is_working_hours) over () as is_next_working_hour,
            lag(work_calendar_prep.is_working_hours) over ()  as is_prev_working_hour
        from
            work_calendar_prep
    ),
    status_changes_raw as (
        select
            t_1.id                                                               as ticket_id,
            timezone('europe/kiev'::text, timezone('utc'::text, t_1.created_at)) as ticket_created_at,
            timezone('europe/kiev'::text, timezone('utc'::text, sc.updated_at))  as status_started_at,
            sc.updated_status,
            case
                when s_1.is_pending or s_1.is_resolved then 0
                else 1
                end                                                              as is_open_status,
            s_1.is_resolved                                                      as is_resolved_status
        from
            freshdesk_internal.status_changes sc
            join freshdesk_internal.tickets t_1
                 on t_1.id = sc.ticket_id
            join freshdesk_internal.statuses s_1
                 on sc.updated_status = s_1.id
            join freshdesk_internal.groups g_1
                 on t_1.group_id = g_1.id
        where
                timezone('europe/kiev'::text, timezone('utc'::text, t_1.created_at))::date >= (current_date - 401)
          and   not (t_1.tags !~~ '%closed_after_resolved%'::text and t_1.status_id = 5)
          and   g_1.team_id = 1
    ),
    status_changes_prep as (
        select
            status_changes_raw.ticket_id,
            status_changes_raw.updated_status                                                              as status,
            case
                when row_number()
                     over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at) =
                     1 and status_changes_raw.updated_status = 2 then status_changes_raw.ticket_created_at
                else status_changes_raw.status_started_at
                end                                                                                        as status_started_at,
            coalesce(lead(status_changes_raw.status_started_at)
                     over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at),
                     case
                         when status_changes_raw.is_open_status = 1 then timezone('europe/kiev'::text,
                                                                                  timezone('utc'::text, now()::timestamp without time zone))
                         else status_changes_raw.status_started_at
                         end)                                                                              as status_ended_at,
            status_changes_raw.is_open_status,
            lag(status_changes_raw.is_resolved_status)
            over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at) as is_prev_resolved_status
        from
            status_changes_raw
    ),
    status_changes_calendar as (
        select
            scp.ticket_id,
            scp.status,
            scp.status_started_at,
            scp.status_ended_at,
            scp.is_open_status,
            wc.datetime,
            wc.is_working_hours,
            case
                when wc.datetime = min(wc.datetime) over (partition by scp.ticket_id, scp.status_started_at) then 1
                else 0
                end as is_first_row,
            case
                when wc.datetime = max(wc.datetime) over (partition by scp.ticket_id, scp.status_started_at) then 1
                else 0
                end as is_last_row,
            case
                when coalesce(scp.is_prev_resolved_status::integer, 0) = 1 and scp.is_open_status = 1 then 1
                else 0
                end as is_reopened_ticket
        from
            status_changes_prep scp
            left join work_calendar wc
                      on wc.datetime >= date_trunc('hour'::text, scp.status_started_at) and
                         wc.datetime <= scp.status_ended_at and
                         (wc.is_working_hours = 1 or wc.is_prev_working_hour = 1 or wc.is_next_working_hour = 1)
        where
            scp.is_open_status = 1
    ),
    status_changes_business_hours as (
        select
            status_changes_calendar.ticket_id,
            status_changes_calendar.status,
            status_changes_calendar.status_started_at,
            status_changes_calendar.status_ended_at,
            status_changes_calendar.is_open_status,
            status_changes_calendar.datetime,
            status_changes_calendar.is_working_hours,
            status_changes_calendar.is_first_row,
            status_changes_calendar.is_last_row,
            status_changes_calendar.is_reopened_ticket,
            (date_part('epoch'::text,
                       case
                           when status_changes_calendar.is_first_row = 1 then
                                   (status_changes_calendar.is_working_hours *
                                    status_changes_calendar.is_open_status)::double precision * (coalesce(lead(
                                                                                                          status_changes_calendar.datetime)
                                                                                                          over (partition by status_changes_calendar.ticket_id, status_changes_calendar.status_started_at order by status_changes_calendar.datetime),
                                                                                                          status_changes_calendar.status_ended_at::timestamp with time zone) -
                                                                                                 status_changes_calendar.status_started_at::timestamp with time zone)
                           when status_changes_calendar.is_last_row = 1 then (status_changes_calendar.is_working_hours *
                                                                              status_changes_calendar.is_open_status)::double precision *
                                                                             (status_changes_calendar.status_ended_at::timestamp with time zone -
                                                                              status_changes_calendar.datetime)
                           else (status_changes_calendar.is_working_hours *
                                 status_changes_calendar.is_open_status)::double precision * (
                                                lead(status_changes_calendar.datetime)
                                                over (partition by status_changes_calendar.ticket_id, status_changes_calendar.status_started_at order by status_changes_calendar.datetime) -
                                                status_changes_calendar.datetime)
                           end) / 60::double precision)::numeric as resolve_time_min
        from
            status_changes_calendar
    ),
    ticket_resolve_time as (
        select
            status_changes_business_hours.ticket_id,
            round(sum(status_changes_business_hours.resolve_time_min), 2) as resolve_time_min,
            max(status_changes_business_hours.is_reopened_ticket)         as is_reopened_ticket
        from
            status_changes_business_hours
        group by status_changes_business_hours.ticket_id
    ),
    ticket_replies as (
        select
            replies.ticket_id,
            count(*) as reply_cnt
        from
            freshdesk_internal.replies
        group by replies.ticket_id
    ),
    ticket_assignment_time as (
        select
            ac_1.ticket_id,
            max(ac_1.updated_at) as assignment_datetime
        from
            freshdesk_internal.assignment_changes ac_1
        where
            ac_1.updated_agent is not null
        group by ac_1.ticket_id
    )
select
    'internal'::text                                                    as ticket_system,
    t.id,
    timezone('europe/kiev'::text, timezone('utc'::text, t.created_at))  as created_at,
    timezone('europe/kiev'::text, timezone('utc'::text, t.resolved_at)) as resolved_at,
    t.country,
    ct.name                                                             as client_type,
    cat.name                                                            as category_type,
    rt.name                                                             as request_type,
    s.name                                                              as status_name,
    case
        when s.is_pending or s.is_resolved then 0
        else 1
        end                                                             as is_open_status,
    s.is_resolved::integer                                              as is_resolved_status,
    coalesce(g.display_name, g.name)                                    as group_name,
    a.name                                                              as agent_name,
    trt.resolve_time_min,
    coalesce(trt.is_reopened_ticket, 0)                                 as is_reopened_ticket,
    tr.reply_cnt,
    case
        when coalesce(t.tags, ''::text) !~~ '%closed_after_resolved%'::text and t.status_id = 5 then 1
        else 0
        end                                                             as is_merged_ticket,
    case
        when a.team_id = 1 then 1
        else 0
        end                                                             as is_support_agent,
    case
        when g.team_id = 1 then 1
        else 0
        end                                                             as is_support_group,
    case
        when (r.email::text <> all
              (array ['<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text, '<EMAIL>'::character varying::text])) and
             (t.tags ~~ '%mkteam%'::text or t.tags ~~ '%legal%'::text or t.tags ~~ '%auctiondevs%'::text or
              t.tags ~~ '%accounting%'::text or t.tags ~~ '%xteam%'::text or t.tags ~~ '%jteam%'::text or
              t.tags ~~ '%dsteam%'::text or t.tags ~~ '%sup-amt%'::text or t.tags ~~ '%sup-vvt%'::text or
              ac.ticket_id is not null) then 1
        else 0
        end                                                             as is_forwarded,
    t.tags,
    tat.assignment_datetime                                             as last_agent_assignment_datetime,
    case
        when ct.name::text = 'Partner'::text then 'Aggregation'::text
        when ct.name::text = 'General'::text then 'General'::text
        when (ct.name::text = any
              (array ['Employer'::character varying::text, 'Jobseeker'::character varying::text])) and
             (t.country::text = any
              (array ['UA'::character varying::text, 'HU'::character varying::text, 'RO'::character varying::text]))
            then 'DTE'::text
        when (ct.name::text = any
              (array ['Employer'::character varying::text, 'Jobseeker'::character varying::text])) and
             (t.country::text = any
              (array ['RS'::character varying::text, 'GR'::character varying::text, 'HR'::character varying::text, 'BA'::character varying::text]))
            then 'EA Balkans'::text
        when ct.name::text = 'Employer'::text then 'EA International'::text
        when ct.name::text = 'Jobseeker'::text then 'Aggregation'::text
        else null::text
        end                                                             as business_unit,
    null::text                                                          as job_complaint_actions,
    null::text                                                          as employer_project_id,
    null::text                                                          as session_installation_id,
    null::integer                                                       as count_per_session_installation_id
from
    freshdesk_internal.tickets t
    left join freshdesk_internal.groups g
              on g.id = t.group_id
    left join freshdesk_internal.client_types ct
              on t.type_client = ct.id
    left join freshdesk_internal.category_types cat
              on t.type_category = cat.id
    left join freshdesk_internal.request_types rt
              on t.type_request = rt.id
    left join freshdesk_internal.statuses s
              on t.status_id = s.id
    left join freshdesk_internal.agents a
              on t.agent_id = a.id
    left join ticket_resolve_time trt
              on trt.ticket_id = t.id
    left join ticket_replies tr
              on tr.ticket_id = t.id
    left join freshdesk_internal.requesters r
              on t.requester_id = r.id
    left join (select
                   ac_1.ticket_id
               from
                   freshdesk_internal.assignment_changes ac_1
                   join freshdesk_internal.groups g_1
                        on ac_1.updated_group = g_1.id
               group by ac_1.ticket_id
               having
                     count(distinct coalesce(g_1.team_id, 0)) > 1
                 and max(
                             case
                                 when g_1.team_id = 1 then g_1.team_id
                                 else null::integer
                                 end) = 1) ac
              on ac.ticket_id = t.id
    left join ticket_assignment_time tat
              on tat.ticket_id = t.id
where
        timezone('europe/kiev'::text, timezone('utc'::text, t.created_at)) >= (current_date - 401);