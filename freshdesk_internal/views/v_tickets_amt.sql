create view freshdesk_internal.v_tickets_amt
            (id, is_amt_ticket, ticket_group, created_at, resolved_at, first_reply_at, country, tags, ticket_source,
             resolution_time_sla_hrs, selected_tags, source_type, status_name, is_open_status, is_resolved_status,
             is_pending_status, agent_name, resolve_time_min, is_reopened_ticket, is_merged_ticket)
as
with
    target_tickets as (
        select
            t_1.id
        from
            freshdesk_internal.tickets t_1
            join freshdesk_internal.groups g2
                 on t_1.group_id = g2.id
        where
            g2.team_id = 2
        union
        select distinct
            ac.ticket_id as id
        from
            freshdesk_internal.assignment_changes ac
            join freshdesk_internal.groups g_1
                 on ac.updated_group = g_1.id
        where
            g_1.team_id = 2
    ),
    working_hours as (
        select
                '00:00:00'::time without time zone +
                business_hours.start_hour::double precision * '01:00:00'::interval as start_time,
                '00:00:00'::time without time zone + business_hours.end_hour::double precision * '01:00:00'::interval -
                '00:00:01'::interval                                               as end_time
        from
            freshdesk_internal.business_hours
        where
            business_hours.team_id = 2
    ),
    work_calendar_prep as (
        select
            t_1.x   as datetime,
            case
                when t_1.x::time without time zone >= wh.start_time and t_1.x::time without time zone <= wh.end_time and
                     date_part('dow'::text, t_1.x) >= 1::double precision and
                     date_part('dow'::text, t_1.x) <= 5::double precision then 1
                else 0
                end as is_working_hours
        from
            generate_series('2019-09-30 00:00:00+00'::timestamp with time zone,
                            (current_date + 1)::timestamp with time zone, '01:00:00'::interval) t_1(x)
            cross join working_hours wh
    ),
    work_calendar as (
        select
            work_calendar_prep.datetime,
            work_calendar_prep.is_working_hours,
            lead(work_calendar_prep.is_working_hours) over () as is_next_working_hour,
            lag(work_calendar_prep.is_working_hours) over ()  as is_prev_working_hour
        from
            work_calendar_prep
    ),
    status_changes_raw as (
        select
            t_1.id                                                               as ticket_id,
            timezone('europe/kiev'::text, timezone('utc'::text, t_1.created_at)) as ticket_created_at,
            timezone('europe/kiev'::text, timezone('utc'::text, sc.updated_at))  as status_started_at,
            sc.updated_status,
            case
                when s_1.is_pending or s_1.is_resolved then 0
                else 1
                end                                                              as is_open_status,
            s_1.is_resolved                                                      as is_resolved_status
        from
            freshdesk_internal.status_changes sc
            join target_tickets tt_1
                 on tt_1.id = sc.ticket_id
            join freshdesk_internal.tickets t_1
                 on t_1.id = sc.ticket_id
            join freshdesk_internal.statuses s_1
                 on sc.updated_status = s_1.id
        where
                timezone('europe/kiev'::text, timezone('utc'::text, t_1.created_at))::date >= '2019-09-30'::date
          and   not (t_1.tags !~~ '%closed_after_resolved%'::text and t_1.status_id = 5)
    ),
    status_changes_prep as (
        select
            status_changes_raw.ticket_id,
            status_changes_raw.updated_status                                                              as status,
            case
                when row_number()
                     over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at) =
                     1 and status_changes_raw.updated_status = 2 then status_changes_raw.ticket_created_at
                else status_changes_raw.status_started_at
                end                                                                                        as status_started_at,
            coalesce(lead(status_changes_raw.status_started_at)
                     over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at),
                     case
                         when status_changes_raw.is_open_status = 1 then timezone('europe/kiev'::text,
                                                                                  timezone('utc'::text, now()::timestamp without time zone))
                         else status_changes_raw.status_started_at
                         end)                                                                              as status_ended_at,
            status_changes_raw.is_open_status,
            lag(status_changes_raw.is_resolved_status)
            over (partition by status_changes_raw.ticket_id order by status_changes_raw.status_started_at) as is_prev_resolved_status
        from
            status_changes_raw
    ),
    status_changes_calendar as (
        select
            scp.ticket_id,
            scp.status,
            scp.status_started_at,
            scp.status_ended_at,
            scp.is_open_status,
            wc.datetime,
            wc.is_working_hours,
            case
                when wc.datetime = min(wc.datetime) over (partition by scp.ticket_id, scp.status_started_at) then 1
                else 0
                end as is_first_row,
            case
                when wc.datetime = max(wc.datetime) over (partition by scp.ticket_id, scp.status_started_at) then 1
                else 0
                end as is_last_row,
            case
                when coalesce(scp.is_prev_resolved_status::integer, 0) = 1 and scp.is_open_status = 1 then 1
                else 0
                end as is_reopened_ticket
        from
            status_changes_prep scp
            left join work_calendar wc
                      on wc.datetime >= date_trunc('hour'::text, scp.status_started_at) and
                         wc.datetime <= scp.status_ended_at and
                         (wc.is_working_hours = 1 or wc.is_prev_working_hour = 1 or wc.is_next_working_hour = 1)
        where
            scp.is_open_status = 1
    ),
    status_changes_business_hours as (
        select
            status_changes_calendar.ticket_id,
            status_changes_calendar.status,
            status_changes_calendar.status_started_at,
            status_changes_calendar.status_ended_at,
            status_changes_calendar.is_open_status,
            status_changes_calendar.datetime,
            status_changes_calendar.is_working_hours,
            status_changes_calendar.is_first_row,
            status_changes_calendar.is_last_row,
            status_changes_calendar.is_reopened_ticket,
            (date_part('epoch'::text,
                       case
                           when status_changes_calendar.is_first_row = 1 then
                                   (status_changes_calendar.is_working_hours *
                                    status_changes_calendar.is_open_status)::double precision * (coalesce(lead(
                                                                                                          status_changes_calendar.datetime)
                                                                                                          over (partition by status_changes_calendar.ticket_id, status_changes_calendar.status_started_at order by status_changes_calendar.datetime),
                                                                                                          status_changes_calendar.status_ended_at::timestamp with time zone) -
                                                                                                 status_changes_calendar.status_started_at::timestamp with time zone)
                           when status_changes_calendar.is_last_row = 1 then (status_changes_calendar.is_working_hours *
                                                                              status_changes_calendar.is_open_status)::double precision *
                                                                             (status_changes_calendar.status_ended_at::timestamp with time zone -
                                                                              status_changes_calendar.datetime)
                           else (status_changes_calendar.is_working_hours *
                                 status_changes_calendar.is_open_status)::double precision * (
                                                lead(status_changes_calendar.datetime)
                                                over (partition by status_changes_calendar.ticket_id, status_changes_calendar.status_started_at order by status_changes_calendar.datetime) -
                                                status_changes_calendar.datetime)
                           end) / 60::double precision)::numeric as resolve_time_min
        from
            status_changes_calendar
    ),
    ticket_resolve_time as (
        select
            status_changes_business_hours.ticket_id,
            round(sum(status_changes_business_hours.resolve_time_min), 2) as resolve_time_min,
            max(status_changes_business_hours.is_reopened_ticket)         as is_reopened_ticket
        from
            status_changes_business_hours
        group by status_changes_business_hours.ticket_id
    )
select
    t.id,
    case
        when g.team_id = 2 then 1
        else 0
        end                                                                as is_amt_ticket,
    g.name                                                                 as ticket_group,
    timezone('europe/kiev'::text, timezone('utc'::text, t.created_at))     as created_at,
    timezone('europe/kiev'::text, timezone('utc'::text, t.resolved_at))    as resolved_at,
    timezone('europe/kiev'::text, timezone('utc'::text, t.first_reply_at)) as first_reply_at,
    t.country,
    t.tags,
    case
        when t.tags ~~ '%Sales%'::text then 'Sales'::text
        when t.tags ~~ '%outreach%'::text then 'Outreach'::text
        when t.tags ~~ '%Support%'::text then 'Support'::text
        else null::text
        end                                                                as ticket_source,
    case
        when t.tags ~~ '%Sales%'::text then 2
        when t.tags ~~ '%outreach%'::text then 40
        else null::integer
        end                                                                as resolution_time_sla_hrs,
    case
        when t.tags ~~ '%salary%'::text and t.tags !~~ '%salary_from_desc%'::text then 'salary'::text
        when t.tags ~~ '%fromCrawler%'::text then 'fromCrawler'::text
        when t.tags ~~ '%wrong_region%'::text then 'wrong_region'::text
        else null::text
        end                                                                as selected_tags,
    st.name                                                                as source_type,
    s.name                                                                 as status_name,
    case
        when s.is_pending or s.is_resolved then 0
        else 1
        end                                                                as is_open_status,
    s.is_resolved::integer                                                 as is_resolved_status,
    s.is_pending                                                           as is_pending_status,
    a.name                                                                 as agent_name,
    trt.resolve_time_min,
    coalesce(trt.is_reopened_ticket, 0)                                    as is_reopened_ticket,
    case
        when coalesce(t.tags, ''::text) !~~ '%closed_after_resolved%'::text and t.status_id = 5 then 1
        else 0
        end                                                                as is_merged_ticket
from
    freshdesk_internal.tickets t
    join target_tickets tt
         on tt.id = t.id
    left join freshdesk_internal.groups g
              on g.id = t.group_id
    left join freshdesk_internal.statuses s
              on t.status_id = s.id
    left join freshdesk_internal.agents a
              on t.agent_id = a.id
    left join ticket_resolve_time trt
              on trt.ticket_id = t.id
    left join freshdesk_internal.source_types st
              on st.id = t.source_type
where
        timezone('europe/kiev'::text, timezone('utc'::text, t.created_at)) >=
        '2019-09-30 00:00:00'::timestamp without time zone;