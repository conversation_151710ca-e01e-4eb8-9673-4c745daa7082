create or replace view affiliate.v_project_publisher_cpa
            (min_date, max_date, sale_manager, affiliate_manager, country_id, country, id_project, project_name,
             is_job_ad_exchange, publisher, clicks, current_publisher_cpa, publisher_target_cpa_usd,
             current_affiliate_cpa, affiliate_target_cpa_usd, total_project_cpa, total_project_target_cpa_usd, rule,
             perc_of_pub_target_exceeded, perc_of_affiliate_target_exceeded, perc_of_total_project_target_exceeded,
             max_deviation)
as
with
    target_action_raw as (
        select *,
           row_number() over (partition by country, id_project order by date desc) AS row
        from
            aggregation.auction_user_target_action as auta
       ),
    raw_data as (select
                     vpcbs.sale_manager,
                     vpcbs.country_id,
                     vpcbs.country,
                     vpcbs.id_project,
                     vpcbs.project_name,
                     vpcbs.traffic_source,
                     vpcbs.traffic_channel,
                     sum(vpcbs.aways)                            as aways,
                     sum(vpcbs.away_revenue)                     as away_revenue,
                     sum(vpcbs.conversions)                      as conversions,
                     case
                         when sum(vpcbs.aways) > 0::numeric then
                             sum(vpcbs.target_cpa_usd * vpcbs.aways::double precision) /
                             sum(vpcbs.aways)::double precision
                         else 0::double precision
                         end                                     as target_cpa_usd,
                     current_date - (coalesce(pcd.delay, 1) + 2) as min_date,
                     current_date - coalesce(pcd.delay, 1)       as max_date
                 from
                     affiliate.v_project_conversions_by_source vpcbs
                     left join affiliate.project_conversions_delay pcd
                               on lower(pcd.country::text) = lower(vpcbs.country::text) and vpcbs.id_project = pcd.project_id
                 where
                       vpcbs.is_conversion_considered = 1
                   and vpcbs.country in ('US', 'UK', 'FR', 'DE', 'NL', 'CA')
                   and vpcbs.record_date >= (current_date - (coalesce(pcd.delay, 1) + 2))
                   and vpcbs.record_date <= (current_date - coalesce(pcd.delay, 1))
                 group by
                     vpcbs.sale_manager, vpcbs.country_id, vpcbs.country, vpcbs.id_project, vpcbs.project_name,
                     vpcbs.traffic_source, vpcbs.traffic_channel, pcd.delay),
    prepared_data as (select
                          rd.min_date,
                          rd.max_date,
                          rd.sale_manager,
                          rd.country_id,
                          rd.country,
                          rd.id_project,
                          rd.project_name,
                          rd.traffic_channel,
                          rd.traffic_source                                                                                      as publisher,
                          away_revenue,
                          conversions,
                          sum(rd.target_cpa_usd * rd.aways::double precision)
                          over (partition by rd.country, rd.id_project)                                                    as sum_project_target_cpa_usd,
                          sum(rd.target_cpa_usd * rd.aways::double precision)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel)                          as sum_traf_channel_target_cpa_usd,
                          sum(rd.target_cpa_usd * rd.aways::double precision)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel, rd.traffic_source) as sum_traf_source_target_cpa_usd,
                          sum(rd.aways)
                          over (partition by rd.country, rd.id_project)                                                    as total_project_clicks,
                          sum(rd.aways)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel)                          as traf_channel_clicks,
                          sum(rd.aways)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel, rd.traffic_source) as traf_source_clicks,
                          sum(rd.away_revenue)
                          over (partition by rd.country, rd.id_project)                                                    as total_project_away_revenue,
                          sum(rd.away_revenue)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel)                          as traf_channel_away_revenue,
                          sum(rd.away_revenue)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel, rd.traffic_source) as traf_source_away_revenue,
                          sum(rd.conversions)
                          over (partition by rd.country, rd.id_project)                                                    as total_project_conversions,
                          sum(rd.conversions)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel)                          as traf_channel_conversions,
                          sum(rd.conversions)
                          over (partition by rd.country, rd.id_project, rd.traffic_channel, rd.traffic_source) as traf_source_conversions
                      from
                          raw_data rd),
    final_data as (select
                       pd.min_date,
                       pd.max_date,
                       pd.sale_manager,
                       m.code                as affiliate_manager,
                       pd.country_id,
                       pd.country,
                       pd.id_project,
                       pd.project_name,
                       pd.publisher,
                       ip.hide_in_search     as is_job_ad_exchange,
                       pd.traf_source_clicks as clicks,
                       case
                           when pd.traf_source_conversions > 0::numeric
                               then pd.traf_source_away_revenue / pd.traf_source_conversions
                           else 0::numeric
                           end               as current_publisher_cpa,
                       case
                           when pd.traf_source_clicks > 0::numeric then pd.sum_traf_source_target_cpa_usd /
                                                                        pd.traf_source_clicks::double precision
                           else 0::double precision
                           end               as publisher_target_cpa_usd,
                       case
                           when pd.traf_channel_conversions > 0::numeric
                               then pd.traf_channel_away_revenue / pd.traf_channel_conversions
                           else 0::numeric
                           end               as current_affiliate_cpa,
                       case
                           when pd.traf_channel_clicks > 0::numeric then pd.sum_traf_channel_target_cpa_usd /
                                                                         pd.traf_channel_clicks::double precision
                           else 0::double precision
                           end               as affiliate_target_cpa_usd,
                       case
                           when pd.total_project_conversions > 0::numeric
                               then pd.total_project_away_revenue / pd.total_project_conversions
                           else 0::numeric
                           end               as current_total_project_cpa,
                       case
                           when pd.total_project_clicks > 0::numeric then pd.sum_project_target_cpa_usd /
                                                                          pd.total_project_clicks::double precision
                           else 0::double precision
                           end               as total_project_target_cpa_usd,
                       away_revenue,
                       conversions
                   from
                       prepared_data pd
                       join affiliate.partner_settings ps
                            on ps.country::text = lower(pd.country::text) and ps.partner::text = pd.publisher::text
                       left join affiliate.dic_manager m
                                 on m.id = ps.id_manager
                       join dimension.info_project ip
                            on ip.country = pd.country_id and ip.id = pd.id_project
                   where
                         pd.traffic_channel::text = 'Affiliate'::text
                     and pd.traf_source_clicks >= 100::numeric)
select
    fd.min_date,
    fd.max_date,
    fd.sale_manager,
    fd.affiliate_manager,
    fd.country_id,
    fd.country,
    fd.id_project,
    fd.project_name,
    fd.is_job_ad_exchange,
    fd.publisher,
    fd.clicks,
    case
        when fd.current_publisher_cpa = 0 then null
        else fd.current_publisher_cpa end as current_publisher_cpa,
    fd.publisher_target_cpa_usd,
    fd.current_affiliate_cpa,
    fd.affiliate_target_cpa_usd,
    fd.current_total_project_cpa as total_project_cpa,
    fd.total_project_target_cpa_usd,
    case
        when auta.max_deviation is null then 0
        when coalesce(conversions, 0) = 0 and
             away_revenue > coalesce(publisher_target_cpa_usd, 0) * (1 + auta.max_deviation) then 1
        when coalesce(conversions, 0) > 0
            and (case
                     when fd.publisher_target_cpa_usd > 0::double precision and fd.current_publisher_cpa > 0::numeric
                         then fd.current_publisher_cpa::double precision / fd.publisher_target_cpa_usd *
                              100.0::double precision -
                              100.0::double precision
                     else 0::double precision
                end) > (auta.max_deviation * 100) then 1
        else 0
        end                      as rule,
    case
        when fd.publisher_target_cpa_usd > 0 and fd.current_publisher_cpa > 0
            then (fd.current_publisher_cpa / fd.publisher_target_cpa_usd * 100.0) - 100.0
        end as perc_of_pub_target_exceeded,
    case
        when fd.affiliate_target_cpa_usd > 0 and fd.current_affiliate_cpa > 0
            then (fd.current_affiliate_cpa / fd.affiliate_target_cpa_usd * 100.0) - 100.0
        end as perc_of_affiliate_target_exceeded,
    case
        when fd.total_project_target_cpa_usd > 0 and fd.current_total_project_cpa > 0
            then (fd.current_total_project_cpa / fd.total_project_target_cpa_usd * 100.0) - 100.0
        end as perc_of_total_project_target_exceeded,
    auta.max_deviation
from
    final_data fd
    left join target_action_raw as auta
              on auta.country = fd.country and auta.id_project = fd.id_project and auta.row = 1
where
      fd.clicks >= 200
  and not (fd.country::text = 'US'::text and fd.id_project = 1573)
  and not (fd.country::text = 'DE'::text and fd.id_project = 919);

alter table affiliate.v_project_publisher_cpa
    owner to ypr;

grant select on affiliate.v_project_publisher_cpa to readonly;

grant delete, insert, select, update on affiliate.v_project_publisher_cpa to writeonly_pyscripts;