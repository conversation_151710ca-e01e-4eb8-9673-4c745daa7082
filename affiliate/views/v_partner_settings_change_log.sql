create or replace view affiliate.v_partner_settings_change_log
            (source_id, local_id, field_name, field_value, min_dt, max_dt, min_gather_dt, max_gather_dt) as
with
    t as (
        select
            pscl.source_id,
            pscl.local_id,
            pscl.field_name,
            case
                when pscl.field_name::text = 'static_min_cpc'::text then
                    case
                        when pscl.value_after::text = 'true'::text then 1.0
                        else 0.0
                        end
                else pscl.value_after::numeric
                end                as field_value,
            pscl.update_datetime,
            min(tsl.task_datetime) as min_gather_dt
        from
            affiliate.partner_settings_change_log pscl
            left join affiliate.task_status_log tsl
                      on pscl.source_id = tsl.source_id and pscl.local_id = tsl.id_partner and
                         pscl.update_datetime < tsl.task_datetime
        where
                pscl.field_name::text = any
                (array ['cpc_ratio'::character varying, 'worst_cpc_ratio'::character varying, 'min_cpc_in_usd'::character varying, 'max_cpc_in_usd'::character varying, 'local_flags'::character varying, 'static_min_cpc'::character varying, 'update_gap'::character varying, 'optimized_cpc_part'::character varying, 'reassembled_jobs_part'::character varying]::text[])
        group by pscl.source_id, pscl.local_id, pscl.field_name, pscl.value_after, pscl.update_datetime
    ),
    t2 as (
        select
            t.source_id,
            t.local_id,
            t.field_name,
            t.field_value,
            t.update_datetime                                                                                          as min_dt,
            t.min_gather_dt,
            coalesce(lead(t.min_gather_dt)
                     over (partition by t.source_id, t.local_id, t.field_name order by t.update_datetime)::timestamp with time zone,
                     now())::timestamp without time zone                                                               as max_gather_dt,
            coalesce(lead(t.update_datetime)
                     over (partition by t.source_id, t.local_id, t.field_name order by t.update_datetime)::timestamp with time zone,
                     now())::timestamp without time zone                                                               as max_dt,
            row_number()
            over (partition by t.source_id, t.local_id, t.field_name, t.min_gather_dt order by t.update_datetime desc) as row_num
        from
            t
    )
select
    t2.source_id,
    t2.local_id,
    t2.field_name,
    t2.field_value,
    t2.min_dt,
    t2.max_dt,
    t2.min_gather_dt,
    t2.max_gather_dt
from
    t2
where
    t2.row_num = 1;

alter table affiliate.v_partner_settings_change_log
    owner to ypr;

grant select on affiliate.v_partner_settings_change_log to readonly;

grant delete, insert, select, update on affiliate.v_partner_settings_change_log to writeonly_pyscripts;

