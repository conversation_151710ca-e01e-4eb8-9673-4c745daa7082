create or replace view affiliate.v_dagster_job_log
            (run_id, job_name, operation_name, op_start_datetime, op_end_datetime, record_date, schema_name,
             table_name) as
select
    djl.run_id,
    djl.job_name,
    djl.operation_name,
    timezone('europe/kiev'::text, timezone('utc'::text, djl.op_start_datetime)) as op_start_datetime,
    timezone('europe/kiev'::text, timezone('utc'::text, djl.op_end_datetime))   as op_end_datetime,
    djl.record_date,
    djl.schema_name,
    djl.table_name
from
    affiliate.dagster_job_log djl
where
        timezone('europe/kiev'::text, timezone('utc'::text, djl.op_start_datetime))::date >= (current_date - 90);

alter table affiliate.v_dagster_job_log
    owner to ypr;

grant select on affiliate.v_dagster_job_log to readonly;

grant delete, insert, select, update on affiliate.v_dagster_job_log to writeonly_pyscripts;

