create or replace procedure affiliate.insert_partner_daily_snapshot(_datediff integer)
    language plpgsql
as
$$
begin
    drop table if exists temp_settings_log;
    drop table if exists temp_settings_clean;
    drop table if exists temp_publisher_list;

    create temporary table temp_settings_log as
    select
        pscl.source_id,
        pscl.local_id,
        (last_value(case when field_name = 'id_soska_user' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::int     as id_manager,
        (last_value(case when field_name = 'id_country_currency' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::int     as partner_id_currency,
        (last_value(case when field_name = 'cpc_ratio' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as cpc_ratio,
        (last_value(case when field_name = 'worst_cpc_ratio' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as worst_cpc_ratio,
        (last_value(case when field_name = 'min_cpc_in_usd' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as min_cpc_in_usd,
        (last_value(case when field_name = 'global_flags' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::int     as global_flags,
        (last_value(case when field_name = 'local_flags' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::int     as local_flags,
        (last_value(case when field_name = 'static_min_cpc' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::bool    as static_min_cpc,
        (last_value(case when field_name = 'update_gap' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::int     as update_gap,
        (last_value(case when field_name = 'optimized_cpc_part' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as optimized_cpc_part,
        (last_value(case when field_name = 'reassembled_jobs_part' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as reassembled_jobs_part,
        (last_value(case when field_name = 'max_cpc_in_usd' then pscl.value_after end)
         over (partition by pscl.source_id, pscl.local_id, field_name order by update_datetime
             range between unbounded preceding and unbounded following))::numeric as max_cpc_in_usd
    from
        affiliate.partner_settings_change_log pscl
    where
        update_datetime::date <= public.fn_get_date_from_date_diff(_datediff);

    create temporary table temp_settings_clean as
    select distinct
        now()                                                                           as update_datetime,
        sl.local_id                                                                     as id_local_partner,
        ps.partner                                                                      as partner,
        lower(ps.country)                                                               as country,
        (max(sl.partner_id_currency)
         over (partition by sl.source_id, sl.local_id))::int                            as partner_id_currency,
        (max(sl.cpc_ratio) over (partition by sl.source_id, sl.local_id))::numeric      as cpc_ratio,
        (max(sl.min_cpc_in_usd) over (partition by sl.source_id, sl.local_id))::numeric as min_cpc_in_usd,
        (max(sl.global_flags) over (partition by sl.source_id, sl.local_id))::int       as global_flags,
        (max(cast(sl.static_min_cpc as int))
         over (partition by sl.source_id, sl.local_id))::int                            as static_min_cpc,
        (max(sl.update_gap) over (partition by sl.source_id, sl.local_id))::int         as update_gap,
        ps.run_interval,
        (max(sl.id_manager) over (partition by sl.source_id, sl.local_id))::int         as id_manager,
        (max(sl.local_flags) over (partition by sl.source_id, sl.local_id))::int        as local_flags,
        ps.offset_in_hour,
        (max(sl.worst_cpc_ratio)
         over (partition by sl.source_id, sl.local_id))::numeric                        as worst_cpc_ratio,
        (max(sl.optimized_cpc_part)
         over (partition by sl.source_id, sl.local_id))::numeric                        as optimized_cpc_part,
        (max(sl.reassembled_jobs_part)
         over (partition by sl.source_id, sl.local_id))::numeric                        as reassembled_jobs_part,
        (max(sl.max_cpc_in_usd) over (partition by sl.source_id, sl.local_id))::numeric as max_cpc_in_usd
    from
        temp_settings_log sl
        join affiliate.partner_settings ps
             on sl.local_id = ps.id_local_partner
                 and sl.source_id = ps.source_id;

    create temporary table temp_publisher_list as
    select distinct
        country,
        partner,
        date_diff
    from
        affiliate.temp_prod_click;

    delete from affiliate.partner_daily_snapshot where date_diff = _datediff;

    insert into
        affiliate.partner_daily_snapshot(date_diff, id_country, partner, manager, partner_id_currency, feed_cpc_ratio,
                                         feed_min_cpc_usd, feed_is_only_easy_apply, feed_run_interval, feed_type,
                                         feed_is_static_min_cpc, update_gap, is_rounded_cpc, local_flags,
                                         worst_cpc_ratio, optimized_cpc_part, reassembled_jobs_part, global_flags,
                                         max_cpc_in_usd)
    select distinct
        tbl.date_diff,
        co.id                                                                        as id_country,
        t.partner,
        coalesce(m.code, t.id_manager::varchar)                                      as manager,
        partner_id_currency,
        cpc_ratio                                                                    as feed_cpc_ratio,
        min_cpc_in_usd                                                               as feed_min_cpc_usd,
        case
            when global_flags & 1 = 1 then 1
            when global_flags is null then null
            else 0
            end                                                                      as feed_is_only_easy_apply,
        run_interval                                                                 as feed_run_interval,
        case when cpc_ratio > 0 and (min_cpc_in_usd > 0 or max_cpc_in_usd > 0) then 'paid' else 'organic' end as feed_type,
        static_min_cpc                                                               as feed_is_static_min_cpc,
        update_gap,
        case when local_flags & 8 = 8 then 1 else 0 end                              as is_rounded_cpc,
        local_flags,
        worst_cpc_ratio,
        optimized_cpc_part,
        reassembled_jobs_part,
        global_flags,
        max_cpc_in_usd
    from
        temp_settings_clean t
        join dimension.countries co
             on lower(co.alpha_2) = t.country
        join temp_publisher_list tbl
             on tbl.country = t.country
                 and lower(tbl.partner) = lower(t.partner)
        left join affiliate.dic_manager m
                  on m.id = t.id_manager;

    drop table if exists temp_settings_log;
    drop table if exists temp_settings_clean;
    drop table if exists temp_publisher_list;
end;

$$;

alter procedure affiliate.insert_partner_daily_snapshot(integer) owner to ypr;

