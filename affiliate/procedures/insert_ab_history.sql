create or replace procedure affiliate.insert_ab_history(_datediff integer)
    language plpgsql
as
$$
begin

            truncate table affiliate.ab_history;

            --connect to NL
            perform dblink_connect('myconnNL', 'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

            insert into affiliate.ab_history(source_id, id, test_name, id_test, start_utc, finish_utc, id_partners, groups, iteration)
            select source_id, id, test_name, id_test, start_utc, finish_utc, id_partners, groups, iteration
            from dblink('myconnNL',
                'select 1 as source_id,
                        id,
                        name as test_name,
                        id_test,
                        start_utc,
                        finish_utc,
                        id_partners,
                        groups,
                        iteration
                 from public.ab_history;') AS ab_history (source_id smallint, id integer, test_name varchar(300),  id_test integer, start_utc timestamp, finish_utc timestamp, id_partners integer[], groups json, iteration integer);

            perform dblink_disconnect('myconnNL');

            --connect to US
            perform dblink_connect('myconnUS', 'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');


            insert into affiliate.ab_history(source_id, id, test_name, id_test, start_utc, finish_utc, id_partners, groups, iteration)
            select source_id, id, test_name, id_test, start_utc, finish_utc, id_partners, groups, iteration
            from dblink('myconnUS',
                'select 2 as source_id,
                        id,
                        name as test_name,
                        id_test,
                        start_utc,
                        finish_utc,
                        id_partners,
                        groups,
                        iteration
                 from public.ab_history;') AS ab_history (source_id smallint, id integer, test_name varchar(300), id_test integer, start_utc timestamp, finish_utc timestamp, id_partners integer[], groups json, iteration integer);

            perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_ab_history(integer) owner to rlu;
