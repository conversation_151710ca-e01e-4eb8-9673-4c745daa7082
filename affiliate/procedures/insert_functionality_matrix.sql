create or replace procedure affiliate.insert_functionality_matrix()
    language plpgsql
as
$$
begin
    truncate affiliate.functionality_matrix;

    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.functionality_matrix(functionality, remainder, percent, is_applied)
    select
        functionality,
        remainder,
        percent,
        is_applied
    from
        dblink('myconnNL',
               'select
                   f.name as functionality,
                   fm.remainder,
                   fm.percent,
                   fm.is_applied
               from public.functionality f
                   join public.functionality_matrix fm
                       on f.id = fm.id_functionality') as fm (functionality varchar, remainder integer, percent numeric, is_applied boolean);

    perform dblink_disconnect('myconnNL');

end
$$;

alter procedure affiliate.insert_functionality_matrix() owner to ypr;

