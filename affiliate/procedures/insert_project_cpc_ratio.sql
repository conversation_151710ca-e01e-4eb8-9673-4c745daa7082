create or replace procedure affiliate.insert_project_cpc_ratio(_datediff integer)
    language plpgsql
as
$$
begin

    truncate table affiliate.project_cpc_ratio;

    --connect to NL
    perform dblink_connect('myconnNL',
                           'dbname=affiliate host=********* user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');

    insert into
        affiliate.project_cpc_ratio(source_id, id_local, id_project, cpc_ratio, update_datetime, id_campaign,
                                    id_campaign_cpc_ratio, optimized_cpc_part, reassembled_jobs_part)
    select
        source_id,
        id_local,
        id_project,
        cpc_ratio,
        update_datetime,
        id_campaign,
        id_campaign_cpc_ratio,
        optimized_cpc_part,
        reassembled_jobs_part
    from
        dblink('myconnNL',
               'select
                   1 as source_id,
                   now() as update_datetime,
                   id_local,
                   id_project,
                   cpc_ratio,
                   json_each.key::integer as id_campaign,
                   json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio,
                   optimized_cpc_part,
                   reassembled_jobs_part
               from
                   public.project_cpc_ratio_nl pcr,
                   lateral json_each(pcr.id_campaign_cpc_ratio) json_each(key, value)

               union all

               select
                   1 as source_id,
                   now() as update_datetime,
                   id_local,
                   id_project,
                   cpc_ratio,
                   null::integer as id_campaign,
                   null::numeric(14, 5) as id_campaign_cpc_ratio,
                   optimized_cpc_part,
                   reassembled_jobs_part
               from
                   public.project_cpc_ratio_nl pcr
               where
                   id_campaign_cpc_ratio::text = ''{}''') as partner_settings (source_id integer,
                                                                                 update_datetime timestamp,
                                                                                 id_local integer,
                                                                                 id_project integer,
                                                                                 cpc_ratio numeric(14, 5),
                                                                                 id_campaign integer,
                                                                                 id_campaign_cpc_ratio numeric(14, 5),
                                                                                 optimized_cpc_part numeric,
                                                                                 reassembled_jobs_part numeric);

    perform dblink_disconnect('myconnNL');

    --connect to US
    perform dblink_connect('myconnUS',
                           'dbname=affiliate host=************ user= ''pentaho'' password= ''!faP@jhWYWb&3rwv'' options=-csearch_path=');


    insert into
        affiliate.project_cpc_ratio(source_id, id_local, id_project, cpc_ratio, update_datetime, id_campaign,
                                    id_campaign_cpc_ratio, optimized_cpc_part, reassembled_jobs_part)
    select
        source_id,
        id_local,
        id_project,
        cpc_ratio,
        update_datetime,
        id_campaign,
        id_campaign_cpc_ratio,
        optimized_cpc_part,
        reassembled_jobs_part
    from
        dblink('myconnUS',
               'select
                   2 as source_id,
                   now() as update_datetime,
                   id_local,
                   id_project,
                   cpc_ratio,
                   json_each.key::integer as id_campaign,
                   json_each.value::varchar::numeric(14, 5) as id_campaign_cpc_ratio,
                   optimized_cpc_part,
                   reassembled_jobs_part
               from
                   public.project_cpc_ratio_us pcr,
                   lateral json_each(pcr.id_campaign_cpc_ratio) json_each(key, value)

               union all

               select
                   2 as source_id,
                   now() as update_datetime,
                   id_local,
                   id_project,
                   cpc_ratio,
                   null::integer as id_campaign,
                   null::numeric(14, 5) as id_campaign_cpc_ratio,
                   optimized_cpc_part,
                   reassembled_jobs_part
               from
                   public.project_cpc_ratio_us pcr
               where
                   id_campaign_cpc_ratio::text = ''{}''') as partner_settings (source_id integer,
                                                                                 update_datetime timestamp,
                                                                                 id_local integer,
                                                                                 id_project integer,
                                                                                 cpc_ratio numeric(14, 5),
                                                                                 id_campaign integer,
                                                                                 id_campaign_cpc_ratio numeric(14, 5),
                                                                                 optimized_cpc_part numeric,
                                                                                 reassembled_jobs_part numeric);

    perform dblink_disconnect('myconnUS');


end;

$$;

alter procedure affiliate.insert_project_cpc_ratio(integer) owner to ypr;
