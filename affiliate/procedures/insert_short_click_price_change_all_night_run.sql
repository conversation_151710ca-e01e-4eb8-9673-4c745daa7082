create or replace procedure affiliate.insert_short_click_price_change_all_night_run(_datediff integer)
    language plpgsql
as
$$
begin

            -- source: affiliate.short_click_price_change with full data for today-3 and today-4
            -- select distinct keys on today-4: for these keys info should be updated
            create temporary table temp_last_updated_keys as
            select distinct
                source_id,
                country_code,
                job_uid
            from affiliate.short_click_price_change
            where cast(datetime as date) = current_date - 4;

            -- for selected keys keep last row
            create temporary table temp_click_price_change_upd as
            select distinct on (source_id, country_code, job_uid)
                source_id,
                country_code,
                id,
                job_uid,
                click_price_usd,
                datetime,
                id_campaign
            from affiliate.short_click_price_change
            where cast(datetime as date) = current_date - 4
            order by source_id, country_code, job_uid, datetime desc;

            -- for other keys leave data untouched
            delete from affiliate.short_click_price_change t
                using temp_last_updated_keys luk
            where luk.source_id = t.source_id
              and luk.country_code = t.country_code
              and luk.job_uid = t.job_uid
              and cast(t.datetime as date) <= current_date - 4;

            insert into affiliate.short_click_price_change(source_id, country_code, id, job_uid, click_price_usd, datetime, id_campaign)
            select
                source_id,
                country_code,
                id,
                job_uid,
                click_price_usd,
                datetime,
                id_campaign
            from temp_click_price_change_upd;

            drop table temp_click_price_change_upd;
            drop table temp_last_updated_keys;

end;

$$;

alter procedure affiliate.insert_short_click_price_change_all_night_run(integer) owner to rlu;

