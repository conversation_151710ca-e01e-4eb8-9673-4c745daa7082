declare @date_diff int = {date_diff};

select
    lower(substring(db_name(), 5, 2))                                               as country,
    sei.date_diff,
    cast(sei.id as varchar(100))                                                    as id_external_init,
    cast(se.id as varchar(100))                                                     as id_external,
    iif(s.flags & 1 = 1 or s.id is null or se.flags & 4 = 4 or se.id is null, 1, 0) as is_bot,
    iif(s.id is null or se.flags & 4 = 4 or s.bot_reason & 32 = 32
        or s.bot_reason & 16 = 16 or s.bot_reason & 1 = 1
        or s.bot_reason & 2 = 2 or s.bot_reason & 8 = 8, 1, 0)                      as is_internal_bot_method,
    iif(sc.flags & 4096 = 4096 or sa.flags & 512 = 512, 1, 0)                       as is_internal_duplicated,
    (iif(s.flags & 1 = 1, 1, 0))
          + (iif(s.id is null, 2, 0))
          + (iif(se.flags & 4 = 4, 4, 0))
          + (iif(s.bot_reason & 32 = 32 , 8, 0))
          + (iif(se.id is null, 16, 0))
          + (iif(s.bot_reason & 16 = 16 , 32, 0))
          + (iif(s.bot_reason & 4 = 4 , 256, 0))
          + (iif(s.bot_reason & 1 = 1, 512, 0))
          + (iif(s.bot_reason & 2 = 2, 1024, 0))
          + (iif(s.bot_reason & 8 = 8, 2048, 0))                                    as bot_flags
from dbo.session_external_init sei with (nolock)
left join dbo.session_external se with (nolock)
    on se.date_diff = sei.date_diff
    and se.id_session_external_init = sei.id
left join dbo.session s with (nolock)
    on s.date_diff between sei.date_diff - 1 and sei.date_diff
    and isnull(se.id_session, sei.id_session) = s.id
left join dbo.session_away sa with (nolock)
    on sa.date_diff = se.date_diff
    and sa.id = se.id_away
left join dbo.session_jdp sj with (nolock)
    on sj.date_diff = se.date_diff
    and sj.id = se.id_jdp
left join dbo.session_click sc with (nolock)
    on sc.date_diff = coalesce(sa.date_diff, sj.date_diff)
    and sc.id = coalesce(sa.id_click_no_serp, sj.id_click_no_serp)
where
      (s.flags & 1 = 1
           or s.id is null
           or se.flags & 4 = 4
           or se.id is null
           or sc.flags & 4096 = 4096
           or sa.flags & 512 = 512)
  and sei.date_diff = @date_diff
