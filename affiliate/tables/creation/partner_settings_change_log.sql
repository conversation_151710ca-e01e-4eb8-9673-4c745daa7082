create table affiliate.partner_settings_change_log
(
    source_id       smallint not null,
    id              integer  not null,
    local_id        integer,
    feed_name       varchar(100),
    field_name      varchar(100),
    value_before    varchar,
    value_after     varchar,
    update_datetime timestamp,
    primary key (source_id, id)
);

alter table affiliate.partner_settings_change_log
    owner to postgres;

grant select on affiliate.partner_settings_change_log to readonly;

grant delete, insert, select, truncate, update on affiliate.partner_settings_change_log to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.partner_settings_change_log to pyapi;

grant select on affiliate.partner_settings_change_log to affiliate_iud;
