create table affiliate.dic_bot_reasons
(
    id               int primary key,
    name             varchar(100) not null,
    reason_group     varchar(100) not null
);


insert into affiliate.dic_bot_reasons (id, name, reason_group)
values (1, 'Cloudflare realtime bot detection', 'Cloudflare'),
       (2, 'Instant bot detection', 'Internal'),
       (3, 'Cloudflare logs based', 'Cloudflare'),
       (4, 'Too many actions per ip', 'Internal'),
       (5, 'High session creation velocity per ip', 'Internal'),
       (6, 'Session with away only and without source', 'Internal'),
       (7, 'Phantom session', 'Internal'),
       (8, 'Crawler', 'Internal'),
       (9, 'Pingdom request', 'Internal');

alter table affiliate.dic_bot_reasons
    owner to ypr;

grant select on affiliate.dic_bot_reasons to readonly;

grant delete, insert, select, update on affiliate.dic_bot_reasons to writeonly_pyscripts;


