create table affiliate.partner_settings
(
    id_local_partner        integer not null,
    partner                 varchar(100),
    country                 varchar(10),
    partner_id_currency     integer,
    cpc_ratio               numeric(14, 5),
    min_cpc_in_usd          numeric(14, 5),
    feed_is_only_easy_apply integer,
    feed_is_static_min_cpc  integer,
    run_interval            integer,
    feed_type               varchar(10),
    update_gap              integer,
    id_manager              integer,
    is_rounded_cpc          integer,
    offset_in_hour          integer,
    local_flags             integer,
    worst_cpc_ratio         numeric(14, 5),
    source_id               integer not null,
    update_datetime         timestamp,
    is_api_publisher        smallint,
    optimized_cpc_part      numeric,
    reassembled_jobs_part   numeric,
    max_cnt_jobs            integer,
    global_flags            integer,
    is_cpa_publisher        smallint,
    max_cpc_in_usd          numeric(14, 5),
    primary key (source_id, id_local_partner)
);

alter table affiliate.partner_settings
    owner to postgres;

grant select on affiliate.partner_settings to readonly;

grant delete, insert, references, select, trigger, truncate, update on affiliate.partner_settings to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.partner_settings to pyapi;

grant delete, insert, select, update on affiliate.partner_settings to affiliate_iud;

