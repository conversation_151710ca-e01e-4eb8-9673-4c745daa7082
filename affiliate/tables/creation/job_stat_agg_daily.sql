create table affiliate.job_stat_agg_daily
(
    date                   date    not null,
    id_source              integer not null,
    id_partner             integer not null,
    id_country             integer not null,
    id_project             integer,
    id_category            integer,
    cnt_gather             integer,
    click_price_usd        numeric(14, 5),
    feed_cpc_usd           numeric(14, 5),
    uid_count              bigint,
    reassembled_jobs_count bigint,
    total_jobs_count       bigint,
    avg_click_price_usd    numeric(14, 5),
    avg_feed_cpc_usd       numeric(14, 5)
);

alter table affiliate.job_stat_agg_daily
    owner to ypr;

grant select on affiliate.job_stat_agg_daily to readonly;

grant delete, insert, select, update on affiliate.job_stat_agg_daily to writeonly_pyscripts;