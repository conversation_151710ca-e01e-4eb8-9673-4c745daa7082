create table affiliate.job_stat_agg_abtest
(
    id_job_stat_agg     bigint,
    id_ab_history       bigint,
    test_group          smallint,
    uid_cnt             integer,
    reassembled_uid_cnt integer,
    primary key (id_job_stat_agg, id_ab_history, test_group)

);

alter table affiliate.job_stat_agg_abtest
    owner to ypr;

grant select on affiliate.job_stat_agg_abtest to readonly;

grant delete, insert, select, update on affiliate.job_stat_agg_abtest to writeonly_pyscripts;

