create table affiliate.statistics_daily_raw
(
    date_diff                    integer,
    id_country                   integer,
    partner                      varchar(100),
    user_country                 varchar(10),
    is_bot                       integer,
    uid_job                      bigint,
    id_project                   integer,
    click_datetime               timestamp,
    id_external                  bigint,
    external_flags               integer,
    inactivation_date            timestamp,
    id_away                      bigint,
    id_jdp                       bigint,
    id_jdp_away                  bigint,
    has_away_conversion          integer,
    has_jdp_away_conversion      integer,
    cpc_change_date              timestamp,
    partner_cost                 numeric(14, 5),
    partner_cost_usd             numeric(14, 5),
    feed_cost                    numeric(14, 5),
    feed_cost_usd                numeric(14, 5),
    revenue                      numeric(14, 5),
    revenue_usd                  numeric(14, 5),
    user_ip                      varchar(100),
    is_external_stat_client      smallint,
    id_external_init             bigint,
    id_campaign                  integer,
    is_duplicated                smallint default 0,
    is_internal_duplicated       smallint,
    id_sub_client                varchar(200),
    affiliate_flags              integer,
    is_billable                  smallint,
    last_gather_id_campaign      integer,
    last_gather_datetime         timestamp,
    id_category                  integer,
    redirected_to_uid            bigint,
    click_type                   integer,
    is_project_accepted_location smallint,
    pub_tracking_id              varchar,
    api_request_id               varchar,
    url_extra_flags              integer,
    bot_flags                    integer,
    job_client_inactive_flags    integer,
    job_client_inactive_date     timestamp,
    is_paid_overflow             integer,
    job_title_hash               varchar,
    user_agent_hash              bigint,
    is_duplicated_old_logic      smallint default 0,
    feed_cpa                     numeric(14, 5),
    feed_cpa_usd                 numeric(14, 5),
    publisher_click_id           varchar(200),
    is_apply                     integer,
    apply_revenue_usd            numeric(14, 5),
    client_cpa                   numeric(14, 5),
    client_cpa_usd               numeric(14, 5),
    constraint statistics_daily_raw_id_country_date_diff_id_external_init_i_key
        unique (id_country, date_diff, id_external_init, id_external, id_away, id_jdp, id_jdp_away)
);

alter table affiliate.statistics_daily_raw
    owner to postgres;

grant select on affiliate.statistics_daily_raw to readonly;

grant delete, insert, select, truncate, update on affiliate.statistics_daily_raw to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.statistics_daily_raw to pyapi;

