create table affiliate.data_completeness_report
(
    record_part_number       smallint,
    update_date              date,
    schema                   varchar,
    table_name               varchar,
    record_date              date,
    dimension_name           varchar,
    dimension_value          varchar,
    metric_name              varchar,
    metric_value             numeric,
    reference_metric_value   numeric,
    reference_source         varchar,
    check_type               varchar,
    is_incomplete            boolean,
    max_acceptable_deviation varchar
);

alter table affiliate.data_completeness_report
    owner to ypr;

grant select on affiliate.data_completeness_report to readonly;

grant delete, insert, select, update on affiliate.data_completeness_report to writeonly_pyscripts;