create table affiliate.temp_bot_click
(
    country                smallint,
    date_diff              integer,
    id_external_init       bigint,
    id_external            bigint,
    is_bot                 smallint,
    is_internal_bot_method smallint,
    is_internal_duplicated smallint,
    bot_flags              integer
);

alter table affiliate.temp_bot_click
    owner to postgres;

grant select on affiliate.temp_bot_click to readonly;

grant delete, insert, select, truncate, update on affiliate.temp_bot_click to writeonly_pyscripts;

grant delete, insert, select, truncate, update on affiliate.temp_bot_click to pyapi;

grant select on affiliate.temp_bot_click to affiliate_iud;