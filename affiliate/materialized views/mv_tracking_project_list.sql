create materialized view if not exists affiliate.mv_tracking_project_list as

with
    prepared_new_data as (select
                              t.country_id                                     as id_country,
                              t.id_project,
                              min(t.action_datediff)                           as min_date_diff,
                              max(t.action_datediff)                           as max_date_diff,
                              sum(coalesce(t.unic_client_conversion_count, 0)) as unic_client_conversion_count,
                              sum(coalesce(t.jooble_apply_count, 0))           as jooble_apply_count,
                              sum(revenue_usd)                                 as revenue_usd
                          from
                              aggregation.click_data_agg t
                              join dimension.info_calendar as ic
                                   on ic.date_diff = t.action_datediff
                          where
                                (coalesce(t.unic_client_conversion_count, 0) +
                                 coalesce(t.jooble_apply_count, 0)) > 0
                            and ic.dt >= '2024-06-01'
                            and t.id_project >= 0
                            and t.id_project <= 100000
                          group by
                              t.country_id, t.id_project),
    result as (select
                   t.country_id                                                       as id_country,
                   t.project_id                                                       as id_project,
                   fn_get_date_diff(min(t.session_date)::timestamp without time zone) as min_date_diff,
                   fn_get_date_diff(max(t.session_date)::timestamp without time zone) as max_date_diff,
                   sum(t.conversions)                                                 as conversion_cnt,
                   sum(away_revenue)                                                  as revenue_usd,
                   case
                       when t.metric::text = 'aways'::text then 'Postback'::text
                       when t.metric::text = 'applies'::text
                           and ip.apply_flags & 240 = 240
                           and ip.jdp_flags & 15 = 15
                           and ip.apply_form = true
                           then 'Apply on Jooble'::text
                       when ip.jdp_flags & 15 = 15 and ip.apply_flags & 16 = 16 and ip.apply_form = true
                           then 'Easy Apply'::text
                       else 'Other'::text end                                         as type
               from
                   aggregation.project_conversions_daily t
                   left join dimension.info_project as ip
                             on ip.country = t.country_id and t.project_id = ip.id
               where
                     t.conversions > 0
                 and t.session_date < '2024-06-01'
                 and t.project_id >= 0
                 and t.project_id <= 100000
               group by
                   t.country_id, t.project_id,
                   (case
                        when t.metric::text = 'aways'::text then 'Postback'::text
                        when t.metric::text = 'applies'::text
                            and ip.apply_flags & 240 = 240
                            and ip.jdp_flags & 15 = 15
                            and ip.apply_form = true
                            then 'Apply on Jooble'::text
                        when ip.jdp_flags & 15 = 15 and ip.apply_flags & 16 = 16 and ip.apply_form = true
                            then 'Easy Apply'::text
                        else 'Other'::text end)

               union all

               select
                   t.id_country,
                   t.id_project,
                   min(t.min_date_diff)                   as min_date_diff,
                   max(t.max_date_diff)                   as max_date_diff,
                   sum(case
                           when t.unic_client_conversion_count > 0 then t.unic_client_conversion_count
                           else t.jooble_apply_count end) as conversion_cnt,
                   sum(revenue_usd)                       as revenue_usd,
                   case
                       when unic_client_conversion_count > 0 then 'Postback'
                       when ip.apply_flags & 240 = 240 and ip.jdp_flags & 15 = 15 and ip.apply_form = true
                           then 'Apply on Jooble'::text
                       when ip.jdp_flags & 15 = 15 and ip.apply_flags & 16 = 16 and ip.apply_form = true
                           then 'Easy Apply'::text
                       else 'Other'::text
                       end                                as type
               from
                   prepared_new_data t
                   left join dimension.info_project as ip
                             on ip.country = t.id_country and t.id_project = ip.id
               group by
                   t.id_country, t.id_project,
                   (case
                        when unic_client_conversion_count > 0 then 'Postback'
                        when ip.apply_flags & 240 = 240 and ip.jdp_flags & 15 = 15 and ip.apply_form = true
                            then 'Apply on Jooble'::text
                        when ip.jdp_flags & 15 = 15 and ip.apply_flags & 16 = 16 and ip.apply_form = true
                            then 'Easy Apply'::text
                        else 'Other'::text
                       end))

select
    r.id_country,
    r.id_project,
    min(r.min_date_diff)  as conversion_start_date_diff,
    max(r.max_date_diff)  as conversion_max_date_diff,
    sum(r.conversion_cnt) as conversion_cnt,
    sum(r.revenue_usd)    as revenue_usd,
    r.type
from
    result r
where
    r.type <> 'Other'
group by
    r.id_country, r.id_project, r.type;

alter materialized view affiliate.mv_tracking_project_list owner to ypr;

grant select on affiliate.mv_tracking_project_list to readonly;

grant delete, insert, select, update on affiliate.mv_tracking_project_list to writeonly_pyscripts;


