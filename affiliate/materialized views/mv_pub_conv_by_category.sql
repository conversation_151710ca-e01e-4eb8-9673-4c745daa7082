create materialized view if not exists affiliate.mv_pub_conv_by_category as
with
    conv_raw as (
        select
            cn.alpha_2                                                     as country,
            pcd.country_id,
            pcd.id_project                                                 as project_id,
            uts.name                                                       as traffic_source,
            uts.channel                                                    as traffic_channel,
            sum(coalesce(pcd.jdp_away_count, 0::bigint::double precision)) as aways,
            sum(case
                    when pcd.job_destination = any
                         (array [1::double precision, 4::double precision])
                        then coalesce(pcd.unic_client_conversion_count, 0::bigint)
                    else coalesce(pcd.jooble_apply_count, 0::bigint)
                end)                                                       as conversions,
            ic.dt                                                          as session_date,
            pcd.id_job_category                                            as job_category_id
        from
            aggregation.click_data_agg pcd
            join dimension.info_calendar ic
                 on ic.date_diff = pcd.action_datediff
            join affiliate.mv_tracking_project_list pp
                 on pp.id_country = pcd.country_id and pp.id_project = pcd.id_project and
                    pp.conversion_cnt >= 5::numeric and
                    ic.date_diff >= pp.conversion_start_date_diff and
                    (pp.type = 'Postback'::text and
                     ic.date_diff <= (pp.conversion_max_date_diff + 7) or
                     pp.type <> 'Postback'::text and pp.revenue_usd > 0::double precision) and
                    case
                        when pp.type = 'Postback'::text then pcd.job_destination = any
                                                             (array [1::double precision, 4::double precision])
                        else pcd.job_destination = 3::double precision
                        end
            left join dimension.countries cn
                      on pcd.country_id = cn.id
            left join dimension.u_traffic_source uts
                      on pcd.country_id = uts.country and pcd.id_current_traf_source = uts.id
        where
              ic.dt >= (current_date - 31)
          and ic.dt <= (current_date - 4)
          and (cn.alpha_2::text = any
               (array ['DE'::character varying, 'UK'::character varying, 'FR'::character varying, 'NL'::character varying, 'CA'::character varying, 'US'::character varying]::text[]))
        group by
            pcd.country_id, pcd.id_project, cn.alpha_2, uts.name, uts.channel, ic.dt,
            pcd.id_job_category
    ),
    exclude_all_countries_list as (
        select
            crpl.date_from                       as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text = 'ALL'::text
          and crpl.record_type::text = 'exclude'::text
    ),
    include_countries_list as (
            select
                min(crpl.date_from)                       as min_date,
                max(coalesce(crpl.date_to, current_date)) as max_date,
                crpl.country
            from
                affiliate.conversion_project_list crpl
            where
                  crpl.country::text <> 'ALL'::text
              and crpl.record_type::text = 'include'::text
            group by crpl.country
    ),
    include_project_list as (
        select
            crpl.date_from                                              as min_date,
            coalesce(crpl.date_to, current_date) as max_date,
            crpl.country,
            crpl.id_project
        from
            affiliate.conversion_project_list crpl
        where
              crpl.country::text <> 'ALL'::text
          and crpl.record_type::text = 'include'::text
    ),
    final_conv as (
        select
            vpc.country,
            vpc.country_id,
            vpc.project_id                            as id_project,
            vpc.traffic_source,
            vpc.traffic_channel,
            sum(coalesce(vpc.aways, 0::bigint))       as aways,
            sum(coalesce(vpc.conversions, 0::bigint)) as conversions,
            vpc.session_date                          as record_date,
            case
                when el.id_project is null and (icl.country is null or ipl.id_project is not null) and
                     sum(coalesce(vpc.conversions, 0::bigint)) is not null then 1
                else 0
                end                                   as is_conversion_considered,
            vpc.job_category_id
        from
            conv_raw vpc
            left join exclude_all_countries_list el
                      on el.id_project = vpc.project_id and vpc.session_date >= el.min_date and
                         vpc.session_date < el.max_date
            left join include_countries_list icl
                      on icl.country::text = vpc.country::text and vpc.session_date >= icl.min_date and
                         vpc.session_date < icl.max_date
            left join include_project_list ipl
                      on ipl.country::text = vpc.country::text and ipl.id_project = vpc.project_id and
                         vpc.session_date >= ipl.min_date and vpc.session_date < ipl.max_date
        where
            vpc.job_category_id is not null
        group by
            vpc.country, vpc.country_id, vpc.project_id, vpc.traffic_source, vpc.traffic_channel, vpc.session_date,
            vpc.job_category_id, el.id_project, icl.country, ipl.id_project
    ),
    raw_data as (
        select
            vcd.click_date                           as date,
            vcd.publisher,
            vcd.id_project,
            vcd.id_category,
            sum(vcd.conversion_click_count)::numeric as click_cnt,
            sum(vcd.conversion_count)                as conversion_cnt,
            vcd.country,
            ps.id_local_partner
        from
            affiliate.v_click_data vcd
            left join affiliate.partner_settings ps
                      on ps.partner::text = vcd.publisher::text and ps.country::text = lower(vcd.country::text)
        where
              vcd.is_conversion_considered = 1
          and vcd.click_date >= (current_date - 31)
          and vcd.click_date <= (current_date - 4)
          and (vcd.target_action = any (array ['Apply'::text, 'Click apply'::text]))
          and vcd.id_category is not null
          and (vcd.country::text = any
               (array ['DE'::character varying, 'UK'::character varying, 'FR'::character varying, 'NL'::character varying, 'CA'::character varying, 'US'::character varying]::text[]))
        group by vcd.click_date, vcd.publisher, vcd.id_project, vcd.country, vcd.id_category, ps.id_local_partner
    ),
    prepared_data as (
        select
            d.date,
            d.publisher,
            d.click_cnt,
            d.conversion_cnt,
            d.id_project,
            sum(vpcbs.aways)       as total_clicks,
            sum(vpcbs.conversions) as total_conversions,
            d.country,
            d.id_category,
            case
                when sum(vpcbs.aways) > 0::numeric then sum(vpcbs.conversions) / sum(vpcbs.aways)
                else 0::numeric
                end                as cr_other_sources,
            d.id_local_partner
        from
            raw_data d
            left join final_conv vpcbs
                      on vpcbs.record_date = d.date and d.publisher::text <> vpcbs.traffic_source::text and
                         vpcbs.id_project = d.id_project and d.country::text = vpcbs.country::text and
                         vpcbs.job_category_id = d.id_category
        group by
            d.date, d.publisher, d.country, d.id_project, d.click_cnt, d.conversion_cnt, d.id_category,
            d.id_local_partner
    ),
    final_data as (
        select
            pd.publisher,
            pd.country,
            vjkc.child_category_name as category_name,
            pd.id_category           as category_id,
            sum(pd.click_cnt)        as click_cnt,
            sum(pd.total_clicks)     as total_clicks,
            round(
                    case
                        when sum(pd.click_cnt) > 0::numeric then sum(pd.conversion_cnt) / sum(pd.click_cnt)
                        else 0::numeric
                        end, 4)      as cr,
            round(
                    case
                        when sum(pd.click_cnt) > 0 then (
                                sum(pd.click_cnt * pd.cr_other_sources) / sum(pd.click_cnt))::numeric
                        else 0::numeric
                        end, 4)      as benchmark_cr,
            pd.id_local_partner      as publisher_id
        from
            prepared_data pd
            left join aggregation.v_job_kaiju_category vjkc
                      on vjkc.child_category_id = pd.id_category
        group by pd.publisher, pd.country, vjkc.child_category_name, pd.id_category, pd.id_local_partner
        having
              sum(pd.click_cnt) >= 150
          and sum(pd.total_clicks) >= 100
    )
select
    fd.publisher,
    fd.publisher_id,
    fd.country,
    fd.category_name,
    fd.category_id,
    fd.click_cnt,
    fd.cr,
    fd.benchmark_cr,
    fd.total_clicks,
    case
        when fd.benchmark_cr > 0::numeric then
            case
                when (fd.cr / fd.benchmark_cr - 1.0) <= '-0.25'::numeric then '-1'::integer
                when (fd.cr / fd.benchmark_cr - 1.0) >= 0.25 then 1
                else 0
                end
        else 0
        end           as rule,
    current_date - 31 as min_date,
    current_date - 4  as max_date
from
    final_data fd
where fd.publisher_id is not null;

alter materialized view affiliate.mv_pub_conv_by_category
    owner to ypr;

grant select on affiliate.mv_pub_conv_by_category to readonly;

grant delete, insert, select, update on affiliate.mv_pub_conv_by_category to writeonly_pyscripts;

