create or replace function affiliate.create_click_report(bool_condition_var boolean, publisher_var character varying, date_start_var date,
                                    date_end_var date)
    returns TABLE
            (
                click_id              character varying,
                publisher             character varying,
                country_code          text,
                id_project            integer,
                uid_job               character varying,
                cpc_partner_curr      numeric,
                cpc_usd               numeric,
                click_type            character varying,
                click_datetime        timestamp without time zone,
                user_ip               character varying,
                cpc_change_datetime   timestamp without time zone,
                job_inactive_datetime timestamp without time zone,
                publisher_sub_source  character varying,
                is_apply              integer,
                user_country          character varying,
                pub_tracking_id       character varying,
                target_action         text,
                job_title_hash        character varying,
                user_agent_hash       bigint,
                publisher_click_id    character varying,
                bot_reason            character varying,
                bot_reason_group      character varying
            )
    language plpgsql
as
$$
declare
    _publisher       varchar = publisher_var;
    _date_diff_start int     := public.fn_get_date_diff(date_start_var);
    _date_diff_end   int     := public.fn_get_date_diff(date_end_var);
    _bool_condition  bool    := bool_condition_var;

begin

    if _bool_condition = false then
        return query
            select
                cast(a.id_click as varchar(50))           as click_id,
                a.publisher                               as publisher,
                lower(a.country)                          as country_code,
                coalesce(a.feed_id_project, a.id_project) as id_project,
                cast(a.uid_job as varchar(50))            as uid_job,
                a.feed_cpc_publisher_currency             as cpc_partner_curr,
                a.feed_cpc_usd                            as cpc_usd,
                a.click_type,
                a.click_datetime                          as click_datetime,
                a.user_ip                                 as user_ip,
                a.cpc_change_datetime,
                a.job_inactive_datetime,
                a.publisher_sub_source,
                0                                         as is_apply,
                a.user_country                            as user_country,
                a.pub_tracking_id,
                a.target_action,
                a.job_title_hash,
                a.user_agent_hash,
                a.publisher_click_id,
                a.bot_reason,
                a.bot_reason_group
            from
                affiliate.v_click_data a
            where
                  a.publisher = _publisher
              and a.date_diff between _date_diff_start and _date_diff_end;
    elsif _bool_condition = true then
        return query
            select
                cast(a.id_click as varchar(50)) as click_id,
                a.publisher                     as publisher,
                lower(a.country)                as country_code,
                a.id_project,
                cast(a.uid_job as varchar(50))  as uid_job,
                a.feed_cpc_publisher_currency   as cpc_partner_curr,
                a.feed_cpc_usd                  as cpc_usd,
                a.click_type,
                a.click_datetime                as click_datetime,
                a.user_ip                       as user_ip,
                a.cpc_change_datetime,
                a.job_inactive_datetime,
                a.publisher_sub_source,
                case
                    when a.conversion_count > 0 then 1
                    else 0 end                  as is_apply,
                a.user_country                  as user_country,
                a.pub_tracking_id,
                a.target_action,
                a.job_title_hash,
                a.user_agent_hash,
                a.publisher_click_id,
                a.bot_reason,
                a.bot_reason_group
            from
                affiliate.v_click_data a
            where
                  a.conversion_click_count > 0
              and a.is_conversion_considered = 1
              and a.publisher = _publisher
              and a.date_diff between _date_diff_start and _date_diff_end;
    end if;

end;
$$;

alter function affiliate.create_click_report(boolean, varchar, date, date) owner to ypr;

grant execute on function affiliate.create_click_report(boolean, varchar, date, date) to readonly;

