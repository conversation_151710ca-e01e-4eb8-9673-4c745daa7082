create or replace function affiliate.get_functionality_alert()
    returns TABLE
            (
                country_code                    character varying,
                publisher                       character varying,
                id_project                      integer,
                project_name                    character varying,
                functionality_type              character varying,
                applied_group_profit            numeric,
                default_group_profit            numeric,
                profit_diff                     numeric
            )
    language plpgsql
as
$$
begin

    drop table if exists temp_click_data;
    drop table if exists temp_partner_settings_log;
    drop table if exists temp_project_settings_log;
    drop table if exists temp_click_result;
    drop table if exists temp_click_result_optimized;
    drop table if exists temp_click_result_reassembled;
    drop table if exists temp_agg_data_optimized;
    drop table if exists temp_agg_data_reassembled;


    create temporary table temp_click_data as
    select
        t.publisher,
        lower(t.country)                                                                 as country_code,
        t.id_project,
        ip.name                                                                          as project_name,
        coalesce(t.client_cpc_usd, 0::numeric)                                           as client_cpc_usd,
        case when t.click_type = 'certified' then coalesce(t.feed_cpc_usd, 0) else 0 end as certified_feed_cpc_usd,
        case
            when (t.affiliate_flags & 8) = 8 then 1
            else 0
            end                                                                          as is_optimal_cpc_ratio,
        case
            when (t.affiliate_flags & 16) = 16 then 1
            else 0
            end                                                                          as is_reassembled_job,
        t.feed_gather_datetime,
        ps.source_id,
        ps.id_local_partner,
        ps.reassembled_jobs_part                                                         as publisher_reassembled_jobs_part,
        ps.optimized_cpc_part                                                            as publisher_optimized_cpc_part,
        t.url_extra_flags
    from
        affiliate.v_click_data t
        join affiliate.partner_settings ps
             on ps.country::text = lower(t.country::text) and ps.partner::text = t.publisher::text
        left join dimension.info_project ip
                  on ip.country = t.id_country and ip.id = t.id_project
    where
          t.click_date between date_trunc('week', current_date)::date - 7 and date_trunc('week', current_date)::date - 1
      and ps.is_api_publisher = 0
      and ps.is_cpa_publisher = 0
      and not (t.is_redirected_click = 1 and coalesce(t.feed_id_project, 0) != coalesce(t.id_project, 0));

    create temporary table temp_partner_settings_log as
    select
        vpscl.source_id,
        vpscl.local_id,
        vpscl.min_gather_dt,
        vpscl.max_gather_dt,
        vpscl.field_name,
        vpscl.field_value
    from
        affiliate.v_partner_settings_change_log vpscl
    where
        vpscl.field_name in ('optimized_cpc_part', 'reassembled_jobs_part');

    create temporary table temp_project_settings_log as
    select
        vpcrcl.source_id,
        vpcrcl.local_id,
        vpcrcl.project_id,
        vpcrcl.min_gather_dt,
        vpcrcl.max_gather_dt,
        vpcrcl.field_name,
        vpcrcl.field_value
    from
        affiliate.v_project_cpc_ratio_change_log vpcrcl
    where
        vpcrcl.field_name in ('optimized_cpc_part', 'reassembled_jobs_part');

    create temporary table temp_click_result as
    select
        t.publisher,
        t.country_code,
        t.id_project,
        t.project_name,
        coalesce(t.certified_feed_cpc_usd, 0)                              as certified_cost_usd,
        coalesce(t.client_cpc_usd, 0::numeric)                             as total_revenue,
        t.is_optimal_cpc_ratio,
        t.is_reassembled_job,
        coalesce(pr_cpc.field_value, par_cpc.field_value)                  as optimized_cpc_part,
        case
            when t.url_extra_flags & 256 = 256 then 0
            else coalesce(pr_jobad.field_value, par_jobad.field_value) end as reassembled_jobs_part
    from
        temp_click_data t
        left join temp_partner_settings_log par_cpc
                  on t.source_id = par_cpc.source_id and
                     t.id_local_partner = par_cpc.local_id and
                     t.feed_gather_datetime >= par_cpc.min_gather_dt and
                     t.feed_gather_datetime < par_cpc.max_gather_dt and
                     par_cpc.field_name::text = 'optimized_cpc_part'::text
        left join temp_project_settings_log pr_cpc
                  on t.source_id = pr_cpc.source_id and
                     t.id_local_partner = pr_cpc.local_id and
                     t.id_project = pr_cpc.project_id and
                     t.feed_gather_datetime >= pr_cpc.min_gather_dt and
                     t.feed_gather_datetime < pr_cpc.max_gather_dt and
                     pr_cpc.field_name::text = 'optimized_cpc_part'::text
        left join temp_partner_settings_log par_jobad
                  on t.source_id = par_jobad.source_id and
                     t.id_local_partner = par_jobad.local_id and
                     t.feed_gather_datetime >= par_jobad.min_gather_dt and
                     t.feed_gather_datetime < par_jobad.max_gather_dt and
                     par_jobad.field_name::text = 'reassembled_jobs_part'::text
        left join temp_project_settings_log pr_jobad
                  on t.source_id = pr_jobad.source_id and
                     t.id_local_partner = pr_jobad.local_id and
                     t.id_project = pr_jobad.project_id and
                     t.feed_gather_datetime >= pr_jobad.min_gather_dt and
                     t.feed_gather_datetime < pr_jobad.max_gather_dt and
                     pr_jobad.field_name::text = 'reassembled_jobs_part'::text
    where
        (coalesce(pr_cpc.field_value, par_cpc.field_value) > 0::numeric or
         coalesce(pr_jobad.field_value, par_jobad.field_value) > 0::numeric);

    create temporary table temp_click_result_optimized as
    select
        tcr.country_code::varchar   as country_code,
        tcr.publisher::varchar      as publisher,
        tcr.id_project,
        tcr.project_name,
        tcr.is_optimal_cpc_ratio,
        tcr.optimized_cpc_part,
        case
            when coalesce(tcr.optimized_cpc_part, 0::numeric) = 0::numeric then null::numeric
            when tcr.is_optimal_cpc_ratio = 1 then tcr.optimized_cpc_part
            else 1::numeric - tcr.optimized_cpc_part
            end                     as optimizer_job_part,
        sum(tcr.total_revenue)      as total_revenue,
        sum(tcr.certified_cost_usd) as certified_cost
    from
        temp_click_result tcr
    where
        optimized_cpc_part > 0
    group by
        tcr.country_code, tcr.publisher, tcr.id_project, tcr.project_name,
        tcr.is_optimal_cpc_ratio, tcr.optimized_cpc_part,
        (
            case
                when coalesce(tcr.optimized_cpc_part, 0::numeric) = 0::numeric then null::numeric
                when tcr.is_optimal_cpc_ratio = 1 then tcr.optimized_cpc_part
                else 1::numeric - tcr.optimized_cpc_part
                end);

    create temporary table temp_click_result_reassembled as
    select
        tcr.country_code::varchar   as country_code,
        tcr.publisher::varchar      as publisher,
        tcr.id_project,
        tcr.project_name,
        tcr.is_reassembled_job,
        tcr.reassembled_jobs_part,
        case
            when coalesce(tcr.reassembled_jobs_part, 0::numeric) = 0::numeric
                then null::numeric
            when tcr.is_reassembled_job = 1
                then tcr.reassembled_jobs_part
            else 1::numeric - tcr.reassembled_jobs_part
            end                     as reassembler_job_part,
        sum(tcr.total_revenue)      as total_revenue,
        sum(tcr.certified_cost_usd) as certified_cost
    from
        temp_click_result tcr
    where
        reassembled_jobs_part > 0
    group by
        tcr.country_code, tcr.publisher, tcr.id_project, tcr.project_name,
        tcr.is_reassembled_job, tcr.reassembled_jobs_part,
        (
            case
                when coalesce(tcr.reassembled_jobs_part, 0::numeric) = 0::numeric then null::numeric
                when tcr.is_reassembled_job = 1 then tcr.reassembled_jobs_part
                else 1::numeric - tcr.reassembled_jobs_part
                end);

    create temporary table temp_agg_data_optimized as
    select
        t.country_code,
        t.publisher,
        t.id_project,
        t.project_name,
        sum(case when t.is_optimal_cpc_ratio = 1 then t.estimated_profit_optimized else 0 end) as applied_group_profit,
        sum(case when t.is_optimal_cpc_ratio = 0 then t.estimated_profit_optimized else 0 end) as default_group_profit,
        sum(case when t.is_optimal_cpc_ratio = 1 then t.estimated_profit_optimized else 0 end) -
        sum(case when t.is_optimal_cpc_ratio = 0 then t.estimated_profit_optimized else 0 end) as profit_diff
    from
        (
            select
                tcro.country_code,
                tcro.publisher,
                tcro.id_project,
                tcro.project_name,
                tcro.is_optimal_cpc_ratio,
                (tcro.total_revenue / tcro.optimizer_job_part) * tcro.optimized_cpc_part -
                (tcro.certified_cost / tcro.optimizer_job_part) * tcro.optimized_cpc_part
                    as estimated_profit_optimized
            from
                temp_click_result_optimized tcro
        ) t
    group by
        t.country_code,
        t.publisher,
        t.id_project,
        t.project_name;

    create temporary table temp_agg_data_reassembled as
    select
        t.country_code,
        t.publisher,
        t.id_project,
        t.project_name,
        sum(case when t.is_reassembled_job = 1 then t.estimated_profit_reassembled else 0 end) as applied_group_profit,
        sum(case when t.is_reassembled_job = 0 then t.estimated_profit_reassembled else 0 end) as default_group_profit,
        sum(case when t.is_reassembled_job = 1 then t.estimated_profit_reassembled else 0 end) -
        sum(case when t.is_reassembled_job = 0 then t.estimated_profit_reassembled else 0 end) as profit_diff
    from
        (
            select
                tcrr.country_code,
                tcrr.publisher,
                tcrr.id_project,
                tcrr.project_name,
                tcrr.is_reassembled_job,
                (tcrr.total_revenue / tcrr.reassembler_job_part) * tcrr.reassembled_jobs_part -
                (tcrr.certified_cost / tcrr.reassembler_job_part) * tcrr.reassembled_jobs_part
                    as estimated_profit_reassembled
            from
                temp_click_result_reassembled tcrr
        ) t
    group by
        t.country_code,
        t.publisher,
        t.id_project,
        t.project_name;

    return query
        select
            t1.country_code,
            t1.publisher,
            t1.id_project,
            t1.project_name,
            'CPC ratio optimization'::varchar as functionality_type,
            t1.applied_group_profit,
            t1.default_group_profit,
            t1.profit_diff
        from
            temp_agg_data_optimized t1
        where
            t1.profit_diff <= -50

        union all

        select
            t2.country_code,
            t2.publisher,
            t2.id_project,
            t2.project_name,
            'Job reassembling'::varchar as functionality_type,
            t2.applied_group_profit,
            t2.default_group_profit,
            t2.profit_diff
        from
            temp_agg_data_reassembled t2
        where
            t2.profit_diff <= -50;

    drop table if exists temp_click_data;
    drop table if exists temp_partner_settings_log;
    drop table if exists temp_project_settings_log;
    drop table if exists temp_click_result;
    drop table if exists temp_click_result_optimized;
    drop table if exists temp_click_result_reassembled;
    drop table if exists temp_agg_data_optimized;
    drop table if exists temp_agg_data_reassembled;

end
$$;

alter function affiliate.get_functionality_alert() owner to ypr;