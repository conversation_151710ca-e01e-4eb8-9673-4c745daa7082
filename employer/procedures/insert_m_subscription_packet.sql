create or replace procedure employer.insert_m_subscription_packet(_datediff integer)
    language plpgsql
as
$$
    begin

            truncate table employer.m_subscription_packet;

            insert into employer.m_subscription_packet(database_source_id, employer_id, subscription_id, subscription_status, packet_price,
                                                       packet_id, subscription_month_cnt, packet_jcoin_cnt, subscription_order_datetime,
                                                       subscription_last_order_datetime, packet_type_id, subscription_expiring_datetime,
                                                       subscription_fact_expiring_datetime, subscription_plan_expiring_datetime,
                                                       subscription_paid_period_end_datetime, payment_id, payment_rank, payment_start_datetime,
                                                       payment_end_datetime, packet_rank, packet_type_paid_result_id, packet_start_datetime,
                                                       packet_next_start_datetime, packet_end_datetime, packet_plan_end_datetime,
                                                       packet_cont_period_start_datetime, packet_cont_period_end_datetime,
                                                       packet_cont_period_rank, has_payment_subscription, packet_name, payment_method)
            select database_source_id, employer_id, subscription_id, subscription_status, packet_price, packet_id, subscription_month_cnt,
                   packet_jcoin_cnt, subscription_order_datetime, subscription_last_order_datetime, packet_type_id, subscription_expiring_datetime,
                   subscription_fact_expiring_datetime, subscription_plan_expiring_datetime, subscription_paid_period_end_datetime,
                   payment_id, payment_rank, payment_start_datetime, payment_end_datetime, packet_rank, packet_type_paid_result_id,
                   packet_start_datetime, packet_next_start_datetime, packet_end_datetime, packet_plan_end_datetime, packet_cont_period_start_datetime,
                   packet_cont_period_end_datetime, packet_cont_period_rank, has_payment_subscription, packet_name, payment_method
            from employer.v_subscription_packet;

    end;

$$;

alter procedure employer.insert_m_subscription_packet(integer) owner to rlu;
