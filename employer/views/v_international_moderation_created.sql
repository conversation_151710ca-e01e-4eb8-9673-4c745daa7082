create or replace view employer.v_international_moderation_created(statistics_type, created_date, day_of_week, day_hour, created_cnt) as
select
    'employer'                                                                            as statistics_type,
    (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date                as created_date,
    date_part('isodow', (sem.date_created at time zone 'utc' at time zone 'europe/kiev')) as day_of_week,
    date_part('hour', (sem.date_created at time zone 'utc' at time zone 'europe/kiev'))   as day_hour,
    count(distinct concat(sem.sources, '_', sem.id_employer))                             as created_cnt
from
    imp_employer.statistics_employer_moderation sem
    join imp_employer.employer e
         on e.sources = sem.sources and e.id = sem.id_employer
where
      sem.moderation_status = 0
  and sem.action_type = 0
  and e.country_code not in ('ua', 'hu', 'ro')
  and (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date between '2022-11-01' and current_date - 1
group by
    (sem.date_created at time zone 'utc' at time zone 'europe/kiev')::date,
    date_part('isodow', (sem.date_created at time zone 'utc' at time zone 'europe/kiev')),
    date_part('hour', (sem.date_created at time zone 'utc' at time zone 'europe/kiev'))

union all

select
    'job'                                                                                 as statistics_type,
    (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date                as created_date,
    date_part('isodow', (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')) as day_of_week,
    date_part('hour', (sjm.date_created at time zone 'utc' at time zone 'europe/kiev'))   as day_hour,
    count(distinct concat(sjm.sources, '_', sjm.id_job))                                  as created_cnt
from
    imp_employer.statistics_job_moderation sjm
    join imp_employer.job j
         on j.sources = sjm.sources and j.id = sjm.id_job
    join imp_employer.employer e
         on e.sources = j.sources and j.id_employer = e.id
where
      sjm.moderation_status = 0
  and sjm.action_type = 0
  and e.country_code not in ('ua', 'hu', 'ro')
  and (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date between '2022-11-01' and current_date - 1
group by
    (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')::date,
    date_part('isodow', (sjm.date_created at time zone 'utc' at time zone 'europe/kiev')),
    date_part('hour', (sjm.date_created at time zone 'utc' at time zone 'europe/kiev'));

grant select on employer.v_international_moderation_created to ypr, user_agg_team;
