create or replace view employer.v_subscription_packet_info
            (database_source_id, employer_id, subscription_id, packet_id, subscription_order_datetime, packet_price,
             currency_id, jcoin_cnt, subscription_month_cnt, packet_name, packet_short_name, subscription_period_id,
             subscription_status, packet_jcoins_available, subscription_last_order_datetime,
             packet_jcoins_refilled_datetime, subscription_expiring_datetime, has_payment_subscription,
             subscription_plan_expiring_datetime, subscription_fact_expiring_datetime,
             subscription_paid_period_end_datetime, payment_method)
as
WITH subscription_expiring AS (
    SELECT b.database_source_id,
           b.subscription_id,
           max(b.subscription_fact_expired_datetime) AS subscription_fact_expired_datetime
    FROM (SELECT ps_1.sources     AS database_source_id,
                 ps_1.id_employer AS employer_id,
                 ps_1.id          AS subscription_id,
                 ps_1.status,
                 CASE
                     WHEN ps_1.status = 0 THEN ps_1.order_date
                     WHEN vse.event_rank = first_value(vse.event_rank)
                                           OVER (PARTITION BY vse.employer_id, vse.subscription_id ORDER BY vse.event_rank DESC)
                         THEN lead(vse.event_datetime) OVER (PARTITION BY vse.employer_id ORDER BY vse.event_rank)
                     ELSE NULL::timestamp without time zone
                     END          AS subscription_fact_expired_datetime
          FROM imp_employer.packet_subscription ps_1
                   LEFT JOIN employer.v_subscription_event vse
                             ON vse.database_source_id = ps_1.sources AND vse.subscription_id = ps_1.id AND
                                CASE
                                    WHEN vse.is_previous_subscription = false AND
                                         vse.is_first_event_of_subscription = false THEN vse.event_type <> 5
                                    ELSE vse.event_type <> ALL (ARRAY [4, 5])
                                    END
          WHERE vse.subscription_id <> 204619
            AND ps_1.sources = 1) b
    GROUP BY b.database_source_id, b.subscription_id
),
     packet_with_price AS (
         SELECT p.id,
                p.name,
                p.short_name,
                p.points,
                p.price,
                p.is_active,
                p.is_recommended,
                p.icon,
                p."order",
                p.hire_min,
                p.hire_max,
                p.sources,
                CASE
                    WHEN p.id_currency = 21 THEN round(p.price / cs.to_eur, 4)::numeric(10, 4)
                    ELSE p.price
                    END AS packet_price_converted,
                CASE
                    WHEN p.id_currency = 21 THEN 19
                    ELSE p.id_currency::integer
                    END AS id_currency
         FROM imp_employer.packet p
                  LEFT JOIN imp_employer.info_currency ic ON p.sources = 1 AND p.id_currency = ic.id
                  JOIN imp_statistic.currency_source cs
                       ON ic.sources = 1 AND ic.iso_code::bpchar = cs.currency AND cs.date = (CURRENT_DATE - 1)
         WHERE p.sources = 1
     )
SELECT ps.sources                                                                                          AS database_source_id,
       ps.id_employer                                                                                      AS employer_id,
       ps.id                                                                                               AS subscription_id,
       p.id                                                                                                AS packet_id,
       ps.order_date                                                                                       AS subscription_order_datetime,
       (p.packet_price_converted * (100.00 - pp.discount) / 100::numeric)::numeric(10, 2)                  AS packet_price,
       p.id_currency                                                                                       AS currency_id,
       p.points                                                                                            AS jcoin_cnt,
       pp.month_count                                                                                      AS subscription_month_cnt,
       p.name                                                                                              AS packet_name,
       p.short_name                                                                                        AS packet_short_name,
       ps.id_period                                                                                        AS subscription_period_id,
       ps.status                                                                                           AS subscription_status,
       ps.points_available                                                                                 AS packet_jcoins_available,
       ps.last_order_date                                                                                  AS subscription_last_order_datetime,
       NULL::timestamp without time zone                                                                   AS packet_jcoins_refilled_datetime,
       COALESCE(se.subscription_fact_expired_datetime, ps.last_order_date +
                                                       ((pp.month_count::text || ' mon'::text)::interval)) AS subscription_expiring_datetime,
       CASE
           WHEN subscription_paid.id_subscription IS NOT NULL THEN 1
           ELSE 0
           END                                                                                             AS has_payment_subscription,
       ps.last_order_date +
       ((pp.month_count::text || ' mon'::text)::interval)                                                  AS subscription_plan_expiring_datetime,
       se.subscription_fact_expired_datetime                                                               AS subscription_fact_expiring_datetime,
       CASE
           WHEN subscription_paid.id_subscription IS NOT NULL THEN
               CASE
                   WHEN ps.status = ANY (ARRAY [1, 3, 5]) THEN ps.last_order_date
                   WHEN ps.status = ANY (ARRAY [4]) THEN se.subscription_fact_expired_datetime
                   ELSE NULL::timestamp without time zone
                   END
           ELSE NULL::timestamp without time zone
           END                                                                                             AS subscription_paid_period_end_datetime,
       subscription_paid.payment_method
FROM imp_employer.packet_subscription ps
         JOIN packet_with_price p ON ps.sources = p.sources AND ps.id_packet = p.id
         JOIN imp_employer.packet_period pp ON pp.sources = ps.sources AND pp.id = ps.id_period
         LEFT JOIN (SELECT so.sources,
                           so.id_subscription,
                           max(ba.payment_method) AS payment_method
                    FROM imp_employer.subscription_order so
                             LEFT JOIN imp_employer.billing_agreement ba
                                       ON ba.sources = so.sources AND ba.id = so.id_agreement AND
                                          ba.payment_method IS NOT NULL
                    WHERE so.status = 2
                      AND so.date_refunded IS NULL
                    GROUP BY so.sources, so.id_subscription) subscription_paid
                   ON ps.sources = subscription_paid.sources AND ps.id = subscription_paid.id_subscription
         LEFT JOIN subscription_expiring se ON ps.sources = se.database_source_id AND ps.id = se.subscription_id
WHERE se.subscription_id <> 204619
  AND ps.sources = 1
UNION ALL
SELECT 1::smallint                                        AS database_source_id,
       63411                                              AS employer_id,
       204619                                             AS subscription_id,
       9::smallint                                        AS packet_id,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_order_datetime,
       7000.00::numeric(10, 2)                            AS packet_price,
       17::smallint                                       AS currency_id,
       400                                                AS jcoin_cnt,
       1                                                  AS subscription_month_cnt,
       'Large 400'::character varying(1048)               AS packet_name,
       'L'::character varying(50)                         AS packet_short_name,
       1                                                  AS subscription_period_id,
       4::smallint                                        AS subscription_status,
       0                                                  AS packet_jcoins_available,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_last_order_datetime,
       NULL::timestamp without time zone                  AS packet_jcoins_refilled_datetime,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_expiring_datetime,
       1                                                  AS has_payment_subscription,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_plan_expiring_datetime,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_fact_expiring_datetime,
       '2021-09-06 13:02:19'::timestamp without time zone AS subscription_paid_period_end_datetime,
       NULL::smallint                                     AS payment_method;

alter table employer.v_subscription_packet_info
    owner to postgres;

grant select on employer.v_subscription_packet_info to readonly;

grant select on employer.v_subscription_packet_info to readonly_employer;

grant select on employer.v_subscription_packet_info to "pavlo.kvasnii";

