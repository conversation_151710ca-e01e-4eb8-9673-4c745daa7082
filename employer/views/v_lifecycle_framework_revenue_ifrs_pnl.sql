create or replace view employer.v_lifecycle_framework_revenue_ifrs_pnl
            (database_source_id, country_code, employer_id, subscription_id, payment_id, packet_name, event_datetime,
             event_type, stage, revenue, revenue_euro)
as
WITH a AS (
    SELECT vsp.database_source_id,
           e.country_code,
           vsp.employer_id,
           vsp.subscription_id,
           vsp.packet_rank,
           vsp.packet_start_datetime,
           vsp.packet_end_datetime,
           vsp.packet_name,
           vsp.packet_price,
           vsp.subscription_month_cnt,
           vsp.payment_id,
           vsp.packet_start_datetime = first_value(vsp.packet_start_datetime)
                                       OVER (PARTITION BY vsp.employer_id ORDER BY vsp.packet_start_datetime) AS is_first_paid_packet,
           vsp.packet_start_datetime = vsp.payment_start_datetime                                             AS is_first_packet_in_payment,
           lead(vsp.packet_start_datetime)
           OVER (PARTITION BY vsp.employer_id ORDER BY vsp.packet_start_datetime)                             AS next_paid_packet_datetime
    FROM employer.v_subscription_packet vsp
             LEFT JOIN imp_employer.employer e ON vsp.employer_id = e.id AND vsp.database_source_id = e.sources
    WHERE vsp.packet_type_paid_result_id = 1
),
     pre_stage AS (
         SELECT a.database_source_id,
                a.country_code,
                a.employer_id,
                a.subscription_id,
                a.packet_rank,
                a.payment_id,
                a.packet_name,
                a.packet_price,
                a.subscription_month_cnt,
                CASE
                    WHEN a.next_paid_packet_datetime IS NULL AND
                         age((CURRENT_DATE - 1)::timestamp without time zone, a.packet_end_datetime) < '10 days'::interval
                        THEN a.packet_end_datetime
                    ELSE a.packet_end_datetime + '10 days'::interval
                    END       AS event_datetime,
                CASE
                    WHEN a.next_paid_packet_datetime IS NULL AND
                         age((CURRENT_DATE - 1)::timestamp without time zone, a.packet_end_datetime) < '10 days'::interval
                        THEN 4
                    ELSE 3
                    END       AS event_type,
                NULL::boolean AS is_first_paid_packet,
                NULL::boolean AS is_first_packet_in_payment
         FROM a
         WHERE date(a.packet_end_datetime) <= (CURRENT_DATE - 1)
           AND (age(a.next_paid_packet_datetime, a.packet_end_datetime) >= '10 days'::interval OR
                a.next_paid_packet_datetime IS NULL)
         UNION ALL
         SELECT a.database_source_id,
                a.country_code,
                a.employer_id,
                a.subscription_id,
                a.packet_rank,
                a.payment_id,
                a.packet_name,
                a.packet_price,
                a.subscription_month_cnt,
                a.packet_start_datetime AS event_datetime,
                1                       AS event_type,
                a.is_first_paid_packet,
                a.is_first_packet_in_payment
         FROM a
     ),
     stage AS (
         SELECT pre_stage.database_source_id,
                pre_stage.country_code,
                pre_stage.employer_id,
                pre_stage.subscription_id,
                pre_stage.packet_rank,
                pre_stage.packet_name,
                pre_stage.packet_price,
                pre_stage.subscription_month_cnt,
                CASE
                    WHEN pre_stage.event_type <> ALL (ARRAY [3, 4]) THEN pre_stage.payment_id
                    ELSE NULL::integer
                    END AS payment_id,
                pre_stage.event_datetime,
                pre_stage.event_type,
                CASE
                    WHEN pre_stage.event_type = 3 THEN 'dormant'::text
                    WHEN pre_stage.event_type = 4 THEN 'temporary-dormant'::text
                    ELSE
                        CASE
                            WHEN pre_stage.is_first_paid_packet = true THEN 'new'::text
                            ELSE
                                CASE
                                    WHEN lag(pre_stage.event_type)
                                         OVER (PARTITION BY pre_stage.employer_id ORDER BY pre_stage.event_datetime) = 3
                                        THEN 'resurrected'::text
                                    ELSE
                                        CASE
                                            WHEN lag(pre_stage.event_type)
                                                 OVER (PARTITION BY pre_stage.employer_id ORDER BY pre_stage.event_datetime) =
                                                 1 THEN
                                                CASE
                                                    WHEN pre_stage.is_first_packet_in_payment = true THEN 'current'::text
                                                    ELSE 'auto-current'::text
                                                    END
                                            ELSE NULL::text
                                            END
                                    END
                            END
                    END AS stage
         FROM pre_stage
     )
SELECT stage.database_source_id,
       stage.country_code,
       stage.employer_id,
       stage.subscription_id,
       stage.payment_id,
       stage.packet_name,
       stage.event_datetime,
       stage.event_type,
       stage.stage,
       CASE
           WHEN stage.stage = ANY (ARRAY ['dormant'::text, 'temporary-dormant'::text]) THEN stage.packet_price
           ELSE round(vpr.payment_without_vat_uah / count(vpr.subscription_id)
                                                    OVER (PARTITION BY vpr.employer_id, vpr.subscription_id, vpr.payment_id)::numeric,
                      2)
           END AS revenue,
       CASE
           WHEN stage.stage = ANY (ARRAY ['dormant'::text, 'temporary-dormant'::text]) THEN stage.packet_price
           ELSE round(vpr.payment_without_vat_euro / count(vpr.subscription_id)
                                                     OVER (PARTITION BY vpr.employer_id, vpr.subscription_id, vpr.payment_id)::numeric,
                      2)
           END AS revenue_euro
FROM stage
         LEFT JOIN employer.v_payment_revenue vpr
                   ON stage.database_source_id = vpr.database_source_id AND stage.payment_id = vpr.payment_id;

alter table employer.v_lifecycle_framework_revenue_ifrs_pnl
    owner to ypi;

grant select on employer.v_lifecycle_framework_revenue_ifrs_pnl to readonly;

grant select on employer.v_lifecycle_framework_revenue_ifrs_pnl to readonly_employer;

grant select on employer.v_lifecycle_framework_revenue_ifrs_pnl to "pavlo.kvasnii";

