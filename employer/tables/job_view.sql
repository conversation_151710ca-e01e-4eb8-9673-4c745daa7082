select sj.country as country_id,
       sj.date_diff as jdp_viewed_datediff,
       e.id as employer_id,
       uid.id_job as job_id,
       count(distinct sj.id) as view_cnt
from imp.session_jdp sj
join imp_employer.job_to_uid_mapping uid on sj.uid_job = uid.uid_job and uid.sources = 1
join imp_employer.job j on j.sources = uid.sources and uid.id_job = j.id
join imp_employer.employer e on e.sources = uid.sources and j.id_employer = e.id
where sj.country = 1 and uid.sources = 1 and sj.date_diff between 44376 and 44467 and job_id_project = -1
group by sj.country,
         sj.date_diff,
         e.id,
         uid.id_job;
