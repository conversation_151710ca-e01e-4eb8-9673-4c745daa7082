select pa.country_id,
       apply_datediff,
       uid.id_job as job_id,
       e.id as employer_id,
       count(distinct profile_id) as apply_cnt
from profile.profile_apply pa
join imp_employer.job_to_uid_mapping uid on pa.job_uid = uid.uid_job
join imp_employer.employer_account ea on ea.sources = uid.sources and ea.id_account = pa.employer_account_id
join imp_employer.employer e on e.sources = uid.sources and e.id = ea.id_employer
where pa.country_id = 1
  and uid.sources = 1
  and e.country_code = 'ua'
  and apply_datediff between 44376 /*2021-07-01*/ and 44467
group by pa.country_id,
         apply_datediff,
         uid.id_job,
         e.id;
