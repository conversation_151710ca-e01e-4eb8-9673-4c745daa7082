create table employer.subscription_packet_spent as
select database_source_id,
       subscription_id,
       packet_rank,
       employer_id,
       min(utilization_datediff) as jcoin_spent_datediff
from employer.v_jcoin_running_utilization_daily
where packet_jcoin_cnt = jcoin_running_utilized_cnt
group by database_source_id,
       subscription_id,
       packet_rank,
       employer_id;

alter table employer.subscription_packet_spent
	add constraint subscription_packet_spent_pk
		primary key (database_source_id, subscription_id, packet_rank);
