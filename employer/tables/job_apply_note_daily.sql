create table employer.job_apply_note_daily as
select j.sources                                                               as database_source_id,
       j.id_employer                                                           as employer_id,
       j.id_account                                                            as employer_account_id,
       j.id                                                                    as job_id,

       fn_get_date_diff(jan.date_created)                                      as action_datediff,
       count(distinct case when ja.flags & 1024 <> 1024 then jan.id_apply end) as apply_note_cnt,
       count(distinct case when ja.flags & 1024 = 1024 then jan.id_apply end)  as reccomendation_note_cnt
from imp_employer.job j
     left join imp_employer.job_apply ja
     on j.sources = ja.sources
         and j.id = ja.id_job
     join      imp_employer.job_apply_note jan
     on ja.sources = jan.sources
         and ja.id = jan.id_apply
where j.sources = 1
  and fn_get_date_diff(jan.date_created) >= 44407
  and j.id_employer in (344240)
group by database_source_id, employer_id, employer_account_id, job_id, action_datediff;


alter table employer.job_apply_note_daily
    add primary key (database_source_id, action_datediff, job_id);
