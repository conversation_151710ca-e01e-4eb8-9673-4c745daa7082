with j_apply AS (
        SELECT distinct job_apply.id_job,
                job_apply.id as id_apply,
                cast(job_apply.date as date) AS date_reciev_apply,
                CASE
                       WHEN (job_apply.flags & 16) = 16 THEN 1
                       ELSE 0
                       END AS is_premium,
                   CASE
                       WHEN job_apply.date_seen IS NOT NULL THEN 1
                       ELSE 0
                       END AS is_seen,
                   CASE
                       WHEN job_apply.date_seen IS NOT NULL
                           THEN EXTRACT(DAY FROM date_seen-date)
                       ELSE 0
                       END AS day_on_seen,
                   CASE
                       WHEN job_apply.flags & 1024!=1024 THEN 0
                       ELSE 1
                       END  AS is_reccomendation,
                    name,
                   job_apply.sources
            FROM imp_employer.job_apply
            WHERE cast(job_apply.date as date) >= '2020-12-01'
              -- с начала рекомендаций
                and sources = 1
        ),
        itc as (
        select distinct employer_account_id,
               apply_id,
               database_source_id
        from employer.apply_intention_to_contact
        where database_source_id = 1 and action_datediff >= 44164 -- '2020-12-01'
        ),
        employer as (
        -- в одного работодателя может быть несколько аккаунтов, поэтому distinct
        select distinct employer_id,
               sources
        from retention_ea.stg_employer_related_account
        where country_code = 'UA' and sources = 1)

        select employer_id,
               id_job as job_id,
               date_reciev_apply as receiv_apply_date,
               is_premium,
               is_seen,
               day_on_seen,
               is_reccomendation,
               case when itc.apply_id is not null then 1 else 0 end as has_interaction,
               count(id_apply) as apply_cnt,
               j_apply.sources as database_source_id
        from j_apply
        left join itc on itc.database_source_id = j_apply.sources and itc.apply_id = j_apply.id_apply
        inner join imp_employer.job j on j_apply.id_job=j.id and j_apply.sources=j.sources
        inner join employer on employer.sources=j.sources and employer.employer_id=j.id_employer
        group by employer_id,
                 id_job,
                 date_reciev_apply,
                 is_premium,
                 is_seen,
                 day_on_seen,
                 is_reccomendation,
                 case when itc.apply_id is not null then 1 else 0 end,
                 j_apply.sources;
