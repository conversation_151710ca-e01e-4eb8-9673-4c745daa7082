create table employer.job_packet as
select msp.database_source_id,
       msp.subscription_id,
       msp.packet_rank,
       msp.employer_id,
       msp.packet_start_datetime,
       msp.packet_end_datetime,
       j.id                         as job_id,
       min(js.date_diff)            as first_impression_datediff,
       max(js.date_diff)            as last_impression_datediff,
       count(distinct js.date_diff) as job_active_day_cnt
from imp_employer.job_statistics_detailed js
     join imp_employer.job j
     on js.sources = j.sources and
        js.id_job = j.id

     -- визначимо, яка підписка була активна в цей день
     join employer.subscription_active_date sda
     on sda.database_source_id = j.sources
            and sda.employer_id = j.id_employer
            and js.date_diff = sda.date_diff

     -- визначимо, який пакет підписки був активним в цей день
     join employer.m_subscription_packet msp
     on msp.database_source_id = sda.database_source_id and msp.employer_id = sda.employer_id
         and msp.subscription_id = sda.subscription_id
         and
        js.date_diff between fn_get_date_diff(msp.packet_start_datetime)
                         and fn_get_date_diff(msp.packet_end_datetime)
group by msp.database_source_id,
         msp.subscription_id,
         msp.packet_rank,
         msp.employer_id,
         msp.packet_start_datetime,
         msp.packet_end_datetime,
         j.id;

alter table employer.job_packet
    owner to dap;

grant select on employer.job_packet to readonly;

alter table employer.job_packet
    add constraint job_packet_pk
        primary key (database_source_id, subscription_id, packet_rank, job_id)
;
