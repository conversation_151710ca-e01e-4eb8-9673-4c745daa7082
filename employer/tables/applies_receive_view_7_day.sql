with j as (
    select id_employer,
           id as job_id,
           sources
    from imp_employer.job
    where date_created >= date_trunc('day', NOW() - interval '18 month')
),
ja as (
    select id_job,
           id as apply_id,
           cast(date as date) as apply_date,
           case when abs(extract(epoch from date - date_seen)/3600)/24<=7 then id end as applies_seen_in_7_days,
           case when (flags & 1024 != 1024) then 0 else 1 end as is_reccomendation,
           sources
    from imp_employer.job_apply
    where date >= date_trunc('day', NOW() - interval '18 month')
    group by id_job,
             id,
             cast(date as date),
             case when (flags & 1024 != 1024) then 0 else 1 end,
             sources
),
ea as (
    select id as employer_id,
           upper(country_code) as country_code,
           sources
    from imp_employer.employer
    where country_code='ua'
)
select ea.country_code,
       coalesce(dim_c.name_country_eng, '-99') as country_name,
       ja.apply_date,
       count(applies_seen_in_7_days) as applies_seen_in_7_days,
       count(ja.apply_id) as applies,
       is_reccomendation,
       ja.sources
from ja
inner join j on ja.id_job = j.job_id and ja.sources = j.sources
inner join ea on ea.employer_id = j.id_employer and ea.sources = j.sources
left join dimension.countries dim_c on ea.country_code = upper(dim_c.alpha_2)
group by ea.country_code,
         coalesce(dim_c.name_country_eng, '-99'),
         ja.apply_date,
         is_reccomendation,
         ja.sources;
