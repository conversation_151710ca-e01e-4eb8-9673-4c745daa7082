WITH employer_daily_data AS (
    SELECT DISTINCT profile_base_jcoin_model_daily.database_source_id,
                    profile_base_jcoin_model_daily.action_datediff,
                    profile_base_jcoin_model_daily.employer_id,
                    profile_base_jcoin_model_daily.subscription_id,
                    profile_base_jcoin_model_daily.packet_rank
    FROM employer.profile_base_jcoin_model_daily
    where profile_base_jcoin_model_daily.database_source_id = 1
    UNION
    SELECT DISTINCT apply_received_jcoin_model_daily.database_source_id,
                    apply_received_jcoin_model_daily.recieved_datediff,
                    apply_received_jcoin_model_daily.employer_id,
                    apply_received_jcoin_model_daily.subscription_id,
                    apply_received_jcoin_model_daily.packet_rank
    FROM employer.apply_received_jcoin_model_daily
    where apply_received_jcoin_model_daily.database_source_id = 1
    UNION
    SELECT DISTINCT apply_viewed_jcoin_model_daily.database_source_id,
                    apply_viewed_jcoin_model_daily.viewed_datediff,
                    apply_viewed_jcoin_model_daily.employer_id,
                    apply_viewed_jcoin_model_daily.subscription_id,
                    apply_viewed_jcoin_model_daily.packet_rank
    FROM employer.apply_viewed_jcoin_model_daily
    where apply_viewed_jcoin_model_daily.database_source_id = 1
    UNION
    SELECT DISTINCT database_source_id,
                    action_datediff,
                    employer_id,
                    subscription_id,
                    packet_rank
    FROM employer.m_message_sent_jcoin_model
    where m_message_sent_jcoin_model.database_source_id = 1
    UNION
    SELECT DISTINCT call_jcoin_model_daily.database_source_id,
                    call_jcoin_model_daily.action_datediff,
                    call_jcoin_model_daily.employer_id,
                    call_jcoin_model_daily.subscription_id,
                    call_jcoin_model_daily.packet_rank
    FROM employer.call_jcoin_model_daily
    where call_jcoin_model_daily.database_source_id = 1
    UNION
    SELECT DISTINCT database_source_id,
                    revenue_datediff,
                    employer_id,
                    subscription_id,
                    packet_rank
    FROM employer.jcoin_model_revenue_pnl
    where database_source_id = 1
      and jcoin_model_revenue_pnl.revenue_datediff < fn_get_date_diff(current_date)
    UNION
    SELECT DISTINCT database_source_id,
                    action_datediff,
                    employer_id,
                    subscription_id,
                    packet_rank
    FROM employer.m_jcoin_utilized
    where m_jcoin_utilized.database_source_id = 1
)
SELECT emd.database_source_id,
       e.country_code,
       emd.subscription_id,
       emd.packet_rank,
       emd.action_datediff,
       emd.employer_id,
       sum(COALESCE(ar.apply_cnt, 0::bigint))                                       AS apply_cnt,
       sum(COALESCE(av.apply_profile_viewed_cnt, 0::bigint))                        AS apply_profile_viewed_cnt,
       sum(
               COALESCE(vju.apply_profile_open_contact_free_packet_cnt, 0::bigint)) AS apply_profile_open_contact_free_packet_cnt,
       sum(
               COALESCE(vju.apply_profile_open_contact_paid_packet_cnt, 0::bigint)) AS apply_profile_open_contact_paid_packet_cnt,
       sum(COALESCE(ms.apply_profile_message_cnt, 0::bigint))                       AS apply_profile_message_cnt,
       sum(COALESCE(vju.apply_revenue, 0::numeric))                                 AS apply_revenue,
       sum(COALESCE(ar.digital_recruiter_profile_cnt, 0::bigint))                   AS digital_recruiter_profile_cnt,
       sum(COALESCE(av.digital_recruiter_profile_viewed_cnt, 0::bigint))            AS digital_recruiter_profile_viewed_cnt,
       sum(COALESCE(vju.digital_recruiter_profile_open_contact_free_packet_cnt,
                    0::bigint))                                                     AS digital_recruiter_profile_open_contact_free_packet_cnt,
       sum(COALESCE(vju.digital_recruiter_profile_open_contact_paid_packet_cnt,
                    0::bigint))                                                     AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       sum(COALESCE(ms.digital_recruiter_profile_message_cnt, 0::bigint))           AS digital_recruiter_profile_message_cnt,
       sum(COALESCE(vju.digital_recruiter_revenue, 0::numeric))                     AS digital_recruiter_revenue,
       sum(COALESCE(p.search_cnt, 0::bigint))                                       AS profile_base_search_cnt,
       sum(COALESCE(p.profile_viewed_cnt, 0::bigint))                               AS profile_base_profile_viewed_cnt,
       sum(COALESCE(vju.profile_base_profile_open_contact_free_packet_cnt,
                    0::bigint))                                                     AS profile_base_profile_open_contact_free_packet_cnt,
       sum(COALESCE(vju.profile_base_profile_open_contact_paid_packet_cnt,
                    0::bigint))                                                     AS profile_base_profile_open_contact_paid_packet_cnt,
       sum(COALESCE(p.profile_message_cnt, 0::bigint))                              AS profile_base_profile_message_cnt,
       sum(COALESCE(vju.profile_base_revenue, 0::numeric))                          AS profile_base_revenue,
       sum(COALESCE(cj.call_cnt, 0::bigint))                                        AS call_cnt,
       sum(COALESCE(cj.call_answered_cnt, 0))                                       AS call_answered_cnt,
       sum(COALESCE(vju.call_answered_30_sec_free_packet_cnt, 0::bigint))           AS call_answered_30_sec_free_packet_cnt,
       sum(COALESCE(vju.call_answered_30_sec_paid_packet_cnt, 0::bigint))           AS call_answered_30_sec_paid_packet_cnt,
       sum(COALESCE(vju.call_revenue, 0::numeric))                                  AS call_revenue,
       sum(COALESCE(vsp.revenue_without_vat_uah, 0::numeric))                       AS revenue_pnl_uah,
       sum(COALESCE(
               CASE
                   WHEN fn_get_date_diff(vsp.payment_datetime) = vsp.revenue_datediff THEN vsp.payment_without_vat_uah
                   ELSE NULL::numeric
                   END, 0::numeric))                                                AS revenue_cashflow_uah

FROM employer_daily_data emd
         JOIN imp_employer.employer e
              ON emd.database_source_id = e.sources
                  and emd.employer_id = e.id
         LEFT JOIN employer.apply_received_jcoin_model_daily ar
                   ON e.sources = ar.database_source_id and e.country_code = ar.country_code and
                      ar.recieved_datediff = emd.action_datediff AND
                      ar.subscription_id = emd.subscription_id and ar.packet_rank = emd.packet_rank
         LEFT JOIN employer.apply_viewed_jcoin_model_daily av
                   ON e.sources = av.database_source_id and e.country_code = av.country_code and
                      av.viewed_datediff = emd.action_datediff
                       AND av.subscription_id = emd.subscription_id
                       and av.packet_rank = emd.packet_rank
         LEFT JOIN employer.m_message_sent_jcoin_model ms
                   ON e.sources = ms.database_source_id and e.country_code = ms.country_code and
                      ms.action_datediff = emd.action_datediff
                       AND ms.subscription_id = emd.subscription_id
                       and ms.packet_rank = emd.packet_rank
         LEFT JOIN employer.profile_base_jcoin_model_daily p
                   ON e.sources = p.database_source_id and e.country_code = p.country_code and
                      emd.action_datediff = p.action_datediff AND
                      emd.subscription_id = p.subscription_id
                       and p.packet_rank = emd.packet_rank
         LEFT JOIN employer.call_jcoin_model_daily cj
                   ON e.sources = cj.database_source_id and e.country_code = cj.country_code and
                      cj.action_datediff = emd.action_datediff AND
                      cj.subscription_id = emd.subscription_id
                       and cj.packet_rank = emd.packet_rank
         LEFT JOIN employer.jcoin_model_revenue_pnl vsp
                   ON e.sources = vsp.database_source_id AND vsp.revenue_datediff = emd.action_datediff AND
                      vsp.revenue_datediff < fn_get_date_diff(current_date) and
                      vsp.subscription_id = emd.subscription_id
                       and vsp.packet_rank = emd.packet_rank
         LEFT JOIN employer.m_jcoin_utilized vju
                   ON e.sources = vju.database_source_id AND e.country_code = vju.country_code AND
                      vju.action_datediff = emd.action_datediff AND
                      vju.subscription_id = emd.subscription_id AND
                      vju.packet_rank = emd.packet_rank
where e.country_code = 'ua'
  and emd.database_source_id = 1
GROUP BY emd.database_source_id, e.country_code, emd.action_datediff, emd.employer_id, emd.subscription_id,
         emd.packet_rank;
