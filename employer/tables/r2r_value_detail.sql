CREATE OR REPLACE PROCEDURE employer.insert_r2r_value_detail()
    LANGUAGE plpgsql
AS
$$
BEGIN

    --===============
    TRUNCATE TABLE employer.r2r_value_detail;
    --===============


    CREATE TEMP TABLE tmp_m_subscription_packet AS
    SELECT *
    FROM employer.m_subscription_packet;
    CREATE INDEX idx_tmp_m_subscription_packet_idx ON tmp_m_subscription_packet (database_source_id, employer_id,
                                                                                 subscription_id, packet_rank,
                                                                                 packet_name);

    CREATE TEMP TABLE tmp_jcoin_utilized_and_revenue_daily AS
    SELECT *
    FROM employer.jcoin_utilized_and_revenue_daily;
    CREATE INDEX idx_tmp_jcoin_utilized_and_revenue_daily ON tmp_jcoin_utilized_and_revenue_daily (database_source_id,
                                                                                                   employer_id,
                                                                                                   subscription_id,
                                                                                                   packet_rank,
                                                                                                   action_datediff);

    CREATE TEMP TABLE tmp_packet_name AS
        (SELECT DISTINCT m_subscription_packet.database_source_id,
                         m_subscription_packet.employer_id,
                         m_subscription_packet.subscription_id,
                         m_subscription_packet.packet_rank,
                         m_subscription_packet.packet_name
         FROM tmp_m_subscription_packet m_subscription_packet);


    CREATE TEMP TABLE tmp_currency_data AS
        (SELECT fn_get_date_diff(info_currency_history.date) AS action_datediff,
                AVG(info_currency_history.value_to_usd)      AS to_usd
         FROM dimension.info_currency_history
         WHERE info_currency_history.country = 1
           AND info_currency_history.id_currency = 0
         GROUP BY (fn_get_date_diff(info_currency_history.date)));

    CREATE TEMP TABLE tmp_packet_with_r2r AS
        (SELECT p.database_source_id,
                p.employer_id,
                p.subscription_id,
                p.packet_rank,
                fn_get_date_diff(po.created_on)       AS action_datediff,
                COALESCE(COUNT(po.job_id), 0::bigint) AS r2r_order_cnt
         FROM tmp_m_subscription_packet p
                  JOIN imp_statistic.profilecrm_order po
                       ON p.employer_id = po.employer_id AND
                          po.created_on >= p.packet_start_datetime AND
                          po.created_on <= p.packet_end_datetime
         GROUP BY p.database_source_id, p.employer_id, p.subscription_id,
                  p.packet_rank,
                  (fn_get_date_diff(po.created_on)));

    CREATE TEMP TABLE tmp_packet_subscription_with_r2r AS
        (SELECT p.database_source_id,
                p.employer_id,
                p.subscription_id,
                p.packet_rank
         FROM tmp_m_subscription_packet p
                  JOIN imp_statistic.profilecrm_order po
                       ON p.employer_id = po.employer_id AND
                          po.created_on >= p.packet_start_datetime AND
                          po.created_on <= p.packet_end_datetime
         GROUP BY p.database_source_id, p.employer_id, p.subscription_id,
                  p.packet_rank);

    CREATE TEMP TABLE tmp_utilization_r2r AS
        (SELECT pp.database_source_id,
                pp.employer_id,
                pp.packet_rank,
                fn_get_date_diff(td.action_datetime)                    AS action_datediff,
                COALESCE(SUM(td.apply_r2r_open_contact_cnt), 0::bigint) AS apply_r2r_open_contact_cnt
         FROM tmp_m_subscription_packet pp
                  JOIN employer.open_contact_by_feature td
                       ON pp.subscription_id = td.subscription_id AND
                          td.action_datetime >= pp.packet_start_datetime AND
                          td.action_datetime <= pp.packet_end_datetime
         WHERE td.apply_r2r_open_contact_cnt > 0
         GROUP BY pp.database_source_id, pp.employer_id, pp.packet_rank,
                  (fn_get_date_diff(td.action_datetime)));

    CREATE TEMP TABLE tmp_result AS
    SELECT ur.database_source_id,
           ur.country_code,
           ur.subscription_id,
           ur.packet_rank,
           ur.action_datediff,
           ur.employer_id,
           pn.packet_name,
           ur.apply_cnt,
           ur.apply_profile_viewed_cnt,
           ur.apply_profile_message_cnt,
           ur.apply_profile_open_contact_jcoin_cnt,
           CASE
               WHEN ur.action_datediff < 45229
                   THEN ur.apply_profile_open_contact_real_jcoin_cnt
               ELSE ur.apply_profile_open_contact_cnt * 10
           END                                               AS apply_profile_open_contact_real_jcoin_cnt,
           ur.apply_revenue_uah,
           ur.apply_revenue_uah * cd.to_usd                  AS apply_revenue_usd,
           ur.apply_revenue_eur,
           ur.digital_recruiter_profile_cnt,
           ur.digital_recruiter_profile_message_cnt,
           ur.digital_recruiter_profile_open_contact_jcoin_cnt,
           ur.digital_recruiter_profile_open_contact_real_jcoin_cnt,
           ur.digital_recruiter_revenue_uah,
           ur.digital_recruiter_revenue_uah * cd.to_usd      AS digital_recruiter_revenue_usd,
           ur.digital_recruiter_revenue_eur,
           ur.profile_base_search_cnt,
           ur.profile_base_profile_viewed_cnt,
           ur.profile_base_profile_message_cnt,
           ur.profile_base_profile_open_contact_jcoin_cnt,
           ur.profile_base_profile_open_contact_real_jcoin_cnt,
           ur.profile_base_revenue_uah,
           ur.profile_base_revenue_uah * cd.to_usd           AS profile_base_revenue_usd,
           ur.profile_base_revenue_eur,
           ur.revenue_pnl_uah,
           ur.revenue_pnl_uah * cd.to_usd                    AS revenue_pnl_usd,
           ur.revenue_pnl_eur,
           ur.revenue_cashflow_uah,
           ur.revenue_cashflow_uah * cd.to_usd               AS revenue_cashflow_usd,
           ur.revenue_cashflow_eur,
           ur.apply_profile_open_contact_cnt,
           ur.digital_recruiter_profile_open_contact_cnt,
           ur.profile_base_profile_open_contact_cnt,
           ur.call_open_contact_cnt,
           ur.call_jcoin_cnt,
           ur.call_real_jcoin_cnt,
           ur.call_revenue_uah,
           ur.call_revenue_uah * cd.to_usd                   AS call_revenue_usd,
           ur.call_revenue_eur,
           CASE
               WHEN psr2r.database_source_id IS NOT NULL THEN 1
               ELSE 0
           END                                               AS is_packet_with_r2r,
           COALESCE(SUM(ur2r.apply_r2r_open_contact_cnt), 0) AS apply_r2r_open_contact_cnt,
           COALESCE(SUM(pr2r.r2r_order_cnt), 0)              AS r2r_order_cnt
    FROM tmp_jcoin_utilized_and_revenue_daily ur
             JOIN tmp_packet_name pn
                  ON ur.database_source_id = pn.database_source_id AND ur.employer_id = pn.employer_id AND
                     ur.subscription_id = pn.subscription_id AND ur.packet_rank = pn.packet_rank
             JOIN tmp_currency_data cd
                  ON ur.action_datediff = cd.action_datediff
             LEFT JOIN tmp_packet_with_r2r pr2r
                       ON ur.database_source_id = pr2r.database_source_id AND ur.employer_id = pr2r.employer_id AND
                          ur.subscription_id = pr2r.subscription_id AND ur.packet_rank = pr2r.packet_rank AND
                          ur.action_datediff = pr2r.action_datediff
             LEFT JOIN tmp_packet_subscription_with_r2r psr2r
                       ON ur.database_source_id = psr2r.database_source_id AND ur.employer_id = psr2r.employer_id AND
                          ur.subscription_id = psr2r.subscription_id AND ur.packet_rank = psr2r.packet_rank
             LEFT JOIN tmp_utilization_r2r ur2r
                       ON ur.database_source_id = ur2r.database_source_id AND ur.employer_id = ur2r.employer_id AND
                          ur.action_datediff = ur2r.action_datediff AND ur.packet_rank = ur2r.packet_rank
    GROUP BY ur.database_source_id, ur.country_code, ur.subscription_id, ur.packet_rank, ur.action_datediff,
             ur.employer_id,
             pn.packet_name, ur.apply_cnt, ur.apply_profile_viewed_cnt, ur.apply_profile_message_cnt,
             ur.apply_profile_open_contact_jcoin_cnt,
             CASE
                 WHEN ur.action_datediff < 45229 THEN ur.apply_profile_open_contact_real_jcoin_cnt
                 ELSE ur.apply_profile_open_contact_cnt * 10
             END,
             ur.apply_revenue_uah,
             (ur.apply_revenue_uah * cd.to_usd), ur.apply_revenue_eur, ur.digital_recruiter_profile_cnt,
             ur.digital_recruiter_profile_message_cnt, ur.digital_recruiter_profile_open_contact_jcoin_cnt,
             ur.digital_recruiter_profile_open_contact_real_jcoin_cnt, ur.digital_recruiter_revenue_uah,
             (ur.digital_recruiter_revenue_uah * cd.to_usd), ur.digital_recruiter_revenue_eur,
             ur.profile_base_search_cnt,
             ur.profile_base_profile_viewed_cnt, ur.profile_base_profile_message_cnt,
             ur.profile_base_profile_open_contact_jcoin_cnt, ur.profile_base_profile_open_contact_real_jcoin_cnt,
             ur.profile_base_revenue_uah, (ur.profile_base_revenue_uah * cd.to_usd), ur.profile_base_revenue_eur,
             ur.revenue_pnl_uah, (ur.revenue_pnl_uah * cd.to_usd), ur.revenue_pnl_eur, ur.revenue_cashflow_uah,
             (ur.revenue_cashflow_uah * cd.to_usd), ur.revenue_cashflow_eur, ur.apply_profile_open_contact_cnt,
             ur.digital_recruiter_profile_open_contact_cnt, ur.profile_base_profile_open_contact_cnt,
             ur.call_open_contact_cnt, ur.call_jcoin_cnt, ur.call_real_jcoin_cnt, ur.call_revenue_uah,
             (ur.call_revenue_uah * cd.to_usd), ur.call_revenue_eur,
             (CASE
                  WHEN psr2r.database_source_id IS NOT NULL THEN 1
                  ELSE 0
              END);

    CREATE TEMP TABLE tmp_m_jcoin_utilized_and_revenue_daily_and_r2r AS
    SELECT *
    FROM tmp_result;

    CREATE TEMP TABLE tmp_revenue_pnl AS
        (SELECT country_code,
                is_packet_with_r2r,
                employer_id,
                subscription_id,
                packet_name,
                packet_rank,
                action_datediff,
                SUM(digital_recruiter_profile_open_contact_real_jcoin_cnt) AS digital_recruiter_profile_open_contact_real_jcoin_cnt,
                SUM(apply_profile_open_contact_real_jcoin_cnt)             AS apply_profile_open_contact_real_jcoin_cnt,
                SUM(profile_base_profile_open_contact_real_jcoin_cnt)      AS profile_base_profile_open_contact_real_jcoin_cnt,
                SUM(r2r_order_cnt)                                         AS r2r_order_cnt,
                SUM(apply_r2r_open_contact_cnt)                            AS apply_r2r_open_contact_cnt,
                SUM(revenue_pnl_usd)                                       AS revenue_pnl_usd
         FROM tmp_m_jcoin_utilized_and_revenue_daily_and_r2r
         GROUP BY 1, 2, 3, 4, 5, 6, 7);

    CREATE TEMP TABLE tmp_utilization AS
        (SELECT is_packet_with_r2r,
                employer_id,
                subscription_id,
                packet_name,
                packet_rank,
                COALESCE(SUM(apply_r2r_open_contact_cnt) * 60 +
                         SUM(digital_recruiter_profile_open_contact_real_jcoin_cnt) +
                         SUM(profile_base_profile_open_contact_real_jcoin_cnt)
                             + SUM(apply_profile_open_contact_real_jcoin_cnt),
                         0)                                                AS open_contact_value_r2r_60_real_jcoin_cnt,
                SUM(apply_r2r_open_contact_cnt) * 60                       AS apply_r2r_open_contact_real_jcoin_cnt,
                SUM(digital_recruiter_profile_open_contact_real_jcoin_cnt) AS digital_recruiter_profile_open_contact_real_jcoin_cnt,
                SUM(profile_base_profile_open_contact_real_jcoin_cnt)      AS profile_base_profile_open_contact_real_jcoin_cnt,
                SUM(apply_profile_open_contact_real_jcoin_cnt)             AS apply_profile_open_contact_real_jcoin_cnt
         FROM tmp_m_jcoin_utilized_and_revenue_daily_and_r2r
         GROUP BY 1, 2, 3, 4, 5
         ORDER BY 6 DESC);

    CREATE TEMP TABLE tmp_utilization_detail AS
        (SELECT utilization.is_packet_with_r2r,
                utilization.employer_id,
                utilization.subscription_id,
                utilization.packet_name,
                utilization.packet_rank,
                utilization.open_contact_value_r2r_60_real_jcoin_cnt,
                CASE WHEN open_contact_value_r2r_60_real_jcoin_cnt = 0 THEN 0 ELSE 1 END AS is_packet_with_utilized,
                CASE
                    WHEN open_contact_value_r2r_60_real_jcoin_cnt = 0 THEN 0
                    ELSE
                        apply_r2r_open_contact_real_jcoin_cnt /
                        open_contact_value_r2r_60_real_jcoin_cnt::numeric
                END                                                                      AS r2r_prc,
                CASE
                    WHEN open_contact_value_r2r_60_real_jcoin_cnt = 0 THEN 0
                    ELSE
                        digital_recruiter_profile_open_contact_real_jcoin_cnt /
                        open_contact_value_r2r_60_real_jcoin_cnt::numeric
                END                                                                      AS dr_prc,
                CASE
                    WHEN open_contact_value_r2r_60_real_jcoin_cnt = 0 THEN 0
                    ELSE
                        profile_base_profile_open_contact_real_jcoin_cnt /
                        open_contact_value_r2r_60_real_jcoin_cnt::numeric
                END                                                                      AS pb_prc,
                CASE
                    WHEN open_contact_value_r2r_60_real_jcoin_cnt = 0 THEN 0
                    ELSE
                        apply_profile_open_contact_real_jcoin_cnt /
                        open_contact_value_r2r_60_real_jcoin_cnt::numeric
                END                                                                      AS apply_prc
         FROM tmp_utilization utilization);

    CREATE TEMP TABLE tmp_open_contact_value AS
        (SELECT revenue_pnl.country_code,
                revenue_pnl.is_packet_with_r2r,
                revenue_pnl.employer_id,
                revenue_pnl.subscription_id,
                revenue_pnl.packet_name,
                revenue_pnl.packet_rank,
                revenue_pnl.action_datediff,
                is_packet_with_utilized,
                digital_recruiter_profile_open_contact_real_jcoin_cnt,
                apply_profile_open_contact_real_jcoin_cnt,
                profile_base_profile_open_contact_real_jcoin_cnt,
                r2r_order_cnt,
                apply_r2r_open_contact_cnt,
                revenue_pnl_usd             AS revenue_pnl_usd,
                revenue_pnl_usd * r2r_prc   AS r2r_rev_usd,
                revenue_pnl_usd * apply_prc AS apply_rev_usd,
                revenue_pnl_usd * dr_prc    AS dr_rev_usd,
                revenue_pnl_usd * pb_prc    AS pb_rev_usd
         FROM tmp_revenue_pnl revenue_pnl
                  JOIN tmp_utilization_detail utilization_detail
                       USING (is_packet_with_r2r, employer_id, subscription_id,
                              packet_name,
                              packet_rank));

    INSERT INTO employer.r2r_value_detail
    SELECT *
    FROM tmp_open_contact_value;


    DROP TABLE IF EXISTS tmp_m_subscription_packet;
    DROP TABLE IF EXISTS tmp_jcoin_utilized_and_revenue_daily;
    DROP TABLE IF EXISTS tmp_packet_name;
    DROP TABLE IF EXISTS tmp_currency_data;
    DROP TABLE IF EXISTS tmp_packet_with_r2r;
    DROP TABLE IF EXISTS tmp_packet_subscription_with_r2r;
    DROP TABLE IF EXISTS tmp_utilization_r2r;
    DROP TABLE IF EXISTS tmp_result;
    DROP TABLE IF EXISTS tmp_m_jcoin_utilized_and_revenue_daily_and_r2r;
    DROP TABLE IF EXISTS tmp_revenue_pnl;
    DROP TABLE IF EXISTS tmp_utilization;
    DROP TABLE IF EXISTS tmp_utilization_detail;
    DROP TABLE IF EXISTS tmp_open_contact_value;


END;
$$;
