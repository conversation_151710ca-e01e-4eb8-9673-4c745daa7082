-- !! цю таблицю не можна перезаписувати!
-- Писати варто щодня
with vacancy as (
    select e.country_code,
           fn_get_date_diff(current_date - 1)                                      as date_diff, /*вчорашня дата*/
           count(distinct j.id)                                                    as vacancies_cnt,
           count(distinct case when j.phones is not null then j.id end)            as vacancies_with_phone_cnt,
           count(distinct case
                              when fn_get_date_diff(j.date_created) = fn_get_date_diff(current_date - 1)
                                  then j.id end)                                   as new_vacancies_cnt,
           count(distinct case
                              when j.flags & 1 = 1
                                  then j.id end)                                   as vacancies_publish_cnt,
           count(distinct
                 case when j.phones is not null and j.flags & 1 = 1 then j.id end) as vacancies_publish_with_phone_cnt
    from imp_employer.job j
             join imp_employer.employer e
                  on j.sources = e.sources
                      and j.id_employer = e.id
    where e.country_code in ('ua', 'hu') /* only for UA + HU employer country code*/
      and j.sources = 1 /* only for 1 sources */
    group by e.country_code, fn_get_date_diff(current_date - 1)
),

     jdp as (
         select e.country_code                                                as country_code,
                s.date_diff                                                   as date_diff,
                count(distinct sj.id)                                         as dte_jdp_cnt,
                count(distinct case when j.phones is not null then sj.id end) as dte_jdp_with_phone_view_cnt,
                count(distinct case
                                   when j.phones is not null and sj.flags & 268435456 = 268435456
                                       then sj.id end)                        as dte_jdp_without_jcoins_cnt,
                count(distinct case
                                   when j.phones is not null and sj.flags & 536870912 = 536870912
                                       then sj.id end)                        as dte_jdp_without_jcoins_subscription_cnt

         from imp.session s
                  join imp.session_jdp sj
                       on s.country = sj.country
                           and s.date_diff = sj.date_diff
                           and s.id = sj.id_session
                  -- додавання job
                  join imp_employer.job_to_uid_mapping jtum
                       on sj.uid_job = jtum.uid_job
                  join imp_employer.job j
                       on jtum.sources = j.sources
                           and jtum.id_job = j.id
                  join imp_employer.employer e
                       on j.sources = e.sources
                           and j.id_employer = e.id
         where s.country in (1, 10) /* only for UA + HU country id*/
           and e.country_code in ('ua', 'hu') /* only for UA + HU employer country code*/
           and jtum.sources = 1 /* only for 1 sources */
           and sj.job_id_project = -1 /* only DTE vacancy views */
           and sj.date_diff >= 44689 /*перший день коли почали писати цю таблицю*/
         group by e.country_code, s.date_diff)

select vacancy.country_code,
       vacancy.date_diff,
       vacancy.vacancies_cnt,
       vacancy.vacancies_with_phone_cnt,
       vacancy.new_vacancies_cnt,
       vacancy.vacancies_publish_cnt,
       vacancy.vacancies_publish_with_phone_cnt,
       dte_jdp_cnt,
       jdp.dte_jdp_with_phone_view_cnt,
       jdp.dte_jdp_without_jcoins_cnt,
       jdp.dte_jdp_without_jcoins_subscription_cnt
from vacancy
         join jdp using (country_code, date_diff);
