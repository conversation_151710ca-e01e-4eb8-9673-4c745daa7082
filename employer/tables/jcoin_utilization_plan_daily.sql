create table employer.jcoin_utilization_plan_daily as
select vsp.database_source_id,
       vsp.subscription_id,
       vsp.payment_id,
       packet_rank,
       vsp.employer_id,
       ic.date_diff::int as utilization_datediff,
       jcoin_cnt as packet_jcoin_cnt,
       payment_jcoin_cnt,
       packet_start_datetime,
       packet_end_datetime,
       packet_plan_end_date,
       payment_order_date,
       payment_plan_end_date,
       date(packet_plan_end_date) - date(vsp.packet_start_datetime) as packet_day_cnt,
       date(payment_plan_end_date) - date(payment_order_date) as payment_day_cnt,
       case
           when
               /*Если дата смены начисления коинов находится в период действия пакета, то с начала действия пакета начисляем по новой методологии*/
               (packet_start_datetime <= '2022-01-13' and '2022-01-13' <= packet_plan_end_datetime)
               or /*После 2021-01-13 начисляем по новой методологии*/ date(dt) >= '2022-01-13'
           then (/*Часть запроса, в которой определяем jcoin plan в период "полупакетов"*/
                 case
                     when (/*Если дата окончания пакета = дате начала следующего пакета (для полупакетов)*/
                           (date(packet_plan_end_datetime) = date(lead(packet_start_datetime)
                                                                  OVER (PARTITION BY employer_id, subscription_id, payment_id ORDER BY subscription_order_datetime, payment_start_datetime, dt, packet_start_datetime))
                         or /*Если дата утилизации = дате окончания пеймента*/ dt = date(payment_plan_end_date))
                         or (/*Если дата начала пакета = дате окончания предыдущего пакета (для полупакетов)*/
                             date(packet_start_datetime) = date(lag(packet_plan_end_datetime)
                                                                OVER (PARTITION BY employer_id, subscription_id, payment_id ORDER BY subscription_order_datetime, payment_start_datetime, dt, packet_start_datetime)))
                               or /*Если дата утилизации = дате начала пеймента*/ dt = date(payment_order_date))
                              and payment_id is not null /*Для фри пакетов условие не применяется*/
                         then ((payment_jcoin_cnt /
                                (date(payment_plan_end_date) - date(payment_order_date)))::decimal(10, 4))/2
                     else (payment_jcoin_cnt /
                           (date(payment_plan_end_date) - date(payment_order_date)))::decimal(10, 4) end)
           /*До 2021-01-13 коины начислялись не сразу, поэтому для оценки плана используем старую методологию*/
           else (packet_jcoin_cnt::numeric/(date(packet_plan_end_datetime) - date(vsp.packet_start_datetime) + 1))::decimal(10, 4) end as jcoin_plan
  from (select vsp.*,
               jcoin_cnt::numeric,
               vspp.payment_jcoin_cnt::numeric,
               date(packet_plan_end_date) as packet_plan_end_date,
               date(vspp.payment_order_date) as payment_order_date,
               date(vspp.payment_plan_end_date) as payment_plan_end_date,
               vspp.payment_type
        from employer.v_subscription_plan_packet vspp
                 join employer.v_subscription_packet vsp
                      on vspp.database_source_id = vsp.database_source_id
                          and vspp.subscription_id = vsp.subscription_id
                          and vspp.packet_rank = vsp.packet_rank) vsp
        left join dimension.info_calendar ic
        on ic.dt between date(vsp.packet_start_datetime)
           and date(coalesce(vsp.packet_end_datetime, vsp.packet_plan_end_date))
 where date(vsp.packet_start_datetime) <= date(coalesce(vsp.packet_end_datetime, vsp.packet_plan_end_date));

