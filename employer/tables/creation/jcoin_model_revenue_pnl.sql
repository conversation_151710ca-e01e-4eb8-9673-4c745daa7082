create table employer.jcoin_model_revenue_pnl
(   database_source_id             smallint  not null,
    subscription_id                integer   not null,
    packet_rank                    integer   not null,
    revenue_datediff               integer   not null,
    employer_id                    integer   not null,
    packet_id                      integer,
    packet_price                   numeric(10, 2),
    packet_day_cnt                 double precision,
    payment_id                     integer   not null,
    payment_datetime               timestamp,
    payment_start_datetime         timestamp not null,
    payment_end_datetime           timestamp not null,
    payment_rank                   integer,
    payment_without_vat_uah        numeric,
    payment_vat_uah                numeric,
    subscription_order_datetime    timestamp,
    subscription_status            smallint,
    subscription_payment_month_cnt integer,
    subscription_payment_price     numeric,
    revenue_without_vat_uah        numeric,
    constraint jcoin_model_revenue_pnl_pk
        primary key (database_source_id, subscription_id, packet_rank, revenue_datediff)
);

alter table employer.jcoin_model_revenue_pnl
    owner to rlu;

grant select on employer.jcoin_model_revenue_pnl to readonly;

grant select on employer.jcoin_model_revenue_pnl to writeonly_product;

grant select on employer.jcoin_model_revenue_pnl to readonly_ds;
