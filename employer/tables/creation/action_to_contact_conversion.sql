create table employer.action_to_contact_conversion
(
    intention_to_contact_type_id integer        not null,
    feature_id                   integer        not null,
    conversion                   decimal(10, 4) not null
);
alter table employer.action_to_contact_conversion
    add constraint action_to_contact_conversion_pk
        primary key (intention_to_contact_type_id, feature_id);
alter table employer.action_to_contact_conversion    owner to postgres;
