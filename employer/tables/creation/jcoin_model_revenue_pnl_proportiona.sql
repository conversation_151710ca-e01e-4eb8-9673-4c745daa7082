create table employer.jcoin_model_revenue_pnl_proportiona
(
    database_source_id             smallint  not null,
    subscription_id                integer   not null,
    revenue_datediff               integer   not null,
    employer_id                    integer   not null,
    packet_id                      integer,
    packet_price                   numeric(10, 2),
    payment_day_cnt                double precision,
    payment_id                     integer   not null,
    payment_datetime               timestamp,
    payment_order_datetime         timestamp not null,
    payment_expiring_datetime      timestamp not null,
    payment_rank                   integer,
    payment_without_vat_uah        numeric,
    payment_vat_uah                numeric,
    subscription_order_datetime    timestamp,
    subscription_status            smallint,
    subscription_payment_month_cnt integer,
    subscription_payment_price     numeric,
    revenue_without_vat_uah        numeric
);

alter table employer.jcoin_model_revenue_pnl_proportiona
    owner to postgres;
