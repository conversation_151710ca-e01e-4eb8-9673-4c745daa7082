create table employer.jcoin_model_daily
(
    database_source_id                  smallint not null,
    subscription_id                                        integer   not null,
    packet_rank                                            integer   not null,
    action_datediff                                        integer not null,
    employer_id                                            integer   not null,
    apply_cnt                                              bigint,
    apply_profile_viewed_cnt                               bigint,
    apply_profile_open_contact_free_packet_cnt             integer,
    apply_profile_open_contact_paid_packet_cnt             integer,
    apply_profile_message_cnt                              integer,
    apply_revenue                                          numeric,
    digital_recruiter_profile_cnt                          bigint,
    digital_recruiter_profile_viewed_cnt                   bigint,
    digital_recruiter_profile_open_contact_free_packet_cnt integer,
    digital_recruiter_profile_open_contact_paid_packet_cnt integer,
    digital_recruiter_profile_message_cnt                  integer,
    digital_recruiter_revenue                              numeric,
    profile_base_search_cnt                                bigint,
    profile_base_profile_viewed_cnt                        bigint,
    profile_base_profile_open_contact_free_packet_cnt      integer,
    profile_base_profile_open_contact_paid_packet_cnt      integer,
    profile_base_profile_message_cnt                       integer,
    profile_base_revenue                                   numeric,
    call_cnt                                               bigint,
    call_answered_cnt                                      bigint,
    call_answered_30_sec_free_packet_cnt                   integer,
    call_answered_30_sec_paid_packet_cnt                   integer,
    call_revenue                                           numeric,
    revenue_pnl_uah                                         numeric,
    revenue_cashflow_uah                                 numeric

    ,constraint jcoin_model_daily_pk
        primary key (database_source_id, subscription_id, packet_rank, action_datediff)
);

alter table employer.jcoin_model_daily
    owner to dap;

grant select on employer.jcoin_model_daily to readonly;
