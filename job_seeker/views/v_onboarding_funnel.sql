create view v_onboarding_funnel
            (country_id, cookie_label, account_id, first_session_datetime, first_session_datediff,
             account_creation_datediff, account_creation_datetime, profile_id, profile_creation_datediff,
             profile_submission_datediff, profile_interested_datediff, profile_onboarding_datediff,
             profile_submission_source_group_id, profile_blue_collar_type_id)
as
SELECT DISTINCT sfv.country_id,
                sfv.cookie_label,
                sr.account_id,
                sfv.first_session_datetime,
                sfv.first_session_datediff,
                sr.account_creation_datediff,
                sr.account_creation_datetime,
                ps.id                  AS profile_id,
                ps.creation_datediff   AS profile_creation_datediff,
                ps.submission_datediff AS profile_submission_datediff,
                pi.interested_datediff AS profile_interested_datediff,
                po.onboarding_datediff AS profile_onboarding_datediff,
                CASE
                    WHEN jsawp.profile_id IS NOT NULL THEN 2
                    WHEN ps.id IS NOT NULL THEN 1
                    ELSE NULL::integer
                    END                AS profile_submission_source_group_id,
                pbs.profile_blue_collar_type_id
FROM job_seeker.session_first_visit sfv
         LEFT JOIN job_seeker.session_registration sr
                   ON sfv.country_id = sr.country_id AND sfv.cookie_label = sr.cookie_label
         LEFT JOIN profile.v_profile_account pa ON sr.country_id = pa.country_id AND sr.account_id = pa.account_id
         LEFT JOIN profile.v_profile_submitted ps ON pa.country_id = ps.country_id AND pa.profile_id = ps.id
         LEFT JOIN job_seeker.profile_interested pi ON pi.country_id = ps.country_id AND pi.profile_id = ps.id
         LEFT JOIN job_seeker.profile_onboarded po ON po.country_id = ps.country_id AND po.profile_id = ps.id
         LEFT JOIN profile.job_seeker_apply_without_profile jsawp
                   ON jsawp.country_id = ps.country_id AND jsawp.profile_id = ps.id
         LEFT JOIN profile.profile_blue_score pbs ON pbs.country_id = ps.country_id AND pbs.profile_id = ps.id;
