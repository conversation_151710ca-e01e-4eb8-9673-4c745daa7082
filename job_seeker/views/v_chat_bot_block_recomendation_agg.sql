create view job_seeker.v_chat_bot_block_recomendation_agg as
select fn_get_date_diff(date_created)                      as recomendation_created_datediff,
       date_part('hour', date_created)                     as recomendation_created_hour,
       block_reason                                        as block_reason_id,
       count(job_uid)                                      as job_cnt,
       count(distinct job_uid)                             as job_unique_cnt,
       count(id_profile)                                   as profile_cnt,
       count(distinct id_profile)                          as profile_unique_cnt,
       sum(case when id_profile is null then 1 else 0 end) as recomendation_without_profile_cnt
from imp_statistic.chat_bot_block_recommendation_sent_events
where fn_get_date_diff(date_created) >= 44592 /* з цього дня почали норм писати стату */
group by 1, 2, 3
order by 1, 2;
