-- Воронка відкриття повідомлень (viber + sms)
create view job_seeker.v_viber_and_sms_message_funnel as
select date_diff                                                            as sent_datediff,
       message_type                                                         as message_type_id,
       count(distinct phone_hash)                                           as users_cnt,
       count(distinct case when status in (2, 3, 4, 5) then phone_hash end) as viber_users_cnt,
       count(case when status in (2, 3, 4, 5) then id end)                  as viber_sent_cnt,
       count(case when status in (3, 4, 5) then id end)                     as viber_delivered_cnt,
       count(case when status in (4, 5) then id end)                        as viber_read_cnt,
       count(case when status = 5 then id end)                              as viber_open_cnt,
       count(distinct case when status in (10, 11, 12) then phone_hash end) as sms_users_cnt,
       count(case when status in (10, 11, 12) then id end)                  as sms_sent_cnt,
       count(case when status in (12) then id end)                          as sms_delivered_cnt
from imp.viber_message_sent
where country = 1
  and date(date) >= '2021-08-01' /* start new monetezation*/
group by 1, 2;
