create view job_seeker.v_chat_bot_recomendation_redirect_to_jdp_agg as
select sj.country                                                      as country_id,
       sj.date_diff                                                    as jdp_view_datediff,
       sj.source                                                       as source_id,
       case when sj.job_id_project = -1 then 1 else 0 end              as is_dte_jdp,
       case
           when sj.job_id_project = -1 and (sj.flags & ********* = ********* or sj.flags & ********* = *********) then 1
           when sj.job_id_project != -1 then 1
           else 0 end                                                  as is_jdp_without_call_feature,
       case when sj.flags & 4 = 0 then 1 else 0 end                    as is_jdp_without_apply_feature,
       count(distinct sj.id)                                           as jdp_view_cnt,
       count(distinct sj.id_session)                                   as session_cnt,
       count(distinct case when id_account is not null then sj.id end) as jdp_login_user_view_cnt
from imp.session_jdp as sj
where sj.source in (1, 2, 3, 4) /*chat bot source*/
  and sj.country = 1
  and sj.date_diff >= 44560
group by 1, 2, 3, 4, 5, 6;
