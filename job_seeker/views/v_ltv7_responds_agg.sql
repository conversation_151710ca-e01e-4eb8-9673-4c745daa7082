create or replace view job_seeker.v_ltv7_responds_agg(country, first_day, group319, responds, clients) as
	SELECT r.country,
       r.first_day,
       r.group319,
       r.clicks_calls + r.applies_click + r.aways AS responds,
       count(0)                                   AS clients
FROM job_seeker.m_ltv7_responds r
GROUP BY r.country, r.first_day, r.group319, (r.clicks_calls + r.applies_click + r.aways);

alter table job_seeker.v_ltv7_responds_agg owner to dap;

grant select on job_seeker.v_ltv7_responds_agg to npo;

grant select on job_seeker.v_ltv7_responds_agg to readonly;

grant select on job_seeker.v_ltv7_responds_agg to nsh;

grant select on job_seeker.v_ltv7_responds_agg to writeonly_product;

grant select on job_seeker.v_ltv7_responds_agg to ksha;

grant select on job_seeker.v_ltv7_responds_agg to readonly_ds;

