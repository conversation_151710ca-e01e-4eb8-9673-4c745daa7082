-- General info про юзерів з chat-bot
-- В розрізі : chat_bot_source_name (Viber/ Telegram)
-- is_users_with_submitted_profile: 1 - True, 0 - False

-- Metric:
-- users_cnt - загальна кількість юзерів, що не видалили чат бот та мають з ним зв'язок
-- new_users_cnt - кількість onboarded юзерів в день date_diff
-- users_delated_bot_cnt - кількість юзерів, що видалили чат-бот в день date_diff
-- users_searching_cnt - кількість юзерів, що мають зв'язок з чат ботом, та підписані на розсилку
-- users_with_auth_cnt -- кількість юзерів з авторизацією на сайті

create table job_seeker.chat_bot_user_info_agg as
select cb.date_diff,
       c.name                                                                                                  as chat_bot_source_name,
       case
           when fn_get_date_diff(cb.created_on) - cbs.first_open_datediff between 0 and 1 then 1
           else 0 end                                                                                          as is_maybe_sudoku_project_onboarding_source,
       case
           when fn_get_date_diff(p.date_is_submitted) <= cb.date_diff then 1
           else 0 end                                                                                          as is_user_with_submitted_profile,
       count(distinct case when is_deleted_bot = 0 then chat_id end)                                           as user_cnt,
       count(distinct case
                          when fn_get_date_diff(cb.created_on) = cb.date_diff
                              then chat_id end)                                                                as new_user_cnt,
       count(distinct case
                          when is_deleted_bot = 1 and fn_get_date_diff(cb.deleted_on) = cb.date_diff
                              then chat_id end)                                                                as user_delated_bot_cnt,
       count(distinct
             case when is_deleted_bot = 0 and is_searching = 1 then chat_id end)                               as user_searching_cnt,
       count(distinct
             case when is_deleted_bot = 0 and is_auth = 1 then chat_id end)                                    as user_with_auth_cnt
from imp.chat_bot_user_info cb
         join imp.profiles p
    -- різні формати номерів
    -- join тут по номеру телефона
              on CONCAT('+', cb.phone) = p.phone
                  and p.country = 1
         join imp.channel c
              on cb.channel_id = c.id
         left join job_seeker.chat_bot_sudoku_open_onboarding_link_profile cbs
                   on p.id = cbs.profile_id
group by 1, 2, 3, 4;

