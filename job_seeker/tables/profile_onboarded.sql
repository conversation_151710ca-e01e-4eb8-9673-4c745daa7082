-- подолання порогу в 1 звернення групи Onboarded
create table job_seeker.profile_onboarded as
select country_id,
       profile_id,
       min(action_datediff) as onboarding_datediff,
       min(running_atc)     as onboarded_day_atc
from (
         select atcs.country_id,
                profile_id,
                action_datediff,
                --ps.submission_datediff,
                sum(action_to_contact_prob)
                over (partition by profile_id, atcs.country_id order by action_datediff) as running_atc
         from profile.action_to_contact_structure atcs
                  join imp.profiles ps
                       on ps.country = atcs.country_id
                           and ps.id = atcs.profile_id
         where atcs.country_id in (1, 10)
           and atcs.platform_user_type_id = 1
           and atcs.action_datediff <= ps.date_submitted + 6
     ) a
where running_atc >= 1
group by profile_id,
         country_id
having min(action_datediff) between 44560 and ${DT_NOW} - 1;
