create table job_seeker.user_additional_questions
(
    id                   serial
        constraint pk_user_additional_questions_id
            primary key,
    user_id              varchar(255),
    user_type_id         smallint,
    usage_first_datediff integer,
    usage_first_datetime timestamp,
    usage_cnt            integer,
    country_id           integer
);

alter table job_seeker.user_additional_questions
    owner to postgres;

grant select on sequence job_seeker.user_additional_questions_id_seq to npo;

create index ind_user_additional_questions_u
    on job_seeker.user_additional_questions (user_id, user_type_id);

create index ind_user_additional_questions_dd
    on job_seeker.user_additional_questions (usage_first_datediff);
