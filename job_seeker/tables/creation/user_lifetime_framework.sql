create table job_seeker.user_lifetime_framework
(
    user_id               varchar(255),
    user_type_id          smallint,
    blue_collar_score     double precision,
    jdp_viewed_datediff   integer,
    next_apply_datediff   integer,
    prev_apply_datediff   integer,
    user_returned_type_id smallint,
    user_churn_type_id    smallint,
    apply_cnt             integer,
    country_id            integer,
    id                    serial
        constraint pk_user_lifetime_framework_id
            primary key
);

alter table job_seeker.user_lifetime_framework
    owner to postgres;

create index ind_user_lifetime_framework_dd
    on job_seeker.user_lifetime_framework (jdp_viewed_datediff);
