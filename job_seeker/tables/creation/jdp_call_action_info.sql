create table if not exists job_seeker.jdp_call_action_info
(
    country_id                  smallint not null,
    employer_id                 integer  not null,
    session_id                  bigint   not null,
    jdp_id                      bigint   not null,
    jdp_view_datediff           integer  not null,
    is_phone_show_click         smallint,
    is_call                     smallint,
    is_call_answered_proper     smallint,
    is_call_missed              smallint,
    is_call_rejected            smallint,
    is_ringostat_replaced_phone smallint,
    is_paid_packet              smallint not null,
    constraint pk_jdp_call_action_info_id
        primary key (country_id, employer_id, session_id, jdp_id, jdp_view_datediff, is_paid_packet)
);

alter table job_seeker.jdp_call_action_info
    owner to postgres;
