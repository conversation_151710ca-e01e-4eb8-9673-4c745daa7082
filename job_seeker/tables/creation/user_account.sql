create table job_seeker.user_account
(
    account_id           bigint       not null,
    user_id              varchar(255) not null,
    user_type_id         smallint     not null,
    user_per_account_cnt integer,
    country_id           integer      not null,
    constraint user_account_pk
        primary key (country_id, account_id, user_id, user_type_id)
);

alter table job_seeker.user_account
    owner to postgres;

create index user_account_acc_idx
    on job_seeker.user_account (country_id, account_id);
