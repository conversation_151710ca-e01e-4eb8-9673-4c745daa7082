create table job_seeker.user_profile_created
(
    country_id           smallint     not null,
    user_id              varchar(255) not null,
    user_type_id         smallint     not null,
    usage_first_datediff integer,
    usage_first_datetime timestamp,
    usage_cnt            integer,
    constraint pk_user_profile_created_id
        primary key (country_id, user_id, user_type_id)
);

alter table job_seeker.user_profile_created
    owner to postgres;

create index ind_user_profile_created_u
    on job_seeker.user_profile_created (user_id, user_type_id);
