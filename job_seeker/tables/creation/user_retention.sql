create table job_seeker.user_retention
(
    score                 double precision,
    user_type_id          smallint,
    user_id               varchar(255),
    activation_datediff   integer,
    activation_datetime   timestamp,
    is_retained_1_7days   integer,
    is_retained_1_28days  integer,
    is_retained_29_98days integer,
    country_id            integer,
    is_retained_1day      integer
);

alter table job_seeker.user_retention
    owner to postgres;

create index ind_user_retention_u
    on job_seeker.user_retention (user_id, user_type_id);
