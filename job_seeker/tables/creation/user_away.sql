create table job_seeker.user_away
(
    country_id          smallint not null,
    jdp_viewed_datediff integer  not null,
    jdp_id              bigint   not null,
    account_id          integer,
    session_id          bigint,
    cookie_label        bigint,
    job_uid             bigint,
    jdp_flag            integer,
    jdp_viewed_datetime timestamp,
    device_type         integer,
    constraint user_away_pk
        primary key (country_id, jdp_viewed_datediff, jdp_id)
);

alter table job_seeker.user_away
    owner to dap;
