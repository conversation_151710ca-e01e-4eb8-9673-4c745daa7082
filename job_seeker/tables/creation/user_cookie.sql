create table job_seeker.user_cookie
(
    cookie_label        bigint       not null,
    user_id             varchar(255) not null,
    user_type_id        smallint     not null,
    user_per_cookie_cnt integer,
    country_id          integer      not null,
    constraint pk_user_cookie_id
        primary key (country_id, user_id, user_type_id, cookie_label)
);

alter table job_seeker.user_cookie
    owner to postgres;

create index user_cookie_cl_idx
    on job_seeker.user_cookie (country_id, cookie_label);
