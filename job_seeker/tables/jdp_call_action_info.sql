-- !!! різна логіка запису
-- якщо плануєте перезаписувати дану таблицю, то:
-- тільки починаючи з 18 листопада 20201 року (включно) варто використовувати умову and sj.flags & 536870912 = 0
-- до даної дати, виключайте умову при перезаписі таблиці

select distinct s.country                                      as country_id,
                vpi.employer_id,
                s.id                                           as session_id,
                sj.id                                          as jdp_id,
                sj.date_diff                                   as jdp_view_datediff,

                max(case
                        when sja.type in (13, 19, 21, 34) /* show phone contact click */ or sja.type = 56 then 1
                        else 0 end)                            as is_phone_show_click,
                max(case
                        when sja.type = 56 /* call */ and sja.flags & 4 = 0 /*not repeated*/ then 1
                        else 0 end)                            as is_call,
                max(case
                        when sja.type = 56 /* call */ and sja.flags & 4 = 0 /*not repeated*/
                            and sja.flags & 2 = 2 /* Answered Proper */ then 1
                        else 0 end)                            as is_call_answered_proper,
                max(case
                        when sja.type = 56 /* call */
                            and sja.flags & 8 = 8 /* NoAnswer = missed */ then 1
                        else 0 end)                            as is_call_missed,
                max(case
                        when sja.type = 56 /* call */
                            and sja.flags & (64 + 32) <> 0 /* Busy or Voicemail = rejected */ then 1
                        else 0 end)                            as is_call_rejected,
                max(case when sja.type = 55 then 1 else 0 end) as is_ringostat_replaced_phone,


                case when packet_price = 0 then 0 else 1 end   as is_paid_packet
from imp.session s
         join imp.session_jdp sj
              on s.country = sj.country
                  and s.date_diff = sj.date_diff
                  and s.id = sj.id_session
                  and sj.flags & 268435456 = 0 -- jdp without jcoin (не пишемо для free jdp)
                  and
                 sj.flags & 536870912 = 0 -- free jdp (флаг додано 18 листопада, таблицю перезаписано з 18 листопада)
         join imp.session_jdp_action sja
              on sj.country = sja.country
                  and sj.date_diff = sja.date_diff
                  and sja.id_jdp = sj.id
         join imp_employer.job_to_uid_mapping uid
              on sj.uid_job = uid.uid_job
                  and uid.sources = 1
         join imp_employer.job j
              on uid.sources = j.sources
                  and uid.id_job = j.id
         join employer.m_subscription_packet vpi
              on vpi.employer_id = j.id_employer
                  and vpi.database_source_id = j.sources
                  -- в session_jdp_action sja.date пишеться по UTC
                  and sja.date between ((vpi.subscription_order_datetime::timestamp at time zone 'UTC') at time zone
                                        'Europe/Kiev') and (
                          (vpi.subscription_expiring_datetime::timestamp at time zone 'UTC') at time zone 'Europe/Kiev')
where s.country = 1
  and s.is_bot = 0
  and sj.job_id_project = -1
  and sja.type in (13, 19, 21, 34, 55, 56)
  and sja.date_diff = 44466 -- '2021-08-01' -> '2021-09-17'
group by s.country, vpi.employer_id, s.id, sj.id, sj.date_diff, case when packet_price = 0 then 0 else 1 end;
