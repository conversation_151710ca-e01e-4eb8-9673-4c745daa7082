select s.country as country_id,
       s.date_diff as session_datediff,
       s.cookie_label,
       s.id as session_id,
       ss.id as search_id,
       sj.id as jdp_id,
       (case when sj.job_id_project = -1 then 1 else 0 end)
           + (case when sj.flags & 256 = 256 then 2 else 0 end)
           + (case when sj.flags & 4 = 4 then 4 else 0 end)
           + (case when sj.flags & 2048 = 2048 then 8 else 0 end)
           as jdp_flag
from imp.session s
join imp.session_search ss on s.country = ss.country  and s.date_diff = ss.date_diff and s.id = ss.id_session
join imp.session_click sc on sc.country = ss.country and sc.id_search = ss.id and sc.date_diff = ss.date_diff
join imp.session_jdp sj on sc.country = sj.country and sc.id = sj.id_click and sc.date_diff = sj.date_diff
where s.country = 1 and s.date_diff = ${DT_NOW} - 1;