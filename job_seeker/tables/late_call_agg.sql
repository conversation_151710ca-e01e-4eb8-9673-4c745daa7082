select date_diff as call_datediff,
       count(phone)                                                           as late_call_cnt, /*усі late call*/
       count(case when status & 1 = 1 /*answered*/ then phone end)            as late_call_answered_cnt, /*late call з відповіддю*/
       count(case when status & 2 = 2 /* answered proper */ then phone end)   as late_call_answered_proper_cnt, /*late call з відповіддю які тривали більше 30 сек*/
       count(case when status & 8 = 8 /* NoAnswer = missed */ then phone end) as late_call_missed_cnt, /*пропощенні дзвінки*/
       count(case
                 when status & (64 + 32) <> 0 /* Busy or Voicemail = rejected */
                     then phone end)                                          as late_call_rejected_cnt /*відхилені дзвінки*/
from imp_employer.late_call
where date_diff between fn_get_date_diff('2021-10-01') /*з цього дня коректно записуються late call*/ and fn_get_date_diff(current_date) - 1
  and status & 4 = 0 /*not repeated*/
group by date_diff
