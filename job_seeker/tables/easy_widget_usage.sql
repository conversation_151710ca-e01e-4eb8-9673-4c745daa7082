CREATE OR REPLACE PROCEDURE job_seeker.insert_easy_widget_usage(_datediff integer)
    LANGUAGE plpgsql
AS
$$

BEGIN

    --=============
    DELETE
    FROM job_seeker.easy_widget_usage
    WHERE date_diff = _datediff;
    --=============

    ANALYZE imp.session_feature;
    ANALYZE imp.session_feature_action;


    INSERT INTO job_seeker.easy_widget_usage(country, country_code, date_diff, is_mobile, position_on_serp,
                                             widget_name, widget_description, session_view_cnt, view_cnt,
                                             session_click_cnt, click_cnt)

    WITH df          AS (SELECT DISTINCT c.name_country_eng                             AS country,
                                         c.alpha_2                                      AS country_code,
                                         s.date_diff,
                                         CASE WHEN s.flags & 16 = 16 THEN 1 ELSE 0 END  AS is_mobile,
                                         sf.feature_data                                AS feature_data,
                                         sfa.action_data                                AS feature_action_data,
                                         CASE WHEN sfa.type = 83 THEN sf.id_session END AS id_session_view,
                                         CASE WHEN sfa.type = 83 THEN sfa.id END        AS id_view,
                                         CASE WHEN sfa.type = 84 THEN sfa.id END        AS id_click,
                                         CASE WHEN sfa.type = 84 THEN sf.id_session END AS id_session_click
                         FROM imp.session s
                                  JOIN imp.session_feature sf
                                       ON s.date_diff = sf.date_diff
                                           AND s.id = sf.id_session
                                  JOIN imp.session_feature_action sfa
                                       ON sf.date_diff = sfa.date_diff
                                           AND sf.id = sfa.id_session_feature
                                  JOIN dimension.countries c
                                       ON s.country = c.id
                         WHERE s.country IN (1, 10, 11)
                           AND s.date_diff = _datediff /* '2024-06-01' */
                           AND sf.type = 36),

         widget_data AS (SELECT *,
                                CAST(feature_data::json -> 'widgetName' AS varchar)               AS widget_name,
                                CAST(feature_data::json -> 'positionOnSerp' AS varchar)           AS position_on_serp,
                                CAST(feature_action_data::json -> 'widgetDescription' AS varchar) AS widget_description
                         FROM df)

    SELECT country,
           country_code,
           date_diff,
           is_mobile,
           position_on_serp,
           widget_name,
           widget_description,
           COUNT(DISTINCT id_session_view)  AS session_view_cnt,
           COUNT(DISTINCT id_view)          AS view_cnt,
           COUNT(DISTINCT id_session_click) AS session_click_cnt,
           COUNT(DISTINCT id_click)         AS click_cnt
    FROM widget_data
    GROUP BY 1, 2, 3, 4, 5, 6, 7;


END;
$$;
