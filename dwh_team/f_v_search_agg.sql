create function dwh_test.f_v_search_agg(_date_start date)
    returns TABLE(country character varying, date date, name_current_traf_source character varying, channel_traf_source_current character varying, device text, is_local integer, session_create_page_type_name character varying, serp_results_for_search text, search_source text, modified_q_kw character varying, q_region_name character varying, empty_searches text, search_cnt bigint, click_cnt bigint, revenue_usd numeric, destination_away_click bigint, destination_jdp_away_click bigint, destination_jdp_apply_click bigint, click_paid_cnt bigint, click_premium_cnt bigint, click_free_cnt bigint, with_only_free_jobs bigint, with_paid_jobs bigint, with_paid_clicks bigint, without_paid_clicks bigint, without_any_clicks bigint, conversion_away_cnt bigint, conversion_revenue_usd numeric, conversion_cnt bigint)
    language plpgsql
as
$$
declare
    _input_date_start      date := _date_start;
    _input_date_diff_start int  := public.fn_get_date_diff(_input_date_start);

begin

    create temp table tmp_search_agg as
    select * from aggregation.search_agg
    where date_diff = _input_date_diff_start
      and country_id = any(array[2, 4, 6, 3, 20, 9, 13, 16, 14, 19, 5, 17, 12, 35, 18, 23, 15, 61, 60, 39]);

    create index idx_tmp_search_agg on tmp_search_agg  (id_traf_source, country_id, date_diff, q_id_region, search_source, id_current_traf_source);
    analyse tmp_search_agg;


    return query
    SELECT countries.name_country_eng                       AS country,
           fn_get_date_from_date_diff(search_agg.date_diff) AS date,
           uts1.name                                        AS name_current_traf_source,
           uts1.channel                                     AS channel_traf_source_current,
           CASE
               WHEN search_agg.user_device = 1 THEN 'mobile'::text
               WHEN search_agg.user_device = 2 THEN 'mobile app'::text
               ELSE 'desktop'::text
               END                                          AS device,
           search_agg.is_local,
           pt.name                                          AS session_create_page_type_name,
           CASE
               WHEN search_agg.serp_results_total::integer = 0 THEN '0'::text
               WHEN search_agg.serp_results_total::integer = 1 THEN '1-10'::text
               WHEN search_agg.serp_results_total::integer = 2 THEN '11-20'::text
               WHEN search_agg.serp_results_total::integer = 3 THEN '21-50'::text
               WHEN search_agg.serp_results_total::integer = 4 THEN '51-100'::text
               WHEN search_agg.serp_results_total::integer = 5 THEN '101-250'::text
               WHEN search_agg.serp_results_total::integer = 6 THEN '251-500'::text
               WHEN search_agg.serp_results_total::integer = 7 THEN '501-1000'::text
               WHEN search_agg.serp_results_total::integer = 8 THEN '1001-5000'::text
               WHEN search_agg.serp_results_total::integer = 9 THEN '5001+'::text
               ELSE NULL::text
               END                                          AS serp_results_for_search,
           dic_search_source.name                           AS search_source,
           search_agg.q_kw                                  AS modified_q_kw,
           ir.name                                          AS q_region_name,
           CASE
               WHEN search_agg.empty_searches = 0 THEN 'empty keyword'::text
               WHEN search_agg.empty_searches = 1 THEN 'empty region'::text
               WHEN search_agg.empty_searches = 2 THEN 'empty keyword and region'::text
               WHEN search_agg.empty_searches = 3 THEN 'not empty keyword and region'::text
               ELSE NULL::text
               END                                          AS empty_searches,
           sum(search_agg.search_cnt)                       AS search_cnt,
           sum(search_agg.click_cnt)                        AS click_cnt,
           sum(search_agg.revenue_usd)                      AS revenue_usd,
           sum(search_agg.destination_away_click)           AS destination_away_click,
           sum(search_agg.destination_jdp_away_click)       AS destination_jdp_away_click,
           sum(search_agg.destination_jdp_apply_click)      AS destination_jdp_apply_click,
           sum(search_agg.click_paid_cnt)                   AS click_paid_cnt,
           sum(search_agg.click_premium_cnt)                AS click_premium_cnt,
           sum(search_agg.click_free_cnt)                   AS click_free_cnt,
           sum(search_agg.with_only_free_jobs)              AS with_only_free_jobs,
           sum(search_agg.with_paid_jobs)                   AS with_paid_jobs,
           sum(search_agg.with_paid_clicks)                 AS with_paid_clicks,
           sum(search_agg.without_paid_clicks)              AS without_paid_clicks,
           sum(search_agg.without_any_clicks)               AS without_any_clicks,
           sum(search_agg.conversion_away_cnt)              AS conversion_away_cnt,
           sum(search_agg.conversion_revenue_usd)           AS conversion_revenue_usd,
           sum(search_agg.conversion_cnt)                   AS conversion_cnt
    FROM tmp_search_agg search_agg
             LEFT JOIN dimension.u_traffic_source uts
                       ON uts.id = search_agg.id_traf_source AND uts.country = search_agg.country_id
             LEFT JOIN dimension.u_traffic_source uts1
                       ON uts1.id = search_agg.id_current_traf_source AND uts1.country = search_agg.country_id
             LEFT JOIN dimension.info_region_other ir
                       ON ir.id = search_agg.q_id_region AND ir.country = search_agg.country_id
             LEFT JOIN aggregation.dic_session_create_page_type pt ON pt.id = search_agg.session_create_page_type
             LEFT JOIN dimension.countries ON search_agg.country_id = countries.id
             LEFT JOIN dimension.dic_search_source ON dic_search_source.id = search_agg.search_source
    WHERE search_agg.date_diff = _input_date_diff_start
      AND (search_agg.country_id = ANY
           (ARRAY [2, 4, 6, 3, 20, 9, 13, 16, 14, 19, 5, 17, 12, 35, 18, 23, 15, 61, 60, 39]))
    GROUP BY countries.name_country_eng, (fn_get_date_from_date_diff(search_agg.date_diff)), uts1.name, uts1.channel,
             (
                 CASE
                     WHEN search_agg.user_device = 1 THEN 'mobile'::text
                     WHEN search_agg.user_device = 2 THEN 'mobile app'::text
                     ELSE 'desktop'::text
                     END), search_agg.is_local, pt.name,
             (
                 CASE
                     WHEN search_agg.serp_results_total::integer = 0 THEN '0'::text
                     WHEN search_agg.serp_results_total::integer = 1 THEN '1-10'::text
                     WHEN search_agg.serp_results_total::integer = 2 THEN '11-20'::text
                     WHEN search_agg.serp_results_total::integer = 3 THEN '21-50'::text
                     WHEN search_agg.serp_results_total::integer = 4 THEN '51-100'::text
                     WHEN search_agg.serp_results_total::integer = 5 THEN '101-250'::text
                     WHEN search_agg.serp_results_total::integer = 6 THEN '251-500'::text
                     WHEN search_agg.serp_results_total::integer = 7 THEN '501-1000'::text
                     WHEN search_agg.serp_results_total::integer = 8 THEN '1001-5000'::text
                     WHEN search_agg.serp_results_total::integer = 9 THEN '5001+'::text
                     ELSE NULL::text
                     END), dic_search_source.name, search_agg.q_kw, ir.name,
             (
                 CASE
                     WHEN search_agg.empty_searches = 0 THEN 'empty keyword'::text
                     WHEN search_agg.empty_searches = 1 THEN 'empty region'::text
                     WHEN search_agg.empty_searches = 2 THEN 'empty keyword and region'::text
                     WHEN search_agg.empty_searches = 3 THEN 'not empty keyword and region'::text
                     ELSE NULL::text
                     END);
end;
$$;

alter function dwh_test.f_v_search_agg(date) owner to yiv;
