create or replace view mobile_app.v_session_account_by_placement
            (country_code, mobile_platform_name, traffic_source, account_id, account_verification_date, session_id,
             session_datediff, session_retention_type_id, session_order_num, session_row_num, placement,
             alertview_search_cnt, impression_cnt, impression_on_screen_cnt, serp_click_cnt, serp_away_cnt,
             serp_jdp_cnt, jdp_away_cnt, revenue_usd, intention_to_contact)
as
WITH session_account_by_placement AS (
         SELECT sabp.country_id,
            sabp.cookie_label,
            sabp.session_datediff,
            sabp.session_id,
            sabp.placement,
            sabp.account_id,
            sabp.alertview_search_cnt,
            sabp.impression_cnt,
            sabp.impression_on_screen_cnt,
            sabp.serp_click_cnt,
            sabp.serp_jdp_cnt,
            sabp.serp_away_cnt,
            sabp.jdp_away_cnt,
            row_number() OVER (PARTITION BY sabp.country_id, sabp.session_datediff, sabp.session_id) AS session_row_num,
            rank() OVER (PARTITION BY sabp.country_id, sabp.account_id ORDER BY sabp.session_datediff, sabp.session_id) AS session_order_num,
                CASE
                    WHEN eai.id_session IS NOT NULL THEN 1
                    ELSE 2
                END AS session_retention_type_id
           FROM mobile_app.session_account_by_placement sabp
             LEFT JOIN imp.email_account_interactions eai ON sabp.country_id = eai.country AND sabp.session_datediff = eai.date_diff AND sabp.session_id = eai.id_session
        ), mobile_app_session_revenue AS (
         SELECT session_revenue_by_placement.country,
            session_revenue_by_placement.session_datediff,
            session_revenue_by_placement.session_id,
            session_revenue_by_placement.placement,
            sum(session_revenue_by_placement.revenue_usd) AS revenue_usd
           FROM mobile_app.session_revenue_by_placement
          GROUP BY session_revenue_by_placement.country, session_revenue_by_placement.placement, session_revenue_by_placement.session_datediff, session_revenue_by_placement.session_id
        ), itc_per_view AS (
         SELECT ic.dt,
            ij.country,
            ij.placement,
            sum(ij.views) AS views,
            sum(ij.intention_to_contact) AS intention_to_contact,
            sum(ij.intention_to_contact) / sum(ij.views) AS itc_per_view
           FROM dimension.info_calendar ic
             JOIN mobile_app.itc_jdp ij ON
                CASE
                    WHEN ic.dt < '2023-01-21'::date THEN ij.dt >= '2023-01-21'::date AND ij.dt <= '2023-02-21'::date
                    ELSE NULL::boolean
                END OR ic.dt >= ij.dt AND ic.dt <= (ij.dt + 30)
          WHERE ij.is_from_swipe = 0 AND ij.jdp_response_type_id = 0 AND ic.dt >= '2022-10-01'::date
          GROUP BY ic.dt, ij.country, ij.placement
         HAVING sum(ij.views) > 0::numeric
        )
 SELECT c.alpha_2 AS country_code,
    fla.platform AS mobile_platform_name,
    fla.traffic_source,
    masaa.account_id,
    ac.verify_date::date AS account_verification_date,
    masaa.session_id,
    masaa.session_datediff,
    masaa.session_retention_type_id,
    masaa.session_order_num,
    masaa.session_row_num,
    masaa.placement,
    sum(masaa.alertview_search_cnt) AS alertview_search_cnt,
    sum(masaa.impression_cnt) AS impression_cnt,
    sum(masaa.impression_on_screen_cnt) AS impression_on_screen_cnt,
    sum(masaa.serp_click_cnt) AS serp_click_cnt,
    sum(masaa.serp_away_cnt) AS serp_away_cnt,
    sum(masaa.serp_jdp_cnt) AS serp_jdp_cnt,
    sum(masaa.jdp_away_cnt) AS jdp_away_cnt,
    sum(masr.revenue_usd) AS revenue_usd,
    sum(masaa.serp_away_cnt::numeric * ipv.itc_per_view) + sum(masaa.jdp_away_cnt) AS intention_to_contact
   FROM session_account_by_placement masaa
     JOIN dimension.countries c ON masaa.country_id = c.id
     LEFT JOIN imp.account_contact ac ON ac.country = masaa.country_id AND ac.id_account = masaa.account_id AND ac.type = 2
     LEFT JOIN mobile_app.first_launch_attribute fla ON lower(fla.installation_id::text) = lower(ac.contact::text)
     LEFT JOIN mobile_app_session_revenue masr ON masr.country = masaa.country_id AND masr.session_id = masaa.session_id and masr.placement = masaa.placement
     LEFT JOIN itc_per_view ipv ON masaa.country_id = ipv.country AND
        CASE
            WHEN masaa.placement = 1 THEN 1
            WHEN masaa.placement = 2 THEN 2
            ELSE 0
        END = ipv.placement AND fn_get_date_from_date_diff(masaa.session_datediff) = ipv.dt
  GROUP BY c.alpha_2, fla.platform, fla.traffic_source, masaa.account_id, (ac.verify_date::date), masaa.session_id, masaa.session_datediff, masaa.session_retention_type_id, masaa.session_order_num, masaa.session_row_num, masaa.placement;

alter table mobile_app.v_session_account_by_placement
    owner to rlu;

grant select on mobile_app.v_session_account_by_placement to readonly;

