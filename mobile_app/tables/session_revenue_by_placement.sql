/*placement:
    (1), -- 'manual search'
    (2), -- 'homepage alertview'
    (3), -- 'homepage tab'
    (4), -- 'favorites'
    (5), -- 'homepage search'
    (6), -- 'recent search'
    (0); -- 'other'
*/

select
    s.cookie_label,
    s.date_diff        as session_datediff,
    s.id               as session_id
into temporary table
    temp_session
from
    public.session s
where
      s.flags & 64 = 64 /*mobile app*/
  and s.flags & 1 = 0 /*not bots*/
  and s.date_diff between 44982-7 and 44982
group by s.cookie_label, s.date_diff, s.id;

select session_datediff,
    session_id,
    case
        when ss.search_source = 1004 then 1
        when ss.search_source = 1001 then 2
        when ss.search_source = 1003 then 3
        when ss.search_source = 1002 then 4
        when ss.search_source = 1006 then 5
        when ss.search_source = 1005 then 6
        else 0 end as placement,
    ss.id          as id_search,
    null::bigint   as id_alertview
into temporary table
    temp_searches
from
    temp_session s
    join public.session_search ss
         on ss.date_diff = s.session_datediff and
            ss.id_session = s.session_id;

insert into
    temp_searches
select session_datediff,
    session_id,
    case
        when sal.service_flags & 536870912 = 536870912 then 2
        else 0 end as placement,
    null::bigint   as id_search,
    sal.id         as id_alertview
from
    temp_session s
    join public.session_alertview sal
         on sal.date_diff = s.session_datediff and
            sal.id_session = s.session_id;

select s.session_datediff,
    s.session_id,
    sc.id as id_click,
    coalesce(ts.placement, 0) as placement
into temporary table
    temp_click_placement
from
    temp_session s
    join public.session_click sc
         on sc.date_diff = s.session_datediff and
            sc.id_session = s.session_id
    left join public.session_impression si
              on si.date = sc.date_diff and
                 si.id = sc.id_impression
    left join temp_searches ts
              on s.session_datediff = ts.session_datediff and
                 (coalesce(sc.id_search, si.id_search) = ts.id_search
                      or coalesce(sc.id_alertview, si.id_alertview) = ts.id_alertview);

select s.session_datediff,
       s.session_id,
       sj.id as id_jdp,
       coalesce(ts.placement, case when scns.click_flags & 4096 = 4096 then 4 end, 0) as placement
into temporary table temp_jdp_placement
from
    temp_session s
    join public.session_jdp sj
              on sj.date_diff = s.session_datediff and
                 sj.id_session = s.session_id
    left join public.session_click_no_serp scns
              on scns.date_diff = s.session_datediff and
                 sj.id_click_no_serp = scns.id
    left join public.session_click sc
         on sc.date_diff = s.session_datediff and
            sj.id_click = sc.id
    left join public.session_impression si
              on si.date = sc.date_diff and
                 si.id = sc.id_impression
    left join temp_searches ts
              on s.session_datediff = ts.session_datediff and
                 (coalesce(sc.id_search, si.id_search) = ts.id_search
                      or coalesce(sc.id_alertview, si.id_alertview) = ts.id_alertview);

select session_datediff,
       session_id,
       case when scns.click_flags & 4096 = 4096 then 4 else 0 end      as placement,
       scns.id as id_click_no_serp
into temporary table temp_click_no_serp_placement
from temp_session s
         join public.session_click_no_serp scns
              on scns.date_diff = s.session_datediff and
                 scns.id_session = s.session_id;

select s.session_datediff,
       s.session_id,
       coalesce(tcp.placement,tjp.placement,tcnsp.placement,0) as placement,
       sa.id as id_away
into temporary table temp_away_placement
from temp_session s
         join public.session_away sa
              on sa.date_diff = s.session_datediff and
                 sa.id_session = s.session_id
         left join temp_click_placement tcp
                   on sa.date_diff = tcp.session_datediff
                   and sa.id_click = tcp.id_click
         left join temp_jdp_placement tjp
                   on sa.date_diff = tjp.session_datediff
                   and sa.id_jdp = tjp.id_jdp
         left join temp_click_no_serp_placement tcnsp
                   on sa.date_diff = tcnsp.session_datediff
                   and sa.id_click_no_serp = tcnsp.id_click_no_serp;

create temp table info_currency as
select *
  from link_dbo.info_currency;

insert into
    mobile_app.session_revenue_by_placement
select
    session_datediff,
    session_id,
    sum(revenue_usd) as revenue_usd,
    placement,
    revenue_type_id
from
    (
select ts.session_datediff,
       ts.session_id,
       sum(sa.click_price * ic.value_to_usd) as revenue_usd,
       1 /*internal statistics*/             as revenue_type_id,
       placement
from public.session_away sa
         join temp_session ts
              on sa.date_diff = ts.session_datediff and
                 sa.id_session = ts.session_id
         join info_currency ic
              on ic.id = sa.id_currency
         left join link_auction.campaign ac
                   on ac.id = sa.id_campaign
         left join link_auction.site ast
                   on ac.id_site = ast.id
         left join link_auction.user au
                   on au.id = ast.id_user
         left join temp_away_placement tap
                   on sa.date_diff = tap.session_datediff
                   and sa.id = tap.id_away
where (sa.id_campaign = 0 or au.flags & 2 = 0)
  and sa.flags & 2 = 0
  and sa.flags & 512 = 0
 group by ts.session_datediff,
          ts.session_id,
          placement

union all

select ts.session_datediff,
       ts.session_id,
       sum(sc.click_price * ic.value_to_usd) as revenue_usd,
       2 /*external statistics*/             as revenue_type_id,
       placement
from public.session_click sc
         join temp_session ts
              on sc.date_diff = ts.session_datediff and
                 sc.id_session = ts.session_id
         join info_currency ic
              on ic.id = sc.id_currency
         left join link_auction.campaign ac
                   on ac.id = sc.id_campaign
         left join link_auction.site ast
                   on ac.id_site = ast.id
         left join link_auction.user au
                   on au.id = ast.id_user
         left join public.session_jdp sj
                   on sc.date_diff = sj.date_diff and
                      sc.id = sj.id_click
         left join temp_click_placement tcp
                   on sc.date_diff = tcp.session_datediff
                   and sc.id = tcp.id_click
where au.flags & 2 = 2
  and sc.flags & 16 = 0
  and sc.flags & 4096 = 0
group by ts.session_datediff,
         ts.session_id,
         placement

union all

select ts.session_datediff,
       ts.session_id,
       sum(scns.click_price * ic.value_to_usd) as revenue_usd,
       2 /*external statistics*/               as revenue_type_id,
       placement
from public.session_click_no_serp scns
         join temp_session ts
              on scns.date_diff = ts.session_datediff and
                 scns.id_session = ts.session_id
         join info_currency ic
              on ic.id = scns.id_currency
         join link_auction.campaign ac
              on ac.id = scns.id_campaign
         join link_auction.site ast
              on ac.id_site = ast.id
         join link_auction.user au
              on au.id = ast.id_user
         left join temp_click_no_serp_placement tcnsp
                   on scns.date_diff = tcnsp.session_datediff
                   and scns.id = tcnsp.id_click_no_serp
where au.flags & 2 = 2
  and scns.flags & 16 = 0
  and scns.flags & 4096 = 0
group by ts.session_datediff,
         ts.session_id,
         placement) t
 group by session_datediff, session_id, placement, revenue_type_id;

