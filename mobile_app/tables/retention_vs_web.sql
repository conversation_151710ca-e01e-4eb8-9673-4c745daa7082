declare @country_id int = ${country_id};

select         --@country as country_id,
               case when ac.type = 0 then 1 /*email user from web*/
                    when ac.type = 2 then 2 /*mobile app user*/
                end as user_type_id,
               CONVERT(INT, ac.verify_date)            as verify_date_diff,
               count( distinct ac.id_account )        as user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then ac.id_account  end) as week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then ac.id_account  end) as second_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then ac.id_account  end) as third_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then ac.id_account  end) as fourth_week_retention_user_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then s.id  end) as first_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then s.id  end) as second_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then s.id  end) as third_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then s.id  end) as fourth_week_session_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 1 and 7 then sc.id  end) as first_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 8 and 14 then sc.id  end) as second_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 15 and 21 then sc.id  end) as third_week_serp_click_cnt,
               count( distinct case when s.date_diff - CONVERT(INT, ac.verify_date)  between 22 and 30 then sc.id  end) as fourth_week_serp_click_cnt
        from account_contact ac with (nolock)
        left join dbo.session_account sa with (nolock)
                on sa.id_account = ac.id_account and
                   sa.date_diff between  CONVERT(INT, ac.verify_date) and CONVERT(INT, ac.verify_date) +30
        left join dbo.session s with (nolock)
          on sa.date_diff = s.date_diff and
             sa.id_session = s.id and
             s.flags & 1 = 0
        left join dbo.session_click sc with (nolock)
              on sc.date_diff = s.date_diff and
                 sc.id_session = s.id
        where  ac.verify_date between '2022-05-15' and '2022-07-10' and
              ac.type in (0,2)
group by       CONVERT(INT, ac.verify_date),
         case when ac.type = 0 then 1 /*email user from web*/
                    when ac.type = 2 then 2 /*mobile app user*/
                end;
