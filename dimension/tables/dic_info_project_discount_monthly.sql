SET NOCOUNT ON;

DECLARE @date_month date = :to_sqlcode_date_or_datediff_start;

SELECT @date_month                                      AS date_month,
       t.id_project,
       t.discount
FROM (SELECT DISTINCT p.id                              AS id_project,
                      ISNULL(acpd.discount, d.discount) AS discount
      FROM (SELECT d.contractor,
                   d.discount,
                   d.date,
                   ROW_NUMBER() OVER (PARTITION BY d.contractor ORDER BY d.date DESC) AS row_num
            FROM auction.client_discount d (NOLOCK)) d
               INNER JOIN auction.[user] u (NOLOCK) ON u.company = d.contractor
               INNER JOIN auction.[site] s (NOLOCK) ON s.id_user = u.id
               INNER JOIN dbo.info_project p (NOLOCK) ON p.id = s.id_project
               LEFT JOIN auction.client_project_discount acpd (NOLOCK) ON acpd.id_project = p.id
      WHERE d.row_num = 1
        AND (acpd.id_project IS NULL OR acpd.discount <> 0)
      UNION ALL
      SELECT DISTINCT acpd.id_project AS id_project,
                      acpd.discount   AS discount
      FROM auction.client_project_discount acpd (NOLOCK)
               INNER JOIN dbo.info_project p (NOLOCK) ON p.id = acpd.id_project
               INNER JOIN auction.[site] s (NOLOCK) ON s.id_project = p.id
               INNER JOIN auction.[user] u (NOLOCK) ON u.id = s.id_user
               LEFT JOIN auction.client_discount d (NOLOCK) ON d.contractor = u.company
      WHERE d.contractor IS NULL) t
WHERE t.discount > 0;
