create or replace function an.select_historical_job_sentinel(_dd_check integer)
    returns TABLE(country_id integer, id_job bigint, id_sentinel integer, sentinel character varying, type smallint)
    language plpgsql
as
$$
            declare _country_id int := (SELECT countries.id
                                        FROM dblink('dbname=postgres host=********* user=pentaho password=!faP@jhWYWb&3rwv options=-csearch_path=',
                                                    'select id, alpha_2
                                                    from dimension.countries') AS countries (id integer, alpha_2 varchar(2))
                                        WHERE lower(alpha_2) = current_database());

            declare _date_snap date := current_date - 1;

begin

                return query
                select _country_id,
                       js.id_job,
                       js.id_sentinel,
                       js.sentinel,
                       js.type
                from an.job_sentinel js
                left join link_dbo.job_region jr on js.id_job = jr.uid
                left join link_dbo.job j on jr.id_job = j.id
                where j.date_created::date < _date_snap or j.date_created::date is null;

end;
$$;

alter function an.select_historical_job_sentinel(integer) owner to rlu;
