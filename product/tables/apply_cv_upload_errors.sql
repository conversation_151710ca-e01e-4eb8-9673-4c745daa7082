select dim_c.alpha_2 as country,
       saa.id_jdp,
       saa.date_diff,
       saa.date,
       saa.flags
from imp.session_apply_action saa
left join dimension.countries dim_c on saa.country = dim_c.id
where saa.type in (29)
and saa.date >= (current_date - interval '1 day')::timestamp
and saa.country in (12, 11, 10, 9, 8, 7, 1)
group by dim_c.alpha_2,
         saa.id_jdp,
         saa.date_diff,
         saa.date,
         saa.flags;
