select  upper(country_code) as country,
		cast(DATE_ADD("1900-01-01", INTERVAL date_diff DAY) as datetime) as dt,
		round(avg(premium_active)) as jobs_premium,
        round(avg(not_premium)) as jobs_not_premium
        
from employer_statistics.job_snapshot
where DATE_ADD("1900-01-01", INTERVAL date_diff DAY) >= adddate(CURRENT_DATE(), INTERVAL -10 DAY)
group by cast(DATE_ADD("1900-01-01", INTERVAL date_diff DAY) as datetime),
		 country_code
