create table if not exists product.apply_metrics
(
	country varchar(3) default 'UA'::character varying not null,
	dt date not null,
	mobile smallint not null
		constraint apply_metrics_fk
			references product.info_mobile,
	returned smallint not null
		constraint apply_metrics_fk_2
			references product.info_returned,
	group_308 smallint default '0'::smallint not null,
	test_308 bigint default '0'::bigint not null,
	group_319 smallint default '0'::smallint not null,
	test_319 smallint default '0'::smallint not null,
	group_330 smallint default '0'::smallint not null,
	test_330 smallint default '0'::smallint not null,
	group_346 smallint default '0'::smallint not null,
	test_346 smallint default '0'::smallint not null,
	cnt_sess double precision,
	cnt_jdp_with_easy_apply_form double precision,
	applies_click double precision,
	applies_submit double precision,
	applies_submit_no_cv double precision,
	clients double precision,
	cv_create_clicks double precision,
	cv_creations double precision,
	cv_attaches double precision,
	phone_clicks double precision,
	constraint idx_90109_primary
		primary key (country, dt, mobile, returned, group_308, test_308, group_319, test_319, group_330, test_330, group_346, test_346)
);

alter table product.apply_metrics owner to postgres;

grant select on product.apply_metrics to npo;

grant select on product.apply_metrics to readonly;

grant delete, insert, select, update on product.apply_metrics to writeonly_product;

grant select on product.apply_metrics to ksha;

