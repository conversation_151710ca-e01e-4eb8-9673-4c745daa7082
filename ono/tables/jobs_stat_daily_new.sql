CREATE OR REPLACE PROCEDURE ono.prc_jobs_stat_daily_new(IN _start_date_date date)
    LANGUAGE plpgsql
AS
$$

BEGIN

    --=====
    DELETE
    FROM ono.jobs_stat_daily_new
    WHERE date::date = _start_date_date;
    --=====

    INSERT INTO ono.jobs_stat_daily_new(id_country, date, id_project, id_campaign, paid_job_count, organic_job_count,
                                        avg_cpc_for_paid_job_count_usd, flag_32_jobs_count, campaign_budget,
                                        campaign_daily_budget, is_price_per_job, campaign_flags, campaign_status,
                                        min_cpc_job_count, max_cpc_job_count, job_category_id,
                                        avg_cpc_for_paid_job_count, date_time, last_paid_hour)
    WITH sum_hour AS (SELECT jobs_stat_hourly.date,
                             jobs_stat_hourly.id_country,
                             jobs_stat_hourly.id_project,
                             SUM(paid_job_count) AS paid_job_count
                      FROM aggregation.jobs_stat_hourly
                      WHERE jobs_stat_hourly.date::date = _start_date_date
                        AND jobs_stat_hourly.date >= _start_date_date
                      GROUP BY jobs_stat_hourly.date,
                               jobs_stat_hourly.id_country,
                               jobs_stat_hourly.id_project),
         top_hour AS (SELECT date,
                             id_country,
                             id_project,
                             paid_job_count,
                             ROW_NUMBER()
                             OVER (PARTITION BY date::date, id_country,id_project ORDER BY paid_job_count DESC ) AS num,
                             MAX(
                             CASE
                                 WHEN paid_job_count > 0::numeric
                                     THEN DATE_PART('hour'::text, date)
                                 ELSE NULL::double precision
                             END)
                             OVER (PARTITION BY id_country, id_project, date::date)                              AS last_paid_hour
                      FROM sum_hour)

    SELECT jobs_stat_hourly.id_country,
           jobs_stat_hourly.date::date,
           jobs_stat_hourly.id_project,
           id_campaign,
           jobs_stat_hourly.paid_job_count,
           organic_job_count,
           avg_cpc_for_paid_job_count_usd,
           flag_32_jobs_count,
           campaign_budget,
           campaign_daily_budget,
           is_price_per_job,
           campaign_flags,
           campaign_status,
           min_cpc_job_count,
           max_cpc_job_count,
           job_category_id,
           avg_cpc_for_paid_job_count,
           jobs_stat_hourly.date AS date_time,
           last_paid_hour
    FROM aggregation.jobs_stat_hourly
             JOIN top_hour
                  ON jobs_stat_hourly.id_country = top_hour.id_country
                      AND jobs_stat_hourly.id_project = top_hour.id_project
                      AND jobs_stat_hourly.date = top_hour.date
                      AND top_hour.num = 1
    WHERE jobs_stat_hourly.date::date = _start_date_date
      AND jobs_stat_hourly.date >= _start_date_date;

END;
$$;
