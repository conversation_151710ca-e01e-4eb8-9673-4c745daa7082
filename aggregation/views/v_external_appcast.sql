create view aggregation.v_external_appcast
            (country, project_id, date, affiliate_traf_source, traffic_name, channel, click_reason, action_type,
             local_type, placement, id_campaign, is_mobile, is_bot, is_duplicated, cnt_clk, cnt_clk_app, clk_pr,
             clk_pr_usd, appcast_price)
as
SELECT COALESCE(ca.country, ext.country)       AS country,
       COALESCE(ca.id_project, ext.id_project) AS project_id,
       COALESCE(ca.datetime, ext.date)::date   AS date,
       ext.affiliate_traf_source,
       uts.name                                AS traffic_name,
       uts.channel,
       ca.click_reason,
       ca.action_type,
       CASE
           WHEN upper(ext.ip_cc::text) = ext.country::text THEN 'Local'::text
           WHEN ext.ip_cc IS NULL THEN 'w/o IP'::text
           ELSE 'Non Local'::text
           END                                 AS local_type,
       ext.placement,
       ca.id_campaign,
       ext.is_mobile,
       ext.is_bot,
       ext.is_duplicated,
       count(ext.id_away)                      AS cnt_clk,
       count(ca.id_click)                      AS cnt_clk_app,
       sum(ext.click_price)                    AS clk_pr,
       sum(ext.click_price_usd)                AS clk_pr_usd,
       sum(ca.price)                           AS appcast_price
FROM imp_statistic.ext_stat_away_match ext
         FULL JOIN imp_statistic.conversions_appcast ca
                   ON ca.id = ext.conversions_appcast_id AND ext.country::text = ca.country::text
         LEFT JOIN dimension.countries cou ON cou.alpha_2::text = ext.country::text
         LEFT JOIN dimension.u_traffic_source uts ON uts.id = ext.id_current_traf_source AND cou.id = uts.country
GROUP BY (COALESCE(ca.country, ext.country)), (COALESCE(ca.id_project, ext.id_project)),
         (COALESCE(ca.datetime, ext.date)::date), ext.affiliate_traf_source, uts.name, uts.channel, ca.click_reason,
         ca.action_type,
         (
             CASE
                 WHEN upper(ext.ip_cc::text) = ext.country::text THEN 'Local'::text
                 WHEN ext.ip_cc IS NULL THEN 'w/o IP'::text
                 ELSE 'Non Local'::text
                 END), ext.placement, ca.id_campaign, ext.is_mobile, ext.is_bot, ext.is_duplicated;

alter table aggregation.v_external_appcast
    owner to ono;

grant select on aggregation.v_external_appcast to user_agg_team;

grant select on aggregation.v_external_appcast to rku;

