create or replace view aggregation.v_invoices
            (month, country_id, contractor, id_project, site, currency, our_revenue_usd, external_statistic_coff,
             other_discount_coff, agency_discount, our_revenue_currentcy, client_revenue_currency, is_min_package,
             certified_cost, client_buying_model, country, affiliate_revenue_usd, id_user, row_number,
             is_job_ad_exchange_project, paid_revenue_usd, paid_click_cnt, paid_cost_usd, total_month_paid_click_cnt,
             manager)
as
with
    our_revenue as (
        select
            make_date(date_part('year'::text, auction_click_statistic_analytics.date::date)::integer,
                      date_part('month'::text, auction_click_statistic_analytics.date::date)::integer, 1) as month,
            auction_click_statistic_analytics.country_id,
            btrim(auction_click_statistic_analytics.contractor::text)                                     as contractor,
            auction_click_statistic_analytics.id_user,
            auction_click_statistic_analytics.id_project,
            auction_click_statistic_analytics.site,
            case
                when (auction_user.flags & 65536) = 65536 then 'external_CPA'::text
                when (auction_user.flags & 2) = 2 then 'external_CPC'::text
                else 'internal_CPC'::text
                end                                                                                       as client_buying_model,
            sum(
                    case
                        when auction_click_statistic_analytics.date::text < '2023-04-01'::text then
                                auction_click_statistic_analytics.total_value + coalesce(
                                    - (auction_click_statistic_analytics.total_value -
                                       (auction_click_statistic_analytics.click_count -
                                        coalesce(auction_click_statistic_analytics.test_count, 0) - coalesce(
                                                case
                                                    when auction_click_statistic_analytics.test_count > 0 then 0
                                                    else auction_click_statistic_analytics.duplicated_count
                                                    end, 0))::numeric * (auction_click_statistic_analytics.total_value /
                                                                         coalesce(auction_click_statistic_analytics.click_count, 1)::numeric)),
                                    0::numeric)
                        else auction_click_statistic_analytics.total_value
                        end)                                                                              as our_revenue_usd
        from
            aggregation.auction_click_statistic_analytics
            left join imp.auction_user
                      on auction_click_statistic_analytics.country_id = auction_user.country and
                         auction_click_statistic_analytics.id_user = auction_user.id
        group by
            (make_date(date_part('year'::text, auction_click_statistic_analytics.date::date)::integer,
                       date_part('month'::text, auction_click_statistic_analytics.date::date)::integer, 1)),
            auction_click_statistic_analytics.country_id, (btrim(auction_click_statistic_analytics.contractor::text)),
            auction_click_statistic_analytics.id_project, auction_click_statistic_analytics.site,
            (
                case
                    when (auction_user.flags & 65536) = 65536 then 'external_CPA'::text
                    when (auction_user.flags & 2) = 2 then 'external_CPC'::text
                    else 'internal_CPC'::text
                    end), auction_click_statistic_analytics.id_user
        union all
        select
            make_date(date_part('year'::text, auction_click_statistic_analytics.date)::integer,
                      date_part('month'::text, auction_click_statistic_analytics.date)::integer, 1) as month,
            auction_click_statistic_analytics.country_id,
            btrim(auction_click_statistic_analytics.contractor::text)                               as contractor,
            auction_click_statistic_analytics.id_user,
            auction_click_statistic_analytics.id_project,
            auction_click_statistic_analytics.site,
            case
                when (auction_user.flags & 65536) = 65536 then 'external_CPA'::text
                when (auction_user.flags & 2) = 2 then 'external_CPC'::text
                else 'internal_CPC'::text
                end                                                                                 as client_buying_model,
            sum(auction_click_statistic_analytics.total_value + coalesce(
                    - (auction_click_statistic_analytics.total_value - (auction_click_statistic_analytics.click_count -
                                                                        auction_click_statistic_analytics.test_count)::numeric *
                                                                       (auction_click_statistic_analytics.total_value /
                                                                        nullif(auction_click_statistic_analytics.click_count, 0)::numeric)),
                    0::numeric))                                                                    as our_revenue_usd
        from
            imp_statistic.auction_click_statistic auction_click_statistic_analytics
            left join imp.auction_user
                      on auction_click_statistic_analytics.country_id = auction_user.country and
                         auction_click_statistic_analytics.id_user = auction_user.id
        where
              auction_click_statistic_analytics.date >= '2022-01-01'::date
          and auction_click_statistic_analytics.date <= '2022-05-31'::date
        group by
            (make_date(date_part('year'::text, auction_click_statistic_analytics.date)::integer,
                       date_part('month'::text, auction_click_statistic_analytics.date)::integer, 1)),
            auction_click_statistic_analytics.country_id, (btrim(auction_click_statistic_analytics.contractor::text)),
            auction_click_statistic_analytics.id_project, auction_click_statistic_analytics.site,
            (
                case
                    when (auction_user.flags & 65536) = 65536 then 'external_CPA'::text
                    when (auction_user.flags & 2) = 2 then 'external_CPC'::text
                    else 'internal_CPC'::text
                    end), auction_click_statistic_analytics.id_user
    ),
    external_coff as (
        select
            tabl.country_id,
            tabl.country,
            tabl.date,
            tabl.contractor,
            tabl.site,
            tabl.currency,
            tabl.our_revenue,
            tabl.external_statistic_discount,
            tabl.other_discount,
            tabl.external_statistic_discount * 1.0 / tabl.our_revenue                                                 as external_statistic_coff,
            tabl.other_discount * 1.0 / tabl.our_revenue                                                              as other_discount_coff,
            tabl.is_min_package,
            row_number()
            over (partition by tabl.country_id, tabl.date, tabl.contractor, tabl.site order by tabl.our_revenue desc) as row_number
        from
            (select
                 invoice_row.country_id,
                 invoice_row.country,
                 invoice_row.date,
                 invoice_row.contractor,
                 invoice_row.site,
                 invoice_row.currency,
                 max(
                         case
                             when invoice_row.min_rate is not null then 1
                             else 0
                             end) as is_min_package,
                 sum(
                         case
                             when invoice_row.type::text = any
                                  (array ['Clicks according to Jooble statistics'::character varying::text, 'Кліки/Клики'::character varying::text, 'Кліки'::character varying::text])
                                 then invoice_row.spent
                             else null::numeric
                             end) as our_revenue,
                 sum(
                         case
                             when invoice_row.type::text = 'Adjustment'::text then invoice_row.spent
                             else null::numeric
                             end) as external_statistic_discount,
                 sum(
                         case
                             when invoice_row.type::text = any
                                  (array ['Коригування обмеження бюджету / Корректировка ограничения бюджета'::character varying::text, 'Budget discount'::character varying::text, 'Invoice correction'::character varying::text])
                                 then invoice_row.spent
                             else null::numeric
                             end) as other_discount
             from
                 aggregation.v_invoices_raw invoice_row
                 join dimension.countries
                      on invoice_row.country::text = countries.alpha_2::text
             group by
                 invoice_row.country_id, invoice_row.country, invoice_row.date, invoice_row.contractor,
                 invoice_row.site, invoice_row.currency) tabl
    ),
    affiliate as (
        select
            date_trunc('month', v_main_metrics_agg.click_date)::date as date,
            v_main_metrics_agg.id_project,
            v_main_metrics_agg.id_country,
            sum(v_main_metrics_agg.certified_cost_usd)               as certified_cost
        from
            affiliate.v_main_metrics_agg
        where
            v_main_metrics_agg.click_date >= '2022-01-01'
        group by
            date_trunc('month', v_main_metrics_agg.click_date)::date, v_main_metrics_agg.id_project,
            v_main_metrics_agg.id_country
    ),
    jdp_away_clicks_agg as (
        select
            jdp_away_clicks_agg.country_id,
            make_date(date_part('year'::text,
                                fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff))::integer,
                      date_part('month'::text,
                                fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff))::integer,
                      1)     as date,
            jdp_away_clicks_agg.project_id,
            sum(
                    case
                        when u_traffic_source.channel::text = 'Affiliate'::text then jdp_away_clicks_agg.revenue_usd
                        else null::numeric
                        end) as affiliate_revenue_usd,
            sum(
                    case
                        when u_traffic_source.channel::text <> 'Affiliate'::text then jdp_away_clicks_agg.revenue_usd
                        else null::numeric
                        end) as paid_revenue_usd,
            sum(
                    case
                        when u_traffic_source.channel::text <> 'Affiliate'::text then jdp_away_clicks_agg.jdp_away_count
                        else null::integer
                        end) as paid_click_cnt
        from
            aggregation.jdp_away_clicks_agg
            join dimension.u_traffic_source
                 on jdp_away_clicks_agg.country_id = u_traffic_source.country and
                    jdp_away_clicks_agg.id_current_traf_source = u_traffic_source.id
        where
              jdp_away_clicks_agg.action_datediff >= 44560
          and u_traffic_source.is_paid is true
        group by
            jdp_away_clicks_agg.country_id,
            (make_date(date_part('year'::text,
                                 fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff))::integer,
                       date_part('month'::text,
                                 fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff))::integer, 1)),
            jdp_away_clicks_agg.project_id
    ),
    affiliate_part as (
        select
            jdp_away_clicks_agg.country_id,
            jdp_away_clicks_agg.date,
            jdp_away_clicks_agg.project_id,
            jdp_away_clicks_agg.affiliate_revenue_usd,
            jdp_away_clicks_agg.paid_revenue_usd,
            jdp_away_clicks_agg.paid_click_cnt,
            sum(jdp_away_clicks_agg.paid_click_cnt)
            over (partition by jdp_away_clicks_agg.date, jdp_away_clicks_agg.country_id) as total_month_paid_click_cnt
        from
            jdp_away_clicks_agg
    ),
    ppc_costs as (
        select
            make_date(date_part('year'::text, v_paid_traffic_metrics_agg.date)::integer,
                      date_part('month'::text, v_paid_traffic_metrics_agg.date)::integer, 1) as month,
            countries.name_country_eng                                                       as country,
            sum(v_paid_traffic_metrics_agg.cost_usd)                                         as paid_cost_usd
        from
            aggregation.v_paid_traffic_metrics_agg
            join dimension.countries
                 on v_paid_traffic_metrics_agg.country::text = lower(countries.alpha_2::text)
        where
            v_paid_traffic_metrics_agg.date >= '2022-01-01'::date
        group by
            (make_date(date_part('year'::text, v_paid_traffic_metrics_agg.date)::integer,
                       date_part('month'::text, v_paid_traffic_metrics_agg.date)::integer, 1)),
            countries.name_country_eng
    ),
    final as (
        select
            our_revenue.month,
            our_revenue.country_id,
            our_revenue.contractor,
            our_revenue.id_project,
            our_revenue.site,
            external_coff.currency,
            our_revenue.our_revenue_usd,
            external_coff.external_statistic_coff,
            external_coff.other_discount_coff,
            - dic_agency_discount.agency_discount                                                                     as agency_discount,
            external_coff.our_revenue                                                                                 as our_revenue_currentcy,
            external_coff.our_revenue + external_coff.external_statistic_discount                                     as client_revenue_currency,
            external_coff.is_min_package,
            affiliate.certified_cost,
            our_revenue.client_buying_model,
            countries.name_country_eng                                                                                as country,
            affiliate_part.affiliate_revenue_usd,
            our_revenue.id_user,
            external_coff.row_number,
            case
                when info_project.hide_in_search then 1
                else 0
                end                                                                                                   as is_job_ad_exchange_project,
            affiliate_part.paid_revenue_usd,
            affiliate_part.paid_click_cnt,
            ppc_costs.paid_cost_usd::double precision * (affiliate_part.paid_click_cnt::numeric * 1.0 /
                                                         affiliate_part.total_month_paid_click_cnt)::double precision as paid_cost_usd,
            affiliate_part.total_month_paid_click_cnt,
            v_sale_manager.sale_manager                                                                               as manager
        from
            our_revenue
            left join external_coff
                      on our_revenue.month = external_coff.date and
                         our_revenue.country_id = external_coff.country_id and
                         our_revenue.contractor = external_coff.contractor and
                         our_revenue.site::text = external_coff.site::text
            left join affiliate
                      on our_revenue.month = affiliate.date and our_revenue.country_id = affiliate.id_country and
                         our_revenue.id_project = affiliate.id_project
            left join aggregation.dic_agency_discount
                      on our_revenue.contractor = dic_agency_discount.contractor
            left join dimension.countries
                      on our_revenue.country_id = countries.id
            left join affiliate_part
                      on our_revenue.month = affiliate_part.date and
                         our_revenue.country_id = affiliate_part.country_id and
                         our_revenue.id_project = affiliate_part.project_id and
                         affiliate_part.total_month_paid_click_cnt > 0::numeric
            left join dimension.info_project
                      on our_revenue.country_id = info_project.country and our_revenue.id_project = info_project.id
            left join ppc_costs
                      on our_revenue.month = ppc_costs.month and countries.name_country_eng::text = ppc_costs.country::text
            left join aggregation.v_sale_manager
                      on our_revenue.country_id = v_sale_manager.country and
                         our_revenue.id_project = v_sale_manager.id_project
    )
select
    final.month,
    final.country_id,
    final.contractor,
    final.id_project,
    final.site,
    final.currency,
    final.our_revenue_usd,
    final.external_statistic_coff,
    final.other_discount_coff,
    final.agency_discount,
    final.our_revenue_currentcy,
    final.client_revenue_currency,
    final.is_min_package,
    final.certified_cost,
    final.client_buying_model,
    final.country,
    final.affiliate_revenue_usd,
    final.id_user,
    final.row_number,
    final.is_job_ad_exchange_project,
    final.paid_revenue_usd,
    final.paid_click_cnt,
    final.paid_cost_usd,
    final.total_month_paid_click_cnt,
    final.manager
from
    final;

alter table aggregation.v_invoices
    owner to ono;

grant select on aggregation.v_invoices to readonly;

grant select on aggregation.v_invoices to write_ono;

grant select on aggregation.v_invoices to readonly_aggregation;
