create or replace view aggregation.v_registration_email_funnel(country_id, country_name, update_datetime, session_datediff, is_complete_data, current_traffic_channel_name, is_returned, device_type_id, is_local, registration_source_id, registration_source_name, serp_test_group_lists, email_test_group_lists, session_cnt, new_account_cnt, new_verified_account_cnt, is_account_with_at_least_1_open_letter_cnt_0_7, is_account_with_at_least_2_open_letter_cnt_0_7, is_account_with_at_least_1_interact_letter_cnt_0_7, is_account_with_at_least_2_interact_letter_cnt_0_7, verified_email_revenue, verified_email_revenue_0_3, verified_email_revenue_4_7, verified_email_revenue_8_14, verified_total_revenue, verified_total_revenue_0_3, verified_total_revenue_4_7, verified_total_revenue_8_14, verified_away_clicks_premium, verified_away_clicks_premium_0_3, verified_away_clicks_premium_4_7, verified_away_clicks_premium_8_14, verified_clicks_free, verified_away_clicks_free_0_3, verified_away_clicks_free_4_7, verified_away_clicks_free_8_14, letter_1_open_cnt_0_14, letter_1_open_cnt_0_3, letter_1_open_cnt_4_7, letter_1_open_cnt_8_14, letter_8_open_cnt_0_14, letter_8_open_cnt_0_3, letter_8_open_cnt_4_7, letter_8_open_cnt_8_14, letter_71_open_cnt_0_14, letter_71_open_cnt_0_3, letter_71_open_cnt_4_7, letter_71_open_cnt_8_14, letter_1_interact_cnt_0_14, letter_1_interact_cnt_0_3, letter_1_interact_cnt_4_7, letter_1_interact_cnt_8_14, letter_8_interact_cnt_0_14, letter_8_interact_cnt_0_3, letter_8_interact_cnt_4_7, letter_8_interact_cnt_8_14, letter_71_interact_cnt_0_14, letter_71_interact_cnt_0_3, letter_71_interact_cnt_4_7, letter_71_interact_cnt_8_14, is_account_with_verified_email_revenue_cnt_0_7, is_account_with_at_least_1_job_click_cnt_0_3, is_account_with_at_least_2_job_click_cnt_0_3, is_account_with_at_least_3_job_click_cnt_0_3, is_account_with_at_least_1_job_click_cnt_0_7, is_account_with_at_least_2_job_click_cnt_0_7, is_account_with_at_least_3_job_click_cnt_0_7, letter_1_job_click_0_3, letter_8_job_click_0_3, letter_71_job_click_0_3, letter_1_job_click_0_7, letter_8_job_click_0_7, letter_71_job_click_0_7, email_job_click_0_3, email_job_click_0_7, is_account_with_at_least_1_interact_letter_cnt_8_14) as
	WITH cte_registration_email_funnel AS (
         SELECT registration_email_funnel.country_id,
            countries.name_country_eng AS country_name,
            registration_email_funnel.update_datetime,
            registration_email_funnel.session_datediff,
            registration_email_funnel.is_complete_data,
            COALESCE(u_traffic_source.name, 'Undefined'::character varying) AS current_traffic_source_name,
            COALESCE(u_traffic_source.channel, 'Undefined'::character varying) AS current_traffic_channel_name,
            registration_email_funnel.is_returned,
            registration_email_funnel.device_type_id,
            registration_email_funnel.is_local,
            registration_email_funnel.registration_source_id,
            auth_source.source_name AS registration_source_name,
            registration_email_funnel.serp_test_group_lists,
            registration_email_funnel.email_test_group_lists,
            registration_email_funnel.account_id,
            registration_email_funnel.session_cnt,
            registration_email_funnel.new_account_cnt,
            registration_email_funnel.new_verified_account_cnt,
            registration_email_funnel.account_with_at_least_1_open_letter_cnt_0_7,
            registration_email_funnel.account_with_at_least_2_open_letter_cnt_0_7,
            registration_email_funnel.account_with_at_least_1_interact_letter_cnt_0_7,
            registration_email_funnel.account_with_at_least_2_interact_letter_cnt_0_7,
            registration_email_funnel.verified_email_revenue,
            registration_email_funnel.verified_email_revenue_0_3,
            registration_email_funnel.verified_email_revenue_4_7,
            registration_email_funnel.verified_email_revenue_8_14,
            registration_email_funnel.verified_total_revenue,
            registration_email_funnel.verified_total_revenue_0_3,
            registration_email_funnel.verified_total_revenue_4_7,
            registration_email_funnel.verified_total_revenue_8_14,
            registration_email_funnel.verified_away_clicks_premium,
            registration_email_funnel.verified_away_clicks_premium_0_3,
            registration_email_funnel.verified_away_clicks_premium_4_7,
            registration_email_funnel.verified_away_clicks_premium_8_14,
            registration_email_funnel.verified_clicks_free,
            registration_email_funnel.verified_away_clicks_free_0_3,
            registration_email_funnel.verified_away_clicks_free_4_7,
            registration_email_funnel.verified_away_clicks_free_8_14,
            registration_email_funnel.letter_1_open_cnt_0_14,
            registration_email_funnel.letter_1_open_cnt_0_3,
            registration_email_funnel.letter_1_open_cnt_4_7,
            registration_email_funnel.letter_1_open_cnt_8_14,
            registration_email_funnel.letter_8_open_cnt_0_14,
            registration_email_funnel.letter_8_open_cnt_0_3,
            registration_email_funnel.letter_8_open_cnt_4_7,
            registration_email_funnel.letter_8_open_cnt_8_14,
            registration_email_funnel.letter_71_open_cnt_0_14,
            registration_email_funnel.letter_71_open_cnt_0_3,
            registration_email_funnel.letter_71_open_cnt_4_7,
            registration_email_funnel.letter_71_open_cnt_8_14,
            registration_email_funnel.letter_1_interact_cnt_0_14,
            registration_email_funnel.letter_1_interact_cnt_0_3,
            registration_email_funnel.letter_1_interact_cnt_4_7,
            registration_email_funnel.letter_1_interact_cnt_8_14,
            registration_email_funnel.letter_8_interact_cnt_0_14,
            registration_email_funnel.letter_8_interact_cnt_0_3,
            registration_email_funnel.letter_8_interact_cnt_4_7,
            registration_email_funnel.letter_8_interact_cnt_8_14,
            registration_email_funnel.letter_71_interact_cnt_0_14,
            registration_email_funnel.letter_71_interact_cnt_0_3,
            registration_email_funnel.letter_71_interact_cnt_4_7,
            registration_email_funnel.letter_71_interact_cnt_8_14,
            registration_email_funnel.account_with_verified_email_revenue_cnt_0_7,
            vaejc.letter_1_job_click_0_3,
            vaejc.letter_8_job_click_0_3,
            vaejc.letter_71_job_click_0_3,
            vaejc.letter_1_job_click_0_7,
            vaejc.letter_8_job_click_0_7,
            vaejc.letter_71_job_click_0_7,
            COALESCE(vaejc.letter_1_job_click_0_3, 0::numeric) + COALESCE(vaejc.letter_8_job_click_0_3, 0::numeric) + COALESCE(vaejc.letter_71_job_click_0_3, 0::numeric) AS email_job_click_0_3,
            COALESCE(vaejc.letter_1_job_click_0_7, 0::numeric) + COALESCE(vaejc.letter_8_job_click_0_7, 0::numeric) + COALESCE(vaejc.letter_71_job_click_0_7, 0::numeric) AS email_job_click_0_7,
            COALESCE(registration_email_funnel.letter_1_interact_cnt_8_14, 0::numeric) + COALESCE(registration_email_funnel.letter_71_interact_cnt_8_14, 0::numeric) + COALESCE(registration_email_funnel.letter_8_interact_cnt_8_14, 0::numeric) AS letter_ineraction_cnt_8_14
           FROM aggregation.registration_email_funnel
             LEFT JOIN dimension.countries ON registration_email_funnel.country_id = countries.id
             LEFT JOIN dimension.auth_source ON registration_email_funnel.registration_source_id = auth_source.id
             LEFT JOIN dimension.u_traffic_source ON registration_email_funnel.country_id = u_traffic_source.country AND u_traffic_source.id = registration_email_funnel.current_traffic_source_id
             LEFT JOIN aggregation.v_account_email_job_click vaejc ON vaejc.country_id = registration_email_funnel.country_id AND vaejc.account_id = registration_email_funnel.account_id
        )
 SELECT cte_registration_email_funnel.country_id,
    cte_registration_email_funnel.country_name,
    cte_registration_email_funnel.update_datetime,
    cte_registration_email_funnel.session_datediff,
    cte_registration_email_funnel.is_complete_data,
    cte_registration_email_funnel.current_traffic_channel_name,
    cte_registration_email_funnel.is_returned,
    cte_registration_email_funnel.device_type_id,
    cte_registration_email_funnel.is_local,
    cte_registration_email_funnel.registration_source_id,
    cte_registration_email_funnel.registration_source_name,
    cte_registration_email_funnel.serp_test_group_lists,
    cte_registration_email_funnel.email_test_group_lists,
    sum(cte_registration_email_funnel.session_cnt) AS session_cnt,
    sum(cte_registration_email_funnel.new_account_cnt) AS new_account_cnt,
    sum(cte_registration_email_funnel.new_verified_account_cnt) AS new_verified_account_cnt,
    cte_registration_email_funnel.account_with_at_least_1_open_letter_cnt_0_7::smallint AS is_account_with_at_least_1_open_letter_cnt_0_7,
    cte_registration_email_funnel.account_with_at_least_2_open_letter_cnt_0_7::smallint AS is_account_with_at_least_2_open_letter_cnt_0_7,
    cte_registration_email_funnel.account_with_at_least_1_interact_letter_cnt_0_7::smallint AS is_account_with_at_least_1_interact_letter_cnt_0_7,
    cte_registration_email_funnel.account_with_at_least_2_interact_letter_cnt_0_7::smallint AS is_account_with_at_least_2_interact_letter_cnt_0_7,
    sum(cte_registration_email_funnel.verified_email_revenue) AS verified_email_revenue,
    sum(cte_registration_email_funnel.verified_email_revenue_0_3) AS verified_email_revenue_0_3,
    sum(cte_registration_email_funnel.verified_email_revenue_4_7) AS verified_email_revenue_4_7,
    sum(cte_registration_email_funnel.verified_email_revenue_8_14) AS verified_email_revenue_8_14,
    sum(cte_registration_email_funnel.verified_total_revenue) AS verified_total_revenue,
    sum(cte_registration_email_funnel.verified_total_revenue_0_3) AS verified_total_revenue_0_3,
    sum(cte_registration_email_funnel.verified_total_revenue_4_7) AS verified_total_revenue_4_7,
    sum(cte_registration_email_funnel.verified_total_revenue_8_14) AS verified_total_revenue_8_14,
    sum(cte_registration_email_funnel.verified_away_clicks_premium) AS verified_away_clicks_premium,
    sum(cte_registration_email_funnel.verified_away_clicks_premium_0_3) AS verified_away_clicks_premium_0_3,
    sum(cte_registration_email_funnel.verified_away_clicks_premium_4_7) AS verified_away_clicks_premium_4_7,
    sum(cte_registration_email_funnel.verified_away_clicks_premium_8_14) AS verified_away_clicks_premium_8_14,
    sum(cte_registration_email_funnel.verified_clicks_free) AS verified_clicks_free,
    sum(cte_registration_email_funnel.verified_away_clicks_free_0_3) AS verified_away_clicks_free_0_3,
    sum(cte_registration_email_funnel.verified_away_clicks_free_4_7) AS verified_away_clicks_free_4_7,
    sum(cte_registration_email_funnel.verified_away_clicks_free_8_14) AS verified_away_clicks_free_8_14,
    sum(cte_registration_email_funnel.letter_1_open_cnt_0_14) AS letter_1_open_cnt_0_14,
    sum(cte_registration_email_funnel.letter_1_open_cnt_0_3) AS letter_1_open_cnt_0_3,
    sum(cte_registration_email_funnel.letter_1_open_cnt_4_7) AS letter_1_open_cnt_4_7,
    sum(cte_registration_email_funnel.letter_1_open_cnt_8_14) AS letter_1_open_cnt_8_14,
    sum(cte_registration_email_funnel.letter_8_open_cnt_0_14) AS letter_8_open_cnt_0_14,
    sum(cte_registration_email_funnel.letter_8_open_cnt_0_3) AS letter_8_open_cnt_0_3,
    sum(cte_registration_email_funnel.letter_8_open_cnt_4_7) AS letter_8_open_cnt_4_7,
    sum(cte_registration_email_funnel.letter_8_open_cnt_8_14) AS letter_8_open_cnt_8_14,
    sum(cte_registration_email_funnel.letter_71_open_cnt_0_14) AS letter_71_open_cnt_0_14,
    sum(cte_registration_email_funnel.letter_71_open_cnt_0_3) AS letter_71_open_cnt_0_3,
    sum(cte_registration_email_funnel.letter_71_open_cnt_4_7) AS letter_71_open_cnt_4_7,
    sum(cte_registration_email_funnel.letter_71_open_cnt_8_14) AS letter_71_open_cnt_8_14,
    sum(cte_registration_email_funnel.letter_1_interact_cnt_0_14) AS letter_1_interact_cnt_0_14,
    sum(cte_registration_email_funnel.letter_1_interact_cnt_0_3) AS letter_1_interact_cnt_0_3,
    sum(cte_registration_email_funnel.letter_1_interact_cnt_4_7) AS letter_1_interact_cnt_4_7,
    sum(cte_registration_email_funnel.letter_1_interact_cnt_8_14) AS letter_1_interact_cnt_8_14,
    sum(cte_registration_email_funnel.letter_8_interact_cnt_0_14) AS letter_8_interact_cnt_0_14,
    sum(cte_registration_email_funnel.letter_8_interact_cnt_0_3) AS letter_8_interact_cnt_0_3,
    sum(cte_registration_email_funnel.letter_8_interact_cnt_4_7) AS letter_8_interact_cnt_4_7,
    sum(cte_registration_email_funnel.letter_8_interact_cnt_8_14) AS letter_8_interact_cnt_8_14,
    sum(cte_registration_email_funnel.letter_71_interact_cnt_0_14) AS letter_71_interact_cnt_0_14,
    sum(cte_registration_email_funnel.letter_71_interact_cnt_0_3) AS letter_71_interact_cnt_0_3,
    sum(cte_registration_email_funnel.letter_71_interact_cnt_4_7) AS letter_71_interact_cnt_4_7,
    sum(cte_registration_email_funnel.letter_71_interact_cnt_8_14) AS letter_71_interact_cnt_8_14,
    cte_registration_email_funnel.account_with_verified_email_revenue_cnt_0_7::smallint AS is_account_with_verified_email_revenue_cnt_0_7,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 1::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_1_job_click_cnt_0_3,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 2::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_2_job_click_cnt_0_3,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 3::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_3_job_click_cnt_0_3,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 1::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_1_job_click_cnt_0_7,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 2::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_2_job_click_cnt_0_7,
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 3::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_3_job_click_cnt_0_7,
    sum(cte_registration_email_funnel.letter_1_job_click_0_3) AS letter_1_job_click_0_3,
    sum(cte_registration_email_funnel.letter_8_job_click_0_3) AS letter_8_job_click_0_3,
    sum(cte_registration_email_funnel.letter_71_job_click_0_3) AS letter_71_job_click_0_3,
    sum(cte_registration_email_funnel.letter_1_job_click_0_7) AS letter_1_job_click_0_7,
    sum(cte_registration_email_funnel.letter_8_job_click_0_7) AS letter_8_job_click_0_7,
    sum(cte_registration_email_funnel.letter_71_job_click_0_7) AS letter_71_job_click_0_7,
    sum(cte_registration_email_funnel.email_job_click_0_3) AS email_job_click_0_3,
    sum(cte_registration_email_funnel.email_job_click_0_7) AS email_job_click_0_7,
        CASE
            WHEN cte_registration_email_funnel.letter_ineraction_cnt_8_14 >= 1::numeric THEN 1
            ELSE 0
        END::smallint AS is_account_with_at_least_1_interact_letter_cnt_8_14
   FROM cte_registration_email_funnel
  GROUP BY cte_registration_email_funnel.country_id, cte_registration_email_funnel.country_name, cte_registration_email_funnel.update_datetime, cte_registration_email_funnel.session_datediff, cte_registration_email_funnel.is_complete_data, cte_registration_email_funnel.current_traffic_channel_name, cte_registration_email_funnel.is_returned, cte_registration_email_funnel.device_type_id, cte_registration_email_funnel.is_local, cte_registration_email_funnel.registration_source_id, cte_registration_email_funnel.registration_source_name, cte_registration_email_funnel.serp_test_group_lists, cte_registration_email_funnel.email_test_group_lists, cte_registration_email_funnel.account_with_at_least_1_open_letter_cnt_0_7, cte_registration_email_funnel.account_with_at_least_2_open_letter_cnt_0_7, cte_registration_email_funnel.account_with_at_least_1_interact_letter_cnt_0_7, cte_registration_email_funnel.account_with_at_least_2_interact_letter_cnt_0_7, cte_registration_email_funnel.account_with_verified_email_revenue_cnt_0_7, (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 1::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 2::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_3 >= 3::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 1::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 2::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.email_job_click_0_7 >= 3::numeric THEN 1
            ELSE 0
        END), (
        CASE
            WHEN cte_registration_email_funnel.letter_ineraction_cnt_8_14 >= 1::numeric THEN 1
            ELSE 0
        END::smallint);

alter table aggregation.v_registration_email_funnel owner to rlu;

grant select on aggregation.v_registration_email_funnel to readonly;

grant select on aggregation.v_registration_email_funnel to write_ono;

grant select on aggregation.v_registration_email_funnel to readonly_aggregation;

