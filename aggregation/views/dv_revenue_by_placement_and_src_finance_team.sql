create view aggregation.dv_revenue_by_placement_and_src_finance_team
            (date, placement, traffic_channel, traffic_source, country, country_code, revenue_usd) as
SELECT fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff) AS date,
       adv_revenue_by_placement_and_src.placement::text                            AS placement,
       u_traffic_source.channel                                                    AS traffic_channel,
       u_traffic_source.name                                                       AS traffic_source,
       countries.name_country_eng                                                  AS country,
       countries.alpha_2                                                           AS country_code,
       adv_revenue_by_placement_and_src.revenue_usd
FROM imp.adv_revenue_by_placement_and_src
         LEFT JOIN dimension.u_traffic_source ON adv_revenue_by_placement_and_src.country = u_traffic_source.country AND
                                                 adv_revenue_by_placement_and_src.id_traf_source = u_traffic_source.id
         LEFT JOIN dimension.countries ON adv_revenue_by_placement_and_src.country = countries.id
WHERE adv_revenue_by_placement_and_src.date_diff >= 44226
  AND adv_revenue_by_placement_and_src.date_diff <= 44710
UNION ALL
SELECT fn_get_timestamp_from_date_diff(adv_revenue_by_placement_and_src.date_diff) AS date,
       adv_revenue_by_placement_and_src.placement::text                            AS placement,
       u_traffic_source.channel                                                    AS traffic_channel,
       u_traffic_source.name                                                       AS traffic_source,
       countries.name_country_eng                                                  AS country,
       countries.alpha_2                                                           AS country_code,
       adv_revenue_by_placement_and_src.revenue_usd
FROM aggregation.adv_revenue_by_placement_and_src_analytics adv_revenue_by_placement_and_src
         LEFT JOIN dimension.u_traffic_source
                   ON adv_revenue_by_placement_and_src.country_id = u_traffic_source.country AND
                      adv_revenue_by_placement_and_src.id_current_traf_source = u_traffic_source.id
         LEFT JOIN dimension.countries ON adv_revenue_by_placement_and_src.country_id = countries.id
WHERE adv_revenue_by_placement_and_src.date_diff >= 44711;

alter table aggregation.dv_revenue_by_placement_and_src_finance_team
    owner to ono;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to readonly;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to ypr;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to vbe;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to vnov;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to readonly_aggregation;

grant select on aggregation.dv_revenue_by_placement_and_src_finance_team to "pavlo.kvasnii";