create view aggregation.v_seo_abtest_agg(union_group, group_num, iteration, test_num, date, test_country, metric, value) as
	select seo_abtest_agg.union_group,
       seo_abtest_agg.group_num,
       seo_abtest_agg.iteration,
       seo_abtest_agg.test_num,
       seo_abtest_agg.date,
       seo_abtest_agg.test_country,
       seo_abtest_agg.metric,
       seo_abtest_agg.value
from aggregation.seo_abtest_agg;

alter table aggregation.v_seo_abtest_agg owner to ono;

grant select on aggregation.v_seo_abtest_agg to readonly;

grant select on aggregation.v_seo_abtest_agg to user_agg_team;

grant select on aggregation.v_seo_abtest_agg to vnov;

grant select on aggregation.v_seo_abtest_agg to readonly_aggregation;

grant select on aggregation.v_seo_abtest_agg to "pavlo.kvasnii";

