create or replace view aggregation.v_email_abtest_agg
            (country, action_date, account_test_num, letter_type, metric_name, account_age, union_group, value,
             alert_cnt, id_project, project_name)
as
SELECT c.name_country_eng           AS country,
       eaa.action_date,
       eaa.account_test_num,
       CASE
           WHEN eaa.letter_type = ANY (ARRAY [1, 4, 6, 8, 71, 78, 84, 86]) THEN eaa.letter_type::text
           WHEN eaa.letter_type IS NULL THEN NULL::text
           ELSE 'other'::text
           END                      AS letter_type,
       btrim(eaa.metric_name::text) AS metric_name,
       btrim(eaa.account_age::text) AS account_age,
       1                            AS union_group,
       sum(eaa.metric_cnt)          AS value,
       eaa.alert_cnt,
       eaa.id_project,
       info_project.name            AS project_name
FROM aggregation.email_abtest_agg eaa
LEFT JOIN dimension.countries c ON eaa.country_id = c.id
LEFT JOIN dimension.info_project ON eaa.country_id = info_project.country AND eaa.id_project = info_project.id
WHERE eaa.account_test_num IS NOT NULL
  AND eaa.action_date >= (CURRENT_DATE - 50)
GROUP BY c.name_country_eng, eaa.action_date, eaa.account_test_num,
         (
             CASE
                 WHEN eaa.letter_type = ANY (ARRAY [1, 4, 6, 8, 71, 78, 84, 86]) THEN eaa.letter_type::text
                 WHEN eaa.letter_type IS NULL THEN NULL::text
                 ELSE 'other'::text
                 END), (btrim(eaa.metric_name::text)), (btrim(eaa.account_age::text)), eaa.alert_cnt, eaa.id_project,
         info_project.name
UNION ALL
SELECT c.name_country_eng           AS country,
       eaa.action_date,
       eaa.account_test_num,
       CASE
           WHEN eaa.letter_type = ANY (ARRAY [1, 4, 6, 8, 71, 78, 84, 86]) THEN eaa.letter_type::text
           WHEN eaa.letter_type IS NULL THEN NULL::text
           ELSE 'other'::text
           END                      AS letter_type,
       btrim(eaa.metric_name::text) AS metric_name,
       btrim(eaa.account_age::text) AS account_age,
       2                            AS union_group,
       sum(eaa.metric_cnt)          AS value,
       eaa.alert_cnt,
       eaa.id_project,
       info_project.name            AS project_name
FROM aggregation.email_abtest_agg eaa
LEFT JOIN dimension.countries c ON eaa.country_id = c.id
LEFT JOIN dimension.info_project ON eaa.country_id = info_project.country AND eaa.id_project = info_project.id
WHERE eaa.account_test_num IS NOT NULL
  AND eaa.action_date >= (CURRENT_DATE - 50)
GROUP BY c.name_country_eng, eaa.action_date, eaa.account_test_num,
         (
             CASE
                 WHEN eaa.letter_type = ANY (ARRAY [1, 4, 6, 8, 71, 78, 84, 86]) THEN eaa.letter_type::text
                 WHEN eaa.letter_type IS NULL THEN NULL::text
                 ELSE 'other'::text
                 END), (btrim(eaa.metric_name::text)), (btrim(eaa.account_age::text)), eaa.alert_cnt, eaa.id_project,
         info_project.name;

alter table aggregation.v_email_abtest_agg
    owner to ono;
