create or replace view aggregation.v_outreach_final_result
            (id_period, period_name, period_dt_start, period_dt_end, period_closed, month_start, period_month,
             period_quarter, period_year, id_team, pay_type, account_id, account_name, account_login, account_busyness,
             account_region_name, account_region_name_full, account_pay_type_name, account_team_id, account_team_name,
             account_deleted_date, account_is_deleted, account_lead_id, account_lead_name, country, country_name,
             add_link, add_score,
             link_total, score_total, deleted_link, deleted_score, add_score_eur, score_eur_total, deleted_score_eur,
             bonus, bonus_currency, bonus_link, bonus_score, report_update_time)
as
WITH union_final AS (SELECT cl.id_period,
                            cl.id_team,
                            cl.pay_type,
                            cl.id_account          AS account_id,
                            cl.account_name,
                            cl.account_email_login AS account_login,
                            cl.account_busyness,
                            cl.account_region_name,
                            cl.account_pay_type_name,
                            cl.account_team_id,
                            cl.account_team_name,
                            cl.account_deleted_date,
                            cl.account_is_deleted::bool,
                            cl.account_lead_id,
                            cl.account_lead_name,
                            cl.country,
                            cl.add_link,
                            cl.add_score,
                            cl.link_total,
                            cl.score_total,
                            cl.deleted_link,
                            cl.deleted_score,
                            cl.add_score_eur,
                            cl.score_eur_total,
                            cl.deleted_score_eur,
                            cl.bonus,
                            cl.bonus_currency,
                            cl.bonus_link,
                            cl.bonus_score,
                            cl.report_update_time
                     FROM aggregation.outreach_link_salary_closed cl
                     GROUP BY cl.id_period, cl.id_team, cl.pay_type, cl.id_account, cl.account_name,
                              cl.account_email_login,
                              cl.account_busyness, cl.account_region_name, cl.account_pay_type_name, cl.account_team_id,
                              cl.account_team_name, cl.account_deleted_date, cl.account_is_deleted, cl.account_lead_id,
                              cl.account_lead_name, cl.country,
                              cl.add_link, cl.add_score, cl.link_total, cl.score_total, cl.deleted_link,
                              cl.deleted_score,
                              cl.add_score_eur, cl.score_eur_total, cl.deleted_score_eur, cl.bonus, cl.bonus_currency,
                              cl.bonus_link,
                              cl.bonus_score, cl.report_update_time
                     UNION ALL
                     SELECT c_1.id_period,
                            NULL::integer                            AS id_team,
                            c_1.pay_type,
                            c_1.id_account                           AS account_id,
                            c_1.account_name::text                   AS account_name,
                            c_1.account_email_login::text            AS account_login,
                            c_1.account_busyness::text               AS account_busyness,
                            c_1.account_region_name::text            AS account_region_name,
                            c_1.account_pay_type_name::text          AS account_pay_type_name,
                            c_1.account_team_id::text                AS account_team_id,
                            c_1.account_team_name::text              AS account_team_name,
                            c_1.account_deleted_date::text           AS account_deleted_date,
                            c_1.account_is_deleted,
                            c_1.account_lead_id::text                AS account_lead_id,
                            c_1.account_lead_name::text              AS account_lead_name,
                            c_1.country,
                            c_1.add_link,
                            c_1.add_score,
                            c_1.link_total,
                            c_1.score_total,
                            c_1.deleted_link,
                            c_1.deleted_score,
                            c_1.add_score_eur,
                            c_1.score_eur_total,
                            c_1.deleted_score_eur,
                            c_1.bonus,
                            c_1.bonus_currency::character varying(3) AS bonus_currency,
                            c_1.bonus_link,
                            c_1.bonus_score,
                            c_1.report_update_time
                     FROM aggregation.outreach_link_current c_1
                     GROUP BY c_1.id_period, c_1.pay_type, c_1.id_account, c_1.account_name, c_1.account_email_login,
                              c_1.account_busyness, c_1.account_region_name, c_1.account_pay_type_name,
                              c_1.account_team_id,
                              c_1.account_team_name, c_1.account_deleted_date, c_1.account_is_deleted,
                              c_1.account_lead_id, c_1.account_lead_name, c_1.country,
                              c_1.add_link, c_1.add_score, c_1.link_total, c_1.score_total, c_1.deleted_link,
                              c_1.deleted_score,
                              c_1.add_score_eur, c_1.score_eur_total, c_1.deleted_score_eur, c_1.bonus,
                              c_1.bonus_currency,
                              c_1.bonus_link, c_1.bonus_score, c_1.report_update_time)
SELECT u.id_period,
       p.name              AS period_name,
       p.dt_start          AS period_dt_start,
       p.dt_end            AS period_dt_end,
       p.closed            AS period_closed,
       p.month_start,
       p.month             AS period_month,
       p.quarter           AS period_quarter,
       p.year              AS period_year,
       u.id_team,
       u.pay_type,
       u.account_id,
       u.account_name,
       u.account_login,
       u.account_busyness,
       u.account_region_name,
       cc.name_country_eng AS account_region_name_full,
       u.account_pay_type_name,
       u.account_team_id,
       u.account_team_name,
       u.account_deleted_date,
       u.account_is_deleted,
       u.account_lead_id,
       u.account_lead_name,
       u.country,
       c.name_country_eng  AS country_name,
       u.add_link,
       u.add_score,
       u.link_total,
       u.score_total,
       u.deleted_link,
       u.deleted_score,
       u.add_score_eur,
       u.score_eur_total,
       u.deleted_score_eur,
       u.bonus,
       u.bonus_currency,
       u.bonus_link,
       u.bonus_score,
       u.report_update_time
FROM union_final u
         LEFT JOIN aggregation.outreach_period p ON u.id_period = p.id
         LEFT JOIN dimension.countries c ON u.country = lower(c.alpha_2::text)
         LEFT JOIN dimension.countries cc ON u.account_region_name = lower(cc.alpha_2::text);

alter table aggregation.v_outreach_final_result
    owner to ono;

grant select on aggregation.v_outreach_final_result to readonly;

grant select on aggregation.v_outreach_final_result to ypr;

grant select on aggregation.v_outreach_final_result to writeonly_pyscripts;

grant select on aggregation.v_outreach_final_result to vnov;

grant select on aggregation.v_outreach_final_result to "pavlo.kvasnii";

grant select on aggregation.v_outreach_final_result to rku;
