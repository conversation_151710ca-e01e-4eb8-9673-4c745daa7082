create view aggregation.v_crm_client_account_contact
            (id, name, created_on, owner_name, owner_email, country, found_in, source_type, client_segment,
             specialization, target_action, problem_segments, is_target_cpa, benchmark_cpa, is_target_clicks,
             benchmark_clicks, is_target_conversions, benchmark_conversions, is_target_cr, benchmark_cr, is_buys_at_pja,
             via_pja, soska_user_link, soska_project_link, primary_contact_id,client_utm,traffic_resources,traffic_flow, number_1c, contact_id, contact_name,
             contact_created_on, contact_owner_name, contact_owner_email, job_title, department, role_name,
             business_phone, mobile_phone, email)
as
SELECT cca.id,
       cca.name,
       cca.created_on,
       cca.owner_name,
       cca.owner_email,
       cca.country,
       cca.found_in,
       cca.source_type,
       cca.client_segment,
       cca.specialization,
       cca.target_action,
       cca.problem_segments,
       cca.is_target_cpa,
       cca.benchmark_cpa,
       cca.is_target_clicks,
       cca.benchmark_clicks,
       cca.is_target_conversions,
       cca.benchmark_conversions,
       cca.is_target_cr,
       cca.benchmark_cr,
       cca.is_buys_at_pja,
       cca.via_pja,
       cca.soska_user_link,
       cca.soska_project_link,
       cca.primary_contact_id,
       cca.client_utm,
       cca.traffic_resources,
       cca.traffic_flow,
       btrim(regexp_replace(cca.number_1c::text, '\s+'::text, ' '::text, 'g'::text)) AS number_1c,
       ccc.id                                                                        AS contact_id,
       ccc.name                                                                      AS contact_name,
       ccc.created_on                                                                AS contact_created_on,
       ccc.owner_name                                                                AS contact_owner_name,
       ccc.owner_email                                                               AS contact_owner_email,
       ccc.job_title,
       ccc.department,
       ccc.role_name,
       ccc.business_phone,
       ccc.mobile_phone,
       ccc.email
FROM aggregation.crm_client_account cca
         LEFT JOIN aggregation.crm_client_contact ccc ON ccc.id::text = cca.primary_contact_id::text
where cca.number_1c != '';

alter table aggregation.v_crm_client_account_contact
    owner to ono;

grant select on aggregation.v_crm_client_account_contact to readonly;

grant select on aggregation.v_crm_client_account_contact to ypr;

grant select on aggregation.v_crm_client_account_contact to user_agg_team;

