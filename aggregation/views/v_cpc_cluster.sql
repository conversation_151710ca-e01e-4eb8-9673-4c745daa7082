create view aggregation.v_cpc_cluster
            (country, record_date, id_project, id_campaign, campaign_name, site, sale_manager, click_price_usd,
             current_avg_cpc_usd, click_count, cluster_click_count, revenue_usd, paid_click_count, paid_job_count,
             project_revenue_usd, country_revenue_usd, country_revenue_rank, low_min_cpc_usd, low_max_cpc_usd,
             medium_min_cpc_usd, medium_max_cpc_usd, high_min_cpc_usd, high_max_cpc_usd, cluster, current_cluster_min,
             current_cluster_max, value_to_eur)
as 
with
    date_ranges as (
        select
            dt_year,
            dt_month,
            min(dt) as min_dt,
            max(dt) as max_dt
        from
            dimension.info_calendar
        where
              dt >= '2022-11-01'::date
          and date_diff <= (fn_get_date_diff(date_trunc('month', current_date)::date) - 1)
        group by dt_year, dt_month
    ),
    revenue as
        (Select auction_click_statistic_analytics.date::date,
                auction_click_statistic_analytics.country_id,
                auction_click_statistic_analytics.id_project,
                auction_click_statistic_analytics.id_campaign,
                auction_click_statistic_analytics.total_value,
                case when auction_click_statistic_analytics.date::text < '2023-04-01'::text then
                auction_click_statistic_analytics.click_count -
                coalesce(auction_click_statistic_analytics.test_count, 0) -
                coalesce(auction_click_statistic_analytics.duplicated_count, 0)
                    else auction_click_statistic_analytics.click_count end
                    as click_count,
                auction_click_statistic_analytics.organic_count
         from aggregation.auction_click_statistic_analytics
         where date::date between '2022-11-01'::date and '2024-09-20'::date
           and auction_click_statistic_analytics.click_price > 0::numeric
           and case when auction_click_statistic_analytics.date::text < '2023-04-01'::text then
                auction_click_statistic_analytics.click_count -
                coalesce(auction_click_statistic_analytics.test_count, 0) -
                coalesce(auction_click_statistic_analytics.duplicated_count, 0)
                    else auction_click_statistic_analytics.click_count end  > 0
         union all
         Select public.fn_get_timestamp_from_date_diff(click_data_agg.action_datediff::int) as date,
                click_data_agg.country_id,
                click_data_agg.id_project,
                click_data_agg.id_campaign,
                click_data_agg.revenue_usd,
                click_data_agg.certified_client_click_cnt,
                case
                    when coalesce(u_traffic_source.is_paid, false) = FALSE then click_data_agg.certified_client_click_cnt
                    else 0 end                                                              as organic_count
         from aggregation.click_data_agg
                  left join dimension.u_traffic_source
                            on click_data_agg.country_id = u_traffic_source.country
                                and click_data_agg.id_current_traf_source = u_traffic_source.id
         where action_datediff >= 45554
           and click_data_agg.revenue_usd > 0
           and click_data_agg.certified_client_click_cnt > 0
        ),
    country_date_ranges as (
        select
            scr.date,
            scr.country_id,
            scr.low_min_cpc_usd,
            scr.low_max_cpc_usd,
            scr.medium_min_cpc_usd,
            scr.medium_max_cpc_usd,
            scr.high_min_cpc_usd,
            scr.high_max_cpc_usd,
            dr.min_dt,
            dr.max_dt,
            t.value_to_eur
        from
            aggregation.cpc_cluster_range scr
            join (select distinct on ((ich.date::date))
                      ich.value_to_eur,
                      ich.date::date as date
                  from
                      dimension.info_currency_history ich
                      join dimension.info_currency ic
                           on ic.country = ich.country and ic.id = ich.id_currency
                  where
                      ic.name::text = 'USD'::text
                  order by (ich.date::date), ich.value_to_eur desc) t
                 on t.date = scr.date
            join date_ranges dr
                 on ((date_part('year'::text, dr.min_dt) - date_part('year'::text, scr.date)) * 12::double precision +
                     (date_part('month'::text, dr.min_dt) - date_part('month'::text, scr.date))) >=
                    '-1'::integer::double precision and
                    ((date_part('year'::text, dr.min_dt) - date_part('year'::text, scr.date)) * 12::double precision +
                     (date_part('month'::text, dr.min_dt) - date_part('month'::text, scr.date))) <= 4::double precision
    ) ,
  acs_raw as (
        select
            co.alpha_2                                                                        as country,
            acs.country_id                                                                    as id_country,
            cdr.max_dt                                                                        as date_month,
            acs.id_project,
            acs.id_campaign,
            cdr.low_min_cpc_usd,
            cdr.low_max_cpc_usd,
            cdr.medium_min_cpc_usd,
            cdr.medium_max_cpc_usd,
            cdr.high_min_cpc_usd,
            cdr.high_max_cpc_usd,
            cdr.value_to_eur,
            acs.total_value / coalesce(acs.click_count, 1)::numeric                           as cpc_usd,
            acs.click_count                                                                   as click_count,
            coalesce(acs.organic_count, 0)                                                    as organic_count,
            acs.total_value
        from
            country_date_ranges cdr
            join revenue acs
                 on acs.country_id = cdr.country_id and acs.date::date >= cdr.min_dt and acs.date::date <= cdr.max_dt
            join dimension.countries co
                 on co.id = acs.country_id
            join dimension.info_project ip_1
                 on acs.country_id = ip_1.country and acs.id_project = ip_1.id and not ip_1.hide_in_search
    ),
    acs as (
        select
            acs_raw.country,
            acs_raw.id_country,
            acs_raw.date_month,
            acs_raw.id_project,
            acs_raw.id_campaign,
            acs_raw.low_min_cpc_usd,
            acs_raw.low_max_cpc_usd,
            acs_raw.medium_min_cpc_usd,
            acs_raw.medium_max_cpc_usd,
            acs_raw.high_min_cpc_usd,
            acs_raw.high_max_cpc_usd,
            acs_raw.value_to_eur,
            case
                when low_min_cpc_usd <= 0.05 then ROUND(acs_raw.cpc_usd::numeric, 3)
                else round(acs_raw.cpc_usd::numeric, 2) end           as click_price_usd,
            sum(acs_raw.click_count)                         as click_count,
            sum(acs_raw.click_count - acs_raw.organic_count) as paid_click_count,
            sum(acs_raw.total_value + coalesce(- (acs_raw.total_value - acs_raw.click_count::numeric * acs_raw.cpc_usd),
                                               0::numeric))  as revenue_usd,
            case
                when low_min_cpc_usd <= 0.05 then 0.001
                else 0.01 end                                as least_price_unit
        from
            acs_raw
        group by
            acs_raw.country, acs_raw.id_country, acs_raw.date_month, acs_raw.id_project, acs_raw.id_campaign,
            acs_raw.low_min_cpc_usd, acs_raw.low_max_cpc_usd, acs_raw.medium_max_cpc_usd, acs_raw.high_max_cpc_usd,
            case when low_min_cpc_usd <= 0.05 then round(acs_raw.cpc_usd::numeric, 3) else round(acs_raw.cpc_usd::numeric, 2) end,
            acs_raw.medium_min_cpc_usd, acs_raw.high_min_cpc_usd, acs_raw.value_to_eur
    ),
    jobs_data as (
        select
            j.id_country,
            j.id_project,
            j.id_campaign,
            avg(j.paid_job_count) as paid_job_count
        from
            aggregation.jobs_stat_daily j
            join country_date_ranges cdr
                 on cdr.country_id = j.id_country and j.date::date >= cdr.min_dt and j.date::date <= cdr.max_dt
        where
            j.paid_job_count > 0
        group by j.id_country, j.id_project, j.id_campaign
    ),
    current_cpc as (
        select
            acsa.country_id,
            acsa.id_campaign,
            sum(revenue_usd)                        as current_revenue,
            sum(certified_client_click_cnt)         as current_click_count
        from
            aggregation.click_data_agg acsa
        where
              acsa.revenue_usd > 0::numeric
          and coalesce(acsa.certified_client_click_cnt, 0)::numeric > 0::numeric
          and acsa.action_datediff >= public.fn_get_date_diff(current_date) -7
          and acsa.action_datediff <= public.fn_get_date_diff(current_date) -1
          and (acsa.country_id in (select distinct
                                       country_date_ranges.country_id
                                   from
                                       country_date_ranges))
        group by acsa.country_id, acsa.id_campaign
    ),
    clustered_data as (
        select
            acs.country,
            acs.id_country,
            acs.date_month                                                                   as record_date,
            acs.id_project,
            acs.id_campaign,
            acs.click_price_usd,
            cc.current_revenue / cc.current_click_count                                      as current_avg_cpc_usd,
            jd.paid_job_count,
            acs.click_count                                                                  as cluster_click_count,
            sum(acs.click_count)
            over (partition by acs.country, acs.date_month, acs.id_project, acs.id_campaign) as click_count,
            sum(acs.paid_click_count)
            over (partition by acs.country, acs.date_month, acs.id_project, acs.id_campaign) as paid_click_count,
            sum(acs.revenue_usd)
            over (partition by acs.country, acs.date_month, acs.id_project, acs.id_campaign) as revenue_usd,
            acs.low_min_cpc_usd,
            acs.low_max_cpc_usd,
            acs.medium_min_cpc_usd,
            acs.medium_max_cpc_usd,
            acs.high_min_cpc_usd,
            acs.high_max_cpc_usd,
            acs.value_to_eur,
            sum(acs.revenue_usd)
            over (partition by acs.country, acs.date_month, acs.id_project)                  as project_revenue_usd,
            sum(acs.revenue_usd) over (partition by acs.country, acs.date_month)             as country_revenue_usd,
            case
                when acs.click_price_usd < acs.low_min_cpc_usd then 'Extremely low'::text
                when acs.click_price_usd >= acs.low_min_cpc_usd and
                     acs.click_price_usd <= acs.low_max_cpc_usd then 'Low'::text
                when acs.click_price_usd >= acs.medium_min_cpc_usd and
                     acs.click_price_usd <= acs.medium_max_cpc_usd then 'Medium'::text
                else 'High'::text
                end                                                                          as cluster,
            case
                when acs.click_price_usd < acs.low_min_cpc_usd then least_price_unit
                when acs.click_price_usd >= acs.low_min_cpc_usd and
                     acs.click_price_usd <= acs.low_max_cpc_usd then acs.low_min_cpc_usd
                when acs.click_price_usd >= acs.medium_min_cpc_usd and
                     acs.click_price_usd <= acs.medium_max_cpc_usd then acs.low_max_cpc_usd + least_price_unit
                else coalesce(acs.medium_max_cpc_usd, acs.low_max_cpc_usd) + least_price_unit
                end                                                                          as current_cluster_min,
            case
                when acs.click_price_usd < acs.low_min_cpc_usd then acs.low_min_cpc_usd - least_price_unit
                when acs.click_price_usd >= acs.low_min_cpc_usd and
                     acs.click_price_usd <= acs.low_max_cpc_usd then acs.low_max_cpc_usd
                when acs.click_price_usd >= acs.medium_min_cpc_usd and
                     acs.click_price_usd <= acs.medium_max_cpc_usd then acs.medium_max_cpc_usd
                else acs.high_max_cpc_usd
                end                                                                          as current_cluster_max
        from
            acs
            left join jobs_data jd
                      on jd.id_country = acs.id_country and jd.id_project = acs.id_project and
                         jd.id_campaign = acs.id_campaign
            left join current_cpc cc
                      on cc.country_id = acs.id_country and cc.id_campaign = acs.id_campaign and
                         cc.current_click_count > 0::numeric
    ),
    clustered_data_agg as (
        select
            cd.country,
            cd.id_country,
            cd.record_date,
            cd.id_project,
            cd.id_campaign,
            cd.revenue_usd / cd.click_count as click_price_usd,
            cd.paid_job_count,
            cd.current_avg_cpc_usd,
            sum(cd.cluster_click_count)     as cluster_click_count,
            cd.click_count,
            cd.paid_click_count,
            cd.revenue_usd,
            cd.low_min_cpc_usd,
            cd.low_max_cpc_usd,
            cd.medium_min_cpc_usd,
            cd.medium_max_cpc_usd,
            cd.high_min_cpc_usd,
            cd.high_max_cpc_usd,
            cd.project_revenue_usd,
            cd.country_revenue_usd,
            cd.cluster,
            cd.current_cluster_min,
            cd.current_cluster_max,
            cd.value_to_eur
        from
            clustered_data cd
        where
            cd.click_count > 0::numeric
        group by
            cd.country, cd.id_country, cd.record_date, cd.id_project,
            cd.id_campaign, (cd.revenue_usd / cd.click_count),
            cd.paid_job_count, cd.click_count, cd.paid_click_count,
            cd.revenue_usd, cd.low_min_cpc_usd, cd.low_max_cpc_usd,
            cd.medium_max_cpc_usd, cd.high_max_cpc_usd, cd.project_revenue_usd,
            cd.country_revenue_usd, cd.cluster, cd.current_cluster_min,
            cd.current_cluster_max, cd.medium_min_cpc_usd, cd.high_min_cpc_usd,
            cd.value_to_eur, cd.current_avg_cpc_usd
    ),
    result as (
        select
            cda.country,
            cda.record_date,
            cda.id_project,
            cda.id_campaign,
            ac.name                                                                                                                 as campaign_name,
            ip.name                                                                                                                 as site,
            sm.sale_manager,
            cda.current_avg_cpc_usd,
            cda.click_price_usd,
            cda.click_count,
            cda.revenue_usd,
            cda.paid_click_count,
            cda.paid_job_count,
            cda.project_revenue_usd,
            cda.country_revenue_usd,
            dense_rank()
            over (partition by cda.record_date order by cda.country_revenue_usd desc)                                               as country_revenue_rank,
            cda.cluster_click_count,
            row_number()
            over (partition by cda.record_date, cda.country, cda.id_project, cda.id_campaign order by cda.cluster_click_count desc) as row_num,
            cda.low_min_cpc_usd,
            cda.low_max_cpc_usd,
            cda.medium_min_cpc_usd,
            cda.medium_max_cpc_usd,
            cda.high_min_cpc_usd,
            cda.high_max_cpc_usd,
            cda.cluster,
            cda.current_cluster_min,
            cda.current_cluster_max,
            cda.value_to_eur
        from
            clustered_data_agg cda
            left join dimension.info_project ip
                      on cda.id_country = ip.country and cda.id_project = ip.id
            left join aggregation.v_sale_manager sm
                      on sm.country = cda.id_country and sm.id_project = cda.id_project
            left join imp.auction_campaign ac
                      on ac.country_id = cda.id_country and ac.id = cda.id_campaign
    )
select
    r.country,
    r.record_date,
    r.id_project,
    r.id_campaign,
    r.campaign_name,
    r.site,
    r.sale_manager,
    case
        when r.low_min_cpc_usd <= 0.05 then round(r.click_price_usd::numeric, 3)
        else round(r.click_price_usd::numeric, 2) end                                                                          as click_price_usd,
    case
        when r.low_min_cpc_usd <= 0.05 then round(r.current_avg_cpc_usd::numeric, 3)
        else round(r.current_avg_cpc_usd::numeric, 2) end                                                                      as current_avg_cpc_usd,
    r.click_count,
    r.cluster_click_count,
    r.revenue_usd,
    r.paid_click_count,
    r.paid_job_count,
    r.project_revenue_usd,
    r.country_revenue_usd,
    r.country_revenue_rank,
    r.low_min_cpc_usd,
    r.low_max_cpc_usd,
    r.medium_min_cpc_usd,
    r.medium_max_cpc_usd,
    r.high_min_cpc_usd,
    r.high_max_cpc_usd,
    r.cluster,
    r.current_cluster_min,
    r.current_cluster_max,
    r.value_to_eur
from
    result r
where
    r.row_num = 1;
