create or replace view aggregation.v_manual_client_conversions
            (country, site, id_project, id_campaign, campaign, date, total_value, click_count, conversions,
             traffic_source) as
WITH conversion AS (SELECT conversions.date,
                           conversions.country,
                           conversions.id_project                AS project_id,
                           info_project.name                     AS project_name,
                           conversions_campaign.id_campaign,
                           max(conversions.conversions)          AS total_conversions,
                           sum(conversions_campaign.conversions) AS conversions
                    FROM imp_statistic.conversions
                             LEFT JOIN imp_statistic.conversions_campaign
                                       ON conversions.id = conversions_campaign.id_conversion
                             LEFT JOIN dimension.countries countries_1
                                       ON conversions.country::text = countries_1.alpha_2::text
                             LEFT JOIN dimension.info_project ON countries_1.id = info_project.country AND
                                                                 conversions.id_project = info_project.id
                    WHERE conversions.id_date_period = 1
                      AND conversions.date >= '2022-01-01'::date
                    GROUP BY conversions.date, conversions.country, conversions.id_project, info_project.name,
                             conversions_campaign.id_campaign),
     conversion_indeed AS (SELECT conversions_indeed.date,
                                  conversions_indeed.country,
                                  conversions_indeed.id_project              AS project_id,
                                  conversions_indeed.id_adv,
                                  conversions_indeed.name_adv,
                                  sum(COALESCE(conversions_indeed.apply, 0)) AS conversions
                           FROM imp_statistic.conversions_indeed
                                    LEFT JOIN dimension.countries countries_1
                                              ON conversions_indeed.country::text = countries_1.alpha_2::text
                           GROUP BY conversions_indeed.date, conversions_indeed.country, conversions_indeed.id_project,
                                    conversions_indeed.id_adv, conversions_indeed.name_adv),
     conversion_source AS (SELECT conversions.date,
                                  conversions.country,
                                  conversions.id_project                  AS project_id,
                                  info_project.name                       AS project_name,
                                  conversions_source.id_traffic_source,
                                  max(conversions.conversions)            AS total_conversions,
                                  sum(conversions_source.cnt_conversions) AS conversions
                           FROM imp_statistic.conversions
                                    JOIN imp_statistic.conversions_source
                                         ON conversions.id = conversions_source.id_conversion
                                    LEFT JOIN dimension.countries countries_1
                                              ON conversions.country::text = countries_1.alpha_2::text
                                    LEFT JOIN dimension.info_project ON countries_1.id = info_project.country AND
                                                                        conversions.id_project = info_project.id
                                    LEFT JOIN dimension.u_traffic_source
                                              ON countries_1.id = u_traffic_source.country AND
                                                 conversions_source.id_traffic_source = u_traffic_source.id
                           WHERE conversions.id_date_period = 1
                             AND conversions.date >= '2022-01-01'::date
                           GROUP BY conversions.date, conversions.country, conversions.id_project, info_project.name,
                                    conversions_source.id_traffic_source),
     revenue AS (SELECT acs_1.country,
                        acs_1.country_id,
                        acs_1.date,
                        acs_1.id_project,
                        acs_1.site,
                        acs_1.id_campaign,
                        acs_1.campaign,
                        sum(acs_1.click_count - acs_1.from_jdp_count - acs_1.test_count) AS click_count,
                        sum(acs_1.total_value + COALESCE(- (acs_1.total_value -
                                                            (acs_1.click_count - acs_1.test_count - acs_1.to_jdp_count)::numeric *
                                                            (acs_1.total_value /
                                                             NULLIF(acs_1.click_count - acs_1.to_jdp_count, 0)::numeric)),
                                                         0::numeric))                    AS total_value
                 FROM imp_statistic.auction_click_statistic acs_1
                          JOIN (SELECT DISTINCT conversions_1.country,
                                                conversions_1.id_project
                                FROM imp_statistic.conversions conversions_1
                                WHERE conversions_1.date >= '2022-01-01'::date
                                UNION
                                SELECT DISTINCT conversions_indeed.country,
                                                conversions_indeed.id_project
                                FROM imp_statistic.conversions_indeed) conversions
                               ON acs_1.country::text = conversions.country::text AND
                                  acs_1.id_project = conversions.id_project
                 WHERE acs_1.date >= '2022-01-01'::date
                 GROUP BY acs_1.country, acs_1.country_id, acs_1.date, acs_1.id_project, acs_1.site, acs_1.id_campaign,
                          acs_1.campaign),
     source_revenue AS (SELECT jdp_away_clicks_agg.country_id,
                               fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff) AS date,
                               jdp_away_clicks_agg.project_id                                       AS id_project,
                               jdp_away_clicks_agg.project_name                                     AS site,
                               jdp_away_clicks_agg.id_traf_source,
                               sum(jdp_away_clicks_agg.jdp_away_count)                              AS click_count,
                               sum(jdp_away_clicks_agg.revenue_usd)                                 AS total_value
                        FROM aggregation.jdp_away_clicks_agg
                        WHERE jdp_away_clicks_agg.away_type::text <> 'JDP only'::text
                          AND jdp_away_clicks_agg.action_datediff >= 44680
                        GROUP BY jdp_away_clicks_agg.country_id,
                                 (fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff)),
                                 jdp_away_clicks_agg.project_id, jdp_away_clicks_agg.project_name,
                                 jdp_away_clicks_agg.id_traf_source),
     final_union AS (SELECT countries.alpha_2 AS country,
                            revenue.site,
                            revenue.id_project,
                            revenue.id_campaign,
                            revenue.campaign,
                            revenue.date,
                            revenue.total_value,
                            revenue.click_count,
                            conversion.conversions,
                            NULL::text        AS traffic_source
                     FROM revenue
                              LEFT JOIN conversion ON revenue.country::text = conversion.country::text AND
                                                      revenue.id_project = conversion.project_id AND
                                                      revenue.id_campaign = conversion.id_campaign AND
                                                      revenue.date = conversion.date
                              JOIN dimension.countries ON revenue.country_id = countries.id
                     WHERE (concat(revenue.country, revenue.id_project) IN
                            (SELECT DISTINCT concat(conversion_1.country, conversion_1.project_id) AS concat
                             FROM conversion conversion_1
                             WHERE conversion_1.id_campaign IS NOT NULL))
                       AND NOT (concat(revenue.country, revenue.id_project) IN
                                (SELECT DISTINCT concat(conversion_source.country, conversion_source.project_id) AS concat
                                 FROM conversion_source))
                     UNION ALL
                     SELECT countries.alpha_2                       AS country,
                            revenue.site,
                            revenue.id_project,
                            NULL::integer                           AS id_campaign,
                            NULL::character varying                 AS campaign,
                            revenue.date,
                            sum(revenue.total_value)                AS total_value,
                            sum(revenue.click_count)                AS click_count,
                            max(conversion_total.total_conversions) AS conversions,
                            NULL::text                              AS traffic_source
                     FROM revenue
                              LEFT JOIN conversion conversion_total
                                        ON revenue.country::text = conversion_total.country::text AND
                                           revenue.id_project = conversion_total.project_id AND
                                           conversion_total.id_campaign IS NULL AND revenue.date = conversion_total.date
                              JOIN dimension.countries ON revenue.country_id = countries.id
                     WHERE (concat(revenue.country, revenue.id_project) IN
                            (SELECT DISTINCT concat(conversion.country, conversion.project_id) AS concat
                             FROM conversion
                             WHERE conversion.id_campaign IS NULL))
                       AND NOT (concat(revenue.country, revenue.id_project) IN
                                (SELECT DISTINCT concat(conversion_source.country, conversion_source.project_id) AS concat
                                 FROM conversion_source))
                     GROUP BY countries.alpha_2, revenue.site, revenue.id_project, revenue.date
                     UNION ALL
                     SELECT countries.alpha_2                 AS country,
                            revenue.site,
                            revenue.id_project,
                            NULL::integer                     AS id_campaign,
                            NULL::character varying           AS campaign,
                            revenue.date,
                            sum(revenue.total_value)          AS total_value,
                            sum(revenue.click_count)          AS click_count,
                            max(conversion_total.conversions) AS conversions,
                            u_traffic_source.name             AS traffic_source
                     FROM source_revenue revenue
                              JOIN dimension.countries ON revenue.country_id = countries.id
                              LEFT JOIN conversion_source conversion_total
                                        ON countries.alpha_2::text = conversion_total.country::text AND
                                           revenue.id_project = conversion_total.project_id AND
                                           revenue.date = conversion_total.date AND
                                           revenue.id_traf_source = conversion_total.id_traffic_source
                              LEFT JOIN dimension.u_traffic_source ON revenue.country_id = u_traffic_source.country AND
                                                                      revenue.id_traf_source = u_traffic_source.id
                     WHERE (concat(countries.alpha_2, revenue.id_project) IN
                            (SELECT DISTINCT concat(conversion_source.country, conversion_source.project_id) AS concat
                             FROM conversion_source))
                     GROUP BY countries.alpha_2, revenue.site, revenue.id_project, revenue.date, u_traffic_source.name
                     UNION ALL
                     SELECT COALESCE(countries.alpha_2, conversion_total.country)     AS country,
                            'indeediq.com'::character varying                         AS site,
                            COALESCE(revenue.id_project, conversion_total.project_id) AS id_project,
                            revenue.id_campaign,
                            revenue.campaign,
                            COALESCE(revenue.date, conversion_total.date)             AS date,
                            sum(revenue.total_value)                                  AS total_value,
                            sum(revenue.click_count)                                  AS click_count,
                            sum(conversion_total.conversions)                         AS conversions,
                            NULL::text                                                AS traffic_source
                     FROM revenue
                              JOIN dimension.countries ON revenue.country_id = countries.id
                              FULL JOIN conversion_indeed conversion_total
                                        ON countries.alpha_2::text = conversion_total.country::text AND
                                           revenue.id_project = conversion_total.project_id AND
                                           revenue.date = conversion_total.date AND (conversion_total.id_adv::text =
                                                                                     replace(revenue.campaign::text, 'Price per job '::text, ''::text) OR
                                                                                     conversion_total.name_adv =
                                                                                     replace(revenue.campaign::text, 'Price per job '::text, ''::text))
                     WHERE COALESCE(revenue.id_project, conversion_total.project_id) = 16342
                       AND COALESCE(revenue.date, conversion_total.date) >= '2022-08-15'::date
                     GROUP BY (COALESCE(countries.alpha_2, conversion_total.country)),
                              (COALESCE(revenue.id_project, conversion_total.project_id)), revenue.id_campaign,
                              revenue.campaign, (COALESCE(revenue.date, conversion_total.date))
                     UNION ALL
                     SELECT upper(apply_conversion_service.country::text)::character varying(2) AS country,
                            'goldenbees.aoj'::character varying                                 AS site,
                            campaign.id_project                                                 AS id_project,
                            apply_conversion_service.id_campaign::integer                       AS id_campaign,
                            campaign.name                                                       AS campaign,
                            apply_conversion_service.date_created::date                         AS date,
                            sum(apply_conversion_service.cost)                                  AS total_value,
                            NULL::integer                                                       AS click_count,
                            count(apply_conversion_service.id)                                  AS conversions,
                            NULL::text                                                          AS traffic_source
                     FROM imp_statistic.apply_conversion_service
                              LEFT JOIN dimension.countries ON apply_conversion_service.country = lower(countries.alpha_2)
                              LEFT JOIN imp.campaign ON countries.id = campaign.country AND apply_conversion_service.id_campaign = campaign.id
                     WHERE apply_conversion_service.country::text = 'fr'::text
                       AND apply_conversion_service.cost IS NOT NULL
                       AND campaign.id_project = 17004
                     GROUP BY (upper(apply_conversion_service.country::text)), campaign.id_project, apply_conversion_service.id_campaign,
                              campaign.name, (apply_conversion_service.date_created::date))
SELECT final_union.country,
       final_union.site,
       final_union.id_project,
       final_union.id_campaign,
       final_union.campaign,
       final_union.date,
       final_union.total_value,
       final_union.click_count,
       final_union.conversions,
       final_union.traffic_source
FROM final_union;

alter table aggregation.v_manual_client_conversions
    owner to ono;

grant select on aggregation.v_manual_client_conversions to readonly;

grant select on aggregation.v_manual_client_conversions to readonly_aggregation;

grant select on aggregation.v_manual_client_conversions to "pavlo.kvasnii";
