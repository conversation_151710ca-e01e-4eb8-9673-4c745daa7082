create view aggregation.v_serp_campaign_rivals_agg
            (country, date, target_campaign_id, target_campaign_name, rival_campaign_id, rival_campaign_name,
             target_project_id, target_project_name, rival_project_id, rival_project_name, click_price_usd,
             serp_click_value, total_search_cnt, search_cnt, impression_cnt, impression_on_screen_cnt, click_cnt,
             position_min, position_mean, position_max)
as
select
    c.alpha_2                                       as country,
    fn_get_timestamp_from_date_diff(scra.date_diff) as date,
    scra.target_campaign_id,
    coalesce(tac.name, 'free or unknown')           as target_campaign_name,
    scra.rival_campaign_id,
    coalesce(rac.name, 'free or unknown')           as rival_campaign_name,
    tac.id_project                                  as target_project_id,
    coalesce(ip.name, 'unknown')                    as target_project_name,
    rac.id_project                                  as rival_project_id,
    coalesce(ipr.name, 'unknown')                   as rival_project_name,
    scra.click_price_usd,
    scra.serp_click_value,
    scra.total_search_cnt,
    scra.search_cnt,
    scra.impression_cnt,
    scra.impression_on_screen_cnt,
    scra.click_cnt,
    scra.position_min,
    scra.position_mean,
    scra.position_max
from
    aggregation.serp_campaign_rivals_agg scra
    join dimension.countries c
         on c.id = scra.country_id
    left join imp.auction_campaign tac
              on tac.country_id = scra.country_id and tac.id = scra.target_campaign_id
    left join imp.auction_campaign rac
              on rac.country_id = scra.country_id and rac.id = scra.rival_campaign_id
    left join dimension.info_project ip
              on ip.country = tac.country_id and ip.id = tac.id_project
    left join dimension.info_project ipr
              on ipr.country = rac.country_id and ipr.id = rac.id_project
where
        scra.date_diff >= (fn_get_date_diff(current_date::timestamp without time zone) - 30);
