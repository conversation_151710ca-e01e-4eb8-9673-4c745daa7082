declare
@first_visit_datediff_begin int = ${@first_visit_datediff_begin},  -- an.fn_get_date_diff(public.ex_getdate()) - 15
@first_visit_datediff_end int =  ${@first_visit_datediff_end},     -- an.fn_get_date_diff(public.ex_getdate()) - 1
@last_available_datediff int = datediff(day,0,dbo.ex_getdate()-1); -- an.fn_get_date_diff(public.ex_getdate()) - 1


drop table if exists session_test_agg_temp;
drop table if exists session_test_cookie_label;
drop table if exists session_temp;
drop table if exists u_traffic_source_temp;
drop table if exists session_from_letter;
drop table if exists cookie_label_first_session;

create temp table session_test_agg_temp as
    select  id_session,
            session_test.date_diff,
            array_to_string(array_agg(id_test),' ') as id_tests,
            array_to_string(array_agg(concat((id_test),'-',session_test."group",'_',(iteration))), ' ') as groups
    from public.session_test
            where date_diff between date_diff - 31 and date_diff
                  and iteration >= 0
     group by id_session,
              session_test.date_diff;

create temp table session_test_cookie_label as
        select s.cookie_label,
               s.id as session_id,
               case when s.flags & 16 = 16 then 1 else 0 end as is_mobile,
               case
                   when s.ip_cc = lower(substring(current_database(), 5, 2)) or
                        s.ip_cc = 'gb' and lower(substring(current_database(), 5, 2)) = 'uk'
                       then 1
                   else 0
               end                                           as is_local,
               coalesce(s.session_create_page_type, 0)       as session_create_page_type_id,
               s.id_current_traf_source as traffic_source_id,
               s.first_visit_date_diff as first_visit_datediff,
               s.date_diff as session_datediff,
               s.start_date as start_datetime,
               min(s.start_date) over (partition by s.cookie_label) as min_start_datetime
        from  session s
        where s.flags & 1 = 0
        and s.first_visit_date_diff = s.date_diff and
            s.first_visit_date_diff between first_visit_datediff_begin and first_visit_datediff_end;

create temp table cookie_label_first_session as
        select distinct
               st.groups,
               stcl.cookie_label,
               1 as is_new,
               stcl.is_mobile,
               stcl.is_local,
               stcl.session_create_page_type_id,
               stcl.traffic_source_id,
               stcl.first_visit_datediff,
               stcl.session_datediff as first_visit_test_datediff
        from session_test_cookie_label stcl
        left join session_test_agg_temp st
               on st.date_diff = stcl.session_datediff and
                  stcl.session_id = st.id_session
        where stcl.start_datetime = stcl.min_start_datetime;

CREATE TEMP TABLE session_temp AS
    SELECT s.date_diff, id, id_current_traf_source, flags
    FROM session s
    WHERE s.date_diff between first_visit_datediff_begin and first_visit_datediff_end and
          s.date_diff <= last_available_datediff;

CREATE INDEX session_temp_INDEX on session_temp(date_diff,id);
CREATE INDEX session_temp_INDEX_TRAF on session_temp(id_current_traf_source);

CREATE TEMP TABLE u_traffic_source_temp AS
    SELECT *
    FROM link_dbo.u_traffic_source;

CREATE TEMP TABLE session_from_letter as
        select s.date_diff as session_datediff,
               s.id        as session_id
    from session_temp s
             left join u_traffic_source_temp uts
                       on s.id_current_traf_source = uts.id
             left join session_jdp sj on s.date_diff = sj.date_diff and s.id = sj.id_session and
                                                           sj.letter_type is not null /*тільки листи*/
             left join session_away_message sam
                       on s.date_diff = sam.date_diff and s.id = sam.id_session
             left join session_alertview sa on sa.date_diff = s.date_diff and sa.id_session = s.id
    where s.date_diff between first_visit_datediff_begin and first_visit_datediff_end and
          s.date_diff <= last_available_datediff and
         (sa.id_session is not null or
         sam.id_session is not null or
         sj.id_session is not null)
      and s.flags & 1 = 0
    group by s.date_diff,
             s.id;

return query
select country_id,
       CAST(coalesce(clfs.groups, '0-0') AS varchar(500)) as test_group_list,
       CAST(clfs.is_new AS INTEGER) as is_new_user,
       CAST(clfs.is_mobile AS INTEGER) as is_mobile,
       CAST(clfs.is_local AS INTEGER) as is_local,
       CAST(clfs.session_create_page_type_id as INTEGER),
       CAST(clfs.first_visit_datediff as INTEGER) as first_session_datediff,
       CAST(clfs.first_visit_test_datediff as INTEGER) as first_test_session_datediff,
       CAST(count(distinct clfs.cookie_label) as INTEGER) as user_cnt,
       CAST(count(distinct case when s.date_diff - clfs.first_visit_test_datediff between 1 and 3 then s.cookie_label end) AS INTEGER) as user_retained_day_1_3_cnt,
       CAST(count(distinct case when s.date_diff - clfs.first_visit_test_datediff between 1 and 7 then s.cookie_label end) AS INTEGER) as user_retained_day_1_7_cnt,
       CAST(count(distinct case when s.date_diff - clfs.first_visit_test_datediff between 1 and 14 then s.cookie_label end) AS INTEGER) as user_retained_day_1_14_cnt,

       CAST(count(distinct case when session_from_letter.session_datediff is not null and s.date_diff - clfs.first_visit_test_datediff between 1 and 3 then s.cookie_label end) AS INTEGER) as user_managed_retained_day_1_3_cnt,
       CAST(count(distinct case when session_from_letter.session_datediff is not null and s.date_diff - clfs.first_visit_test_datediff between 1 and 7 then s.cookie_label end) AS INTEGER) as user_managed_retained_day_1_7_cnt,
       CAST(count(distinct case when session_from_letter.session_datediff is not null and s.date_diff - clfs.first_visit_test_datediff between 1 and 14 then s.cookie_label end) AS INTEGER) as user_managed_retained_day_1_14_cnt,

       CAST(count(distinct case when session_from_letter.session_datediff is null and not (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 3 then s.cookie_label end) AS INTEGER) as user_unmanaged_retained_day_1_3_cnt,
       CAST(count(distinct case when session_from_letter.session_datediff is null and not (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 7 then s.cookie_label end) AS INTEGER) as user_unmanaged_retained_day_1_7_cnt,
       CAST(count(distinct case when session_from_letter.session_datediff is null and not (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 14 then s.cookie_label end) AS INTEGER) as user_unmanaged_retained_day_1_14_cnt,

       CAST(count(distinct case when (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 3 then s.cookie_label end) AS INTEGER) as user_artificial_retained_day_1_3_cnt,
       CAST(count(distinct case when (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 7 then s.cookie_label end) AS INTEGER) as user_artificial_retained_day_1_7_cnt,
       CAST(count(distinct case when (uts.channel in ('Affiliate')) and s.date_diff - clfs.first_visit_test_datediff between 1 and 14 then s.cookie_label end) as INTEGER) as user_artificial_retained_day_1_14_cnt
from cookie_label_first_session clfs
left join session s
     on s.flags & 1 = 0 and
        s.cookie_label = clfs.cookie_label and
        s.date_diff between clfs.first_visit_test_datediff+1 and clfs.first_visit_test_datediff+14 and
        s.date_diff <= last_available_datediff
left join session_from_letter
       on session_from_letter.session_datediff = s.date_diff and
          session_from_letter.session_id = s.id
left join link_dbo.u_traffic_source uts
       on s.id_current_traf_source = uts.id
group by clfs.groups,
         clfs.is_new,
         clfs.is_mobile,
         clfs.is_local,
         clfs.session_create_page_type_id,
         clfs.first_visit_datediff,
         clfs.first_visit_test_datediff;