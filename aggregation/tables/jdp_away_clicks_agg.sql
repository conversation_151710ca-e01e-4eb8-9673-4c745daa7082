SET NOCOUNT ON;


declare @date_diff int = :to_sqlcode_date_or_datediff_start,
        @dt_end int = :to_sqlcode_date_or_datediff_start;


with Final as
         (select s2.date_diff,
                 s2.traffic_source,
                 s2.id_current_traf_source,
                 s2.placement,
                 s2.away_type,
                 id_project,
                 project_name,
                 campaign_name,
                 id_campaign,
                 is_paid,
                 is_mobile,
                 ip_cc,
                 is_returned,
                 session_create_page_type,
                 is_paid_overflow,
                 sum(iif(is_duplicated = 0, s2.click_price_usd, 0))       revenue_usd,
                 count(distinct iif(is_duplicated = 0, s2.id_away, null)) jdp_away_count,
                 sum(iif(is_duplicated = 1, s2.click_price_usd, 0))       duplicated_revenue_usd,
                 count(distinct iif(is_duplicated = 1, s2.id_away, null)) duplicated_count,
                 id_traf_source,
                 is_local,
                 id_account_chatbot,
                 id_job_category,
                 user_id,
                 count(distinct iif(is_duplicated = 0, id_session_away_conversion, null)) as conversions
          from (select s1.date_diff,
                       s1.traffic_source,
                       s1.id_current_traf_source,
                       s1.click_price_usd,
                       id_project,
                       project_name,
                       campaign_name,
                       id_campaign,
                       is_paid,
                       is_mobile,
                       s1.id_away,
                       away_type,
                       case
                        when id_project in (17055,17045)
                          then 'price per post'
                        when add_placement = 1
                          then 'salary page'
                        when add_placement = 2
                          then 'category page'
                        when add_placement = 3
                          then 'company page'
                        when add_placement = 4
                          then 'skill page'
                        when add_placement = 5
                          then 'job description page'
                        when is_mobile = 2
                          then 'mobile app'
                        when info_project.hide_in_search = 1
                          then 'ad exchange'
                        when coalesce(s1.letter_type, email_sent.letter_type) is not null
                          then concat('letter type ',coalesce(s1.letter_type, email_sent.letter_type))
                        when s1.id_recommend is not null
                          then 'recommendations'
                        when s1.id_alertview is not null
                          then 'other letter types'
                        when s1.id_search is not null
                          then 'search'
                        when s1.id_external is not null
                          then 'external'
                        else 'other'
                      end placement,
                       ip_cc,
                       is_returned,
                       session_create_page_type,
                       is_paid_overflow,
                       id_traf_source,
                       is_local,
                       id_account_chatbot,
                       is_duplicated,
                       id_job_category,
                       user_id,
                       coalesce(conversion_away_connection.id_session_away, SAA.id) as id_session_away_conversion
                from (
                         --  Aways
                         select sa.date_diff,
                                null                                 as traffic_source,
                                s.id_current_traf_source,
                                sa.id                                      id_away,
                                case
                                    when sa.id_jdp is not null
                                        then 'Away from JDP'
                                    when sa.id_click is not null
                                        then 'Away from SERP'
                                    when isnull(sa.letter_type, sj.letter_type) is not null
                                        then 'Away from LT8'
                                    else 'Other'
                                    end                                 as away_type,
                                sa.id_project,
                                     case when sa.flags & 2048 = 2048  or coalesce(ss.search_source,ssj.search_source) in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
                                     when sa.flags & 4096 = 4096  or coalesce(ss.search_source,ssj.search_source) in (145) then 2--  category page
                                     when coalesce(ss.search_source,ssj.search_source) in (146, 147, 148, 149, 150) then 3-- company page
                                     when coalesce(ss.search_source,ssj.search_source) in (151, 152, 153, 154) then 4-- skill page
                                     when coalesce(ss.search_source,ssj.search_source) in (155, 156, 157, 158) then 5-- JobDescription page
                                     end as add_placement,
                                ip.name                                 as project_name,
                                ac.name                                 as campaign_name,
                                ac.id                                   as id_campaign,
                                0                                       as click_price_usd,
                                isnull(sa.letter_type, sj.letter_type)     letter_type,
                                isnull(sc.id_recommend, scj.id_recommend)  id_recommend,
                                isnull(sc.id_alertview, scj.id_alertview)  id_alertview,
                                isnull(sc.id_search, scj.id_search)        id_search,
                                ext.id                                     id_external,
                                case
                                    when sa.click_price <> 0
                                        then 'paid'
                                    when (sc.flags & 128 = 128 or sj.flags & 256 = 256)
                                        then 'premium'
                                    else 'free'
                                    end                                 as is_paid,
                                case
                                    when s.flags & 16 = 16 then 1
                                    when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                                    else 0 end
                                                                        as is_mobile,
                                s.ip_cc,
                                sign(s.flags & 2)                       as is_returned,
                                s.session_create_page_type,
                                case
                                    when uts.is_paid = 1 and
                                         (select top 1 flags
                                          from auction.campaign_log
                                          where date <= sa.date and id_campaign = ac.id
                                          order by date desc) & 32 = 32
                                        then 1
                                    else 0 end                          as is_paid_overflow,
                                case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                          as is_local,
                                s.id_traf_source,
                                null                                    as id_account_chatbot,
                                iif(sa.flags & 512 != 0, 1, 0)          as is_duplicated,
                                coalesce(jh.id_category, j.id_category) as id_job_category,
                                au.id                                   as user_id
                         from dbo.session_away sa (nolock)
                                  inner join dbo.[session] s (nolock)
                                             on sa.date_diff = s.date_diff
                                                 and sa.id_session = s.id
                                  left join dbo.info_currency ic (nolock)
                                            on ic.id = sa.id_currency
                                  left join dbo.u_traffic_source uts (nolock)
                                            on uts.id = s.id_traf_source
                                  left join auction.campaign ac (nolock)
                                            on ac.id = sa.id_campaign
                                  left join auction.site ast (nolock)
                                            on ac.id_site = ast.id
                                  left join auction.[user] au (nolock)
                                            on au.id = ast.id_user
                                  left join dbo.info_project ip with (nolock)
                                            on sa.id_project = ip.id
                             -- serp -> away
                                  left join dbo.session_click sc (nolock)
                                            on sc.date_diff = sa.date_diff
                                                and sc.id = sa.id_click
                             -- serp -> jdp -> away
                                  left join dbo.session_jdp sj (nolock)
                                            on sj.date_diff = sa.date_diff
                                                and sj.id = sa.id_jdp
                                  left join dbo.session_click scj (nolock)
                                            on scj.date_diff = sj.date_diff
                                                and scj.id = sj.id_click
                                  left join dbo.session_external ext (nolock)
                                            on ext.date_diff = sa.date_diff
                                                and ext.id_away = sa.id
                                  left join dbo.job j (nolock)
                                            on sa.id_job = j.id
                                  left join dbo.job_history jh (nolock)
                                            on sa.uid_job = jh.uid
                                  left join dbo.session_search ss (nolock) on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search
                                  left join dbo.session_search ssj (nolock) on ssj.date_diff = scj.date_diff
                                                 and ssj.id = scj.id_search

                         where sa.date_diff between @date_diff and @dt_end
                           and isnull(s.flags, 0) & 1 = 0 /*session bot*/
                           and isnull(sa.flags, 0) & 2 = 0 /*test campaign click*/
                           and coalesce(lower(ip.name), '') not like 'j-vers.%'

                         union all

                         -- Revenue
                         select sa.date_diff,
                                null                                as traffic_source,
                                s.id_current_traf_source,
                                null                                       id_away,
                                case
                                    when sa.id_jdp is not null
                                        then 'Away from JDP'
                                    when sa.id_click is not null
                                        then 'Away from SERP'
                                    when isnull(sa.letter_type, sj.letter_type) is not null
                                        then 'Away from LT8'
                                    else 'Other'
                                    end                                 as away_type,
                                sa.id_project,
                                case when sa.flags & 2048 = 2048  or coalesce(ss.search_source,ssj.search_source) in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
                                     when sa.flags & 4096 = 4096  or coalesce(ss.search_source,ssj.search_source) in (145) then 2--  category page
                                     when coalesce(ss.search_source,ssj.search_source) in (146, 147, 148, 149, 150) then 3-- company page
                                     when coalesce(ss.search_source,ssj.search_source) in (151, 152, 153, 154) then 4-- skill page
                                     when coalesce(ss.search_source,ssj.search_source) in (155, 156, 157, 158) then 5-- JobDescription page
                                end as add_placement,
                                ip.name                                 as project_name,
                                ac.name                                 as campaign_name,
                                ac.id                                   as id_campaign,
                                sa.click_price * ic.value_to_usd           click_price_usd,
                                isnull(sa.letter_type, sj.letter_type)     letter_type,
                                isnull(sc.id_recommend, scj.id_recommend)  id_recommend,
                                isnull(sc.id_alertview, scj.id_alertview)  id_alertview,
                                isnull(sc.id_search, scj.id_search)        id_search,
                                ext.id                                     id_external,
                                case
                                    when sa.click_price <> 0
                                        then 'paid'
                                    when (sc.flags & 128 = 128 or sj.flags & 256 = 256)
                                        then 'premium'
                                    else 'free'
                                    end                                 as is_paid,
                                case
                                    when s.flags & 16 = 16 then 1
                                    when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                                    else 0 end
                                                                        as is_mobile,
                                s.ip_cc,
                                sign(s.flags & 2)                       as is_returned,
                                s.session_create_page_type,
                                case
                                    when uts.is_paid = 1 and
                                         (select top 1 flags
                                          from auction.campaign_log
                                          where date <= sa.date and id_campaign = ac.id
                                          order by date desc) & 32 = 32
                                        then 1
                                    else 0 end                          as is_paid_overflow,
                                case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                          as is_local,
                                s.id_traf_source,
                                null                                    as id_account_chatbot,
                                iif(sa.flags & 512 != 0, 1, 0)          as is_duplicated,
                                coalesce(jh.id_category, j.id_category) as id_job_category,
                                au.id                                   as user_id
                         from dbo.session_away sa (nolock)
                                  inner join dbo.[session] s (nolock)
                                             on sa.date_diff = s.date_diff
                                                 and sa.id_session = s.id
                                  left join dbo.info_currency ic (nolock)
                                            on ic.id = sa.id_currency
                                  left join dbo.u_traffic_source uts (nolock)
                                            on uts.id = s.id_traf_source
                                  left join auction.campaign ac (nolock)
                                            on ac.id = sa.id_campaign
                                  left join auction.site ast (nolock)
                                            on ac.id_site = ast.id
                                  left join auction.[user] au (nolock)
                                            on au.id = ast.id_user
                                  left join dbo.info_project ip with (nolock)
                                            on sa.id_project = ip.id
                             -- serp -> away
                                  left join dbo.session_click sc (nolock)
                                            on sc.date_diff = sa.date_diff
                                                and sc.id = sa.id_click
                             -- serp -> jdp -> away
                                  left join dbo.session_jdp sj (nolock)
                                            on sj.date_diff = sa.date_diff
                                                and sj.id = sa.id_jdp
                                  left join dbo.session_click scj (nolock)
                                            on scj.date_diff = sj.date_diff
                                                and scj.id = sj.id_click
                                  left join dbo.session_external ext (nolock)
                                            on ext.date_diff = sa.date_diff
                                                and ext.id_away = sa.id
                                  left join dbo.job j (nolock)
                                            on sa.id_job = j.id
                                  left join dbo.job_history jh (nolock)
                                            on sa.uid_job = jh.uid
                                  left join dbo.session_search ss (nolock) on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search
                                  left join dbo.session_search ssj (nolock) on ssj.date_diff = scj.date_diff
                                                 and ssj.id = scj.id_search
                         where sa.date_diff between @date_diff and @dt_end
                           and isnull(s.flags, 0) & 1 = 0 /*session bot*/
                           and (
                                     sa.id_campaign = 0
                                 or au.flags & 2 = 0 /*client uses his external statistics for traffic*/
                             )
                           and isnull(sa.flags, 0) & 2 = 0 /*test campaign click*/
                           and coalesce(lower(ip.name), '') not like 'j-vers.%'

                         union all

                         select sc.date_diff,
                                null                                as traffic_source,
                                s.id_current_traf_source,
                                null                                    as id_away,
                                case
                                    when sa.id_jdp is not null
                                        then 'Away from JDP'
                                    when sa.id_click is not null
                                        then 'Away from SERP'
                                    when isnull(sa.letter_type, sj.letter_type) is not null
                                        then 'Away from LT8'
                                    else 'Other'
                                    end                                 as away_type,
                                sc.id_project,
                                 case when sa.flags & 2048 = 2048 or sj.source = 9 or ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
                                 when sa.flags & 4096 = 4096  or ss.search_source in (145) then 2--  category page
                                 when ss.search_source in (146, 147, 148, 149, 150) then 3-- company page
                                 when ss.search_source in (151, 152, 153, 154) then 4-- skill page
                                 when ss.search_source in (155, 156, 157, 158) then 5-- JobDescription page
                                 end as add_placement ,
                                ip.name                                 as project_name,
                                ac.name                                 as campaign_name,
                                ac.id                                   as id_campaign,
                                sc.click_price * ic.value_to_usd           click_price_usd,
                                isnull(sa.letter_type, sj.letter_type)     letter_type,
                                sc.id_recommend,
                                sc.id_alertview,
                                sc.id_search,
                                ext.id                                     id_external,
                                case
                                    when sc.click_price <> 0
                                        then 'paid'
                                    when (sc.flags & 128 = 128 or sj.flags & 256 = 256)
                                        then 'premium'
                                    else 'free'
                                    end                                 as is_paid,
                                case
                                    when s.flags & 16 = 16 then 1
                                    when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                                    else 0 end
                                                                        as is_mobile,
                                s.ip_cc,
                                sign(s.flags & 2)                       as is_returned,
                                s.session_create_page_type,
                                case
                                    when uts.is_paid = 1 and
                                         (select top 1 flags
                                          from auction.campaign_log
                                          where date <= sc.date and id_campaign = ac.id
                                          order by date desc) & 32 = 32
                                        then 1
                                    else 0 end                          as is_paid_overflow,
                                case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                          as is_local,
                                s.id_traf_source,
                                null                                    as id_account_chatbot,
                                iif(sc.flags & 4096 != 0, 1, 0)         as is_duplicated,
                                coalesce(jh.id_category, j.id_category) as id_job_category,
                                au.id                                   as user_id
                         from dbo.session_click sc (nolock)
                                  inner join dbo.session s (nolock)
                                             on sc.date_diff = s.date_diff
                                                 and sc.id_session = s.id
                                  left join dbo.u_traffic_source uts (nolock)
                                            on uts.id = s.id_traf_source
                                  inner join dbo.info_currency ic (nolock)
                                             on ic.id = sc.id_currency
                                  left join auction.campaign ac (nolock)
                                            on ac.id = sc.id_campaign
                                  left join auction.site ast (nolock)
                                            on ac.id_site = ast.id
                                  left join auction.[user] au (nolock)
                                            on au.id = ast.id_user
                                  left join dbo.info_project ip with (nolock)
                                            on sc.id_project = ip.id
                                  left join dbo.session_away sa (nolock)
                                            on sc.date_diff = sa.date_diff
                                                and sc.id = sa.id_click
                                  left join dbo.session_jdp sj (nolock)
                                            on sj.date_diff = sa.date_diff
                                                and sj.id = sa.id_jdp
                                  left join dbo.session_external ext (nolock)
                                            on ext.date_diff = sc.date_diff
                                                and
                                               (
                                                           ext.id_away = sa.id
                                                       or ext.id_jdp = sj.id
                                                   )
                                  left join dbo.job j (nolock)
                                            on sc.id_job = j.id
                                  left join dbo.job_history jh (nolock)
                                            on sc.uid_job = jh.uid
                                  left join dbo.session_search ss (nolock) on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search
                         where sc.date_diff between @date_diff and @dt_end
                           and isnull(s.flags, 0) & 1 = 0
                           and (
                                     sc.id_campaign = 0
                                 or au.flags & 2 = 2 /*client uses his external statistics for traffic*/
                             )
                           and isnull(sc.flags, 0) & 16 = 0 /*test campaign click*/
                           and coalesce(lower(ip.name), '') not like 'j-vers.%'

                         union all

                         select scns.date_diff,
                                null                                as traffic_source,
                                s.id_current_traf_source,
                                null                                    as id_away,
                                case
                                    when sa.id_jdp is not null
                                        then 'Away from JDP'
                                    when sa.id_click is not null
                                        then 'Away from SERP'
                                    when sa.letter_type is not null
                                        then 'Away from LT8'
                                    else 'Other'
                                    end                                 as away_type,
                                scns.id_project,
                                null as add_placement,
                                ip.name                                 as project_name,
                                ac.name                                 as campaign_name,
                                ac.id                                   as id_campaign,
                                scns.click_price * ic.value_to_usd         click_price_usd,
                                scns.letter_type                           letter_type,
                                scns.id_recommend,
                                null                                       id_alertview,
                                null                                       id_search,
                                ext.id                                  as id_external,
                                case
                                    when scns.click_price <> 0
                                        then 'paid'
                                    when scns.flags & 128 = 128
                                        then 'premium'
                                    else 'free'
                                    end                                 as is_paid,
                                case
                                    when s.flags & 16 = 16 then 1
                                    when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                                    else 0 end
                                                                        as is_mobile,
                                s.ip_cc,
                                sign(s.flags & 2)                       as is_returned,
                                s.session_create_page_type,
                                case
                                    when uts.is_paid = 1 and
                                         (select top 1 flags
                                          from auction.campaign_log
                                          where date <= scns.date and id_campaign = ac.id
                                          order by date desc) & 32 = 32
                                        then 1
                                    else 0 end                          as is_paid_overflow,
                                case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                          as is_local,
                                s.id_traf_source,
                                null                                    as id_account_chatbot,
                                iif(scns.flags & 4096 != 0, 1, 0)       as is_duplicated,
                                coalesce(jh.id_category, j.id_category) as id_job_category,
                                au.id                                   as user_id
                         from dbo.session_click_no_serp scns (nolock)
                                  inner join dbo.session s (nolock) on scns.date_diff = s.date_diff
                             and scns.id_session = s.id
                                  left join dbo.u_traffic_source uts (nolock) on s.id_traf_source = uts.id
                                  left join dbo.info_currency ic (nolock) on ic.id = scns.id_currency
                                  inner join auction.campaign ac (nolock) on ac.id = scns.id_campaign
                                  inner join auction.site ast (nolock) on ac.id_site = ast.id
                                  inner join auction.[user] au (nolock) on au.id = ast.id_user
                                  left join dbo.session_away sa (nolock)
                                            on scns.date_diff = sa.date_diff
                                                and scns.id = sa.id_click_no_serp
                                  left join dbo.info_project ip (nolock)
                                            on scns.id_project = ip.id
                                  left join dbo.session_jdp sj (nolock)
                                            on sj.id_click_no_serp = scns.id and sj.date_diff = scns.date_diff
                                  left join dbo.session_external ext (nolock)
                                            on ext.date_diff = scns.date_diff
                                                and (
                                                           ext.id_away = sa.id
                                                       or ext.id_jdp = sj.id
                                                   )
                                  left join dbo.job j (nolock)
                                            on scns.id_job = j.id
                                  left join dbo.job_history jh (nolock)
                                            on scns.uid_job = jh.uid
                         where scns.date_diff between @date_diff and @dt_end
                           and isnull(s.flags, 0) & 1 = 0
                           and au.flags & 2 = 2
                           and isnull(scns.flags, 0) & 16 = 0
                           and coalesce(lower(ip.name), '') not like 'j-vers.%'

-- JDP ONLY CLICKS
                         union all

                         select sj.date_diff,
                                null                                               as traffic_source,
                                s.id_current_traf_source,
                                sj.id                                                    id_jdp,
                                'JDP only'                                            as away_type,
                                sj.job_id_project,
                                 case when sa.flags & 2048 = 2048 or sj.source = 9 or ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
                                 when sa.flags & 4096 = 4096  or ss.search_source in (145) then 2--  category page
                                 when ss.search_source in (146, 147, 148, 149, 150) then 3-- company page
                                 when ss.search_source in (151, 152, 153, 154) then 4-- skill page
                                 when ss.search_source in (155, 156, 157, 158) then 5-- JobDescription page
                                 end as add_placement ,
                                ip.name                                               as project_name,
                                ac.name                                               as campaign_name,
                                ac.id                                                 as id_campaign,
                                0                                                     as click_price_usd,
                                sj.letter_type,
                                sc.id_recommend,
                                sc.id_alertview,
                                sc.id_search,
                                ext.id                                                as id_external,
                                case
                                    when coalesce(sja.click_price, sc.click_price, scns.click_price) <> 0
                                        then 'paid'
                                    when (sc.flags & 128 = 128 or sj.flags & 256 = 256)
                                        then 'premium'
                                    else 'free'
                                    end                                               as is_paid,
                                case
                                    when s.flags & 16 = 16 then 1
                                    when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                                    else 0 end
                                                                                      as is_mobile,
                                s.ip_cc,
                                sign(s.flags & 2)                                     as is_returned,
                                s.session_create_page_type,
                                case
                                    when uts.is_paid = 1 and
                                         (select top 1 flags
                                          from auction.campaign_log
                                          where date <= sc.date and id_campaign = ac.id
                                          order by date desc) & 32 = 32
                                        then 1
                                    else 0 end                                        as is_paid_overflow,
                                case
                                    when s.ip_cc = lower(substring(db_name(), 5, 2))
                                        or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                                        then 1
                                    else 0 end                                        as is_local,
                                s.id_traf_source,
                                null                                                  as id_account_chatbot,
                                iif(coalesce(sc.flags, scns.flags) & 4096 != 0, 1, 0) as is_duplicated,
                                coalesce(jh.id_category, j.id_category)               as id_job_category,
                                ast.id_user                                           as user_id
                         from dbo.session_jdp sj (nolock)
                                  inner join dbo.session s (nolock)
                                             on sj.date_diff = s.date_diff
                                                 and sj.id_session = s.id
                                  left join dbo.u_traffic_source uts (nolock)
                                            on uts.id = s.id_traf_source
                                  left join dbo.session_click sc (nolock)
                                            on sc.date_diff = sj.date_diff
                                                and sc.id = sj.id_click
                                  left join dbo.session_search ss (nolock)
                                           on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search
                                  left join auction.campaign ac (nolock)
                                            on ac.id = sc.id_campaign
                                  left join auction.site ast (nolock)
                                            on ac.id_site = ast.id
                                  left join dbo.info_project ip with (nolock)
                                            on sj.job_id_project = ip.id
                                  left join dbo.session_away sa (nolock)
                                            on sj.date_diff = sa.date_diff
                                                and sj.id = sa.id_jdp
                                  left join dbo.session_click_no_serp scns (nolock)
                                            on sj.date_diff = scns.date_diff
                                                and sj.id_click_no_serp = scns.id
                                  left join dbo.session_jdp_action sja (nolock)
                                            on sj.date_diff = sja.date_diff
                                                and sj.id_ref_action = sja.id
                                  left join dbo.session_external ext (nolock)
                                            on ext.date_diff = sj.date_diff
                                                and
                                               (
                                                           ext.id_away = sa.id
                                                       or ext.id_jdp = sj.id
                                                   )
                                  left join dbo.job_region jr (nolock)
                                            on sj.uid_job = jr.uid
                                  left join dbo.job j (nolock)
                                            on jr.id_job = j.id
                                  left join dbo.job_history jh (nolock)
                                            on sj.uid_job = jh.uid
                         where sa.id is null
                           and sj.date_diff between @date_diff and @dt_end
                           and isnull(s.flags, 0) & 1 = 0
                           and isnull(sc.flags, 0) & 16 = 0 /*test campaign click*/
                           and coalesce(lower(ip.name), '') not like 'j-vers.%') s1
                    left join dbo.info_project with(nolock)
                           on s1.id_project = info_project.id
                    left join
                          (  select  id_alertview,
                                     min(id_message)  as id_message
                             from dbo.session_alertview_message  with (nolock)
                             where date_diff = @date_diff
                              group by id_alertview
                          ) sam
                          on sam.id_alertview = s1.id_alertview
                   left join dbo.email_sent with(nolock)
                          on email_sent.id_message = sam.id_message
                   left join
                         (select   id_session_away
                         from auction.conversion_away_connection with (nolock)
                                  group by id_session_away
                          ) conversion_away_connection
                         on conversion_away_connection.id_session_away = s1.id_away
                    left join dbo.session_jdp_action SJA with (nolock)
                         on s1.date_diff = SJA.date_diff and s1.id_away = SJA.id_jdp
                    left join dbo.session_apply SAA with (nolock)
                         on SAA.date_diff = SJA.date_diff and SAA.id_src_jdp_action = SJA.id
                    ) s2
          group by s2.date_diff,
                   s2.traffic_source,
                   s2.id_current_traf_source,
                   s2.placement,
                   s2.away_type,
                   id_project,
                   project_name,
                   campaign_name,
                   id_campaign,
                   is_paid,
                   is_mobile,
                   ip_cc,
                   is_returned,
                   session_create_page_type,
                   is_paid_overflow,
                   id_traf_source,
                   is_local,
                   id_account_chatbot,
                   id_job_category,
                   user_id)

select date_diff       as action_datediff,
       traffic_source  as traffic_source_name,
       id_current_traf_source,
       placement,
       away_type,
       id_project      as project_id,
       project_name,
       campaign_name,
       id_campaign,
       is_paid,
       is_mobile,
       ip_cc,
       is_returned,
       session_create_page_type,
       is_paid_overflow,
       revenue_usd,
       jdp_away_count,
       duplicated_revenue_usd,
       duplicated_count,
       id_traf_source,
       is_local,
       id_account_chatbot,
       id_job_category as job_category_id,
       user_id,
       conversions
from Final;
