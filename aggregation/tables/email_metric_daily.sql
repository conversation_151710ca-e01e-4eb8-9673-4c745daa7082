SET NOCOUNT ON;

-- reload last 7 days

declare @date_diff int = :to_sqlcode_date_or_datediff_start;



-- session data
select id_project, date_diff, id, id_jdp, id_session, id_click, flags
into #tmp_session_away from dbo.session_away where date_diff = @date_diff
;
-- create index idx_tmp_session_away on #tmp_session_away (date_diff, id, id_session, id_click);
-- create index idx_tmp_session_away_flags on #tmp_session_away (flags);

select date_diff, id_session_away
into #tmp_conversion_away_connection from auction.conversion_away_connection
;
-- create index idx_tmp_conversion_away_connection on #tmp_conversion_away_connection (date_diff, id_session_away)
-- /session data


-- prepate conversion data
select sa.id_project,
       min(con.date_diff) as date_diff
into #tmp_conv_start
from #tmp_session_away sa with (nolock)
         join #tmp_conversion_away_connection con on con.date_diff = sa.date_diff and con.id_session_away = sa.id
group by sa.id_project
;
-- create index idx_tmp_conv_start on #tmp_conv_start (id_project);
-- /prepate conversion data


-- emails
Select id_message, date_diff, id_account
into #email_sent
from dbo.email_sent with (nolock)
where date_diff = @date_diff
;

-- 1
Select email_open.id_message,
       email_open.date_diff
into #email_open_raw
from dbo.email_open with (nolock)
union all
Select email_visit.id_message,
       email_visit.date_diff
from dbo.email_visit with (nolock)
;
-- 2
Select email_open.id_message,
      min(email_open.date_diff) as date_diff
into #email_open_aggregated
from #email_open_raw email_open
group by email_open.id_message
;
-- create index idx_email_open_aggregated on #email_open_aggregated (id_message);
-- 3
Select email_open.id_message,
    email_open.date_diff,
    email_sent.id_account
into #email_open
from #email_open_aggregated email_open
      join dbo.email_sent email_sent on email_open.id_message = email_sent.id_message
where email_open.date_diff = @date_diff
;
-- create index idx_email_open on #email_open (id_message);


Select email_visit.id_message,
       min(email_visit.date_diff) as date_diff
into #email_visit_raw
from dbo.email_visit with (nolock)
group by email_visit.id_message
;

Select email_visit.id_message,
    email_visit.date_diff,
    email_sent.id_account
into #email_visit
from #email_visit_raw email_visit
      join dbo.email_sent email_sent on email_visit.id_message = email_sent.id_message
where email_visit.date_diff = @date_diff
;
-- create index idx_email_visit on #email_visit (id_message);


Select id_message,
       id_account
into #yesterday_emails
from #email_sent email_sent
union
Select id_message,
       id_account
from #email_open email_open
union
Select id_message,
       id_account
from #email_visit email_visit
;
-- create index idx_yesterday_emails on #yesterday_emails (id_message);
-- /emails




--- aggregation #1
select distinct date_diff, id_session_away
into #tmp_conv from auction.conversion_away_connection
;
-- create index idx_tmp_conv on #tmp_conv (date_diff, id_session_away);


Select id,
       date_diff as date_diff
into #session 
from dbo.session with (nolock)
where flags & 1 = 0
  and flags & 4 = 0
  and date_diff = @date_diff
;
-- create index idx_session on #session (id, date_diff)



select coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                session_click_message.id_message)                     as id_message,
       count(distinct sa.id)                                          as away_open_cnt,
       count(distinct case when sa.id_jdp is not null then sa.id end) as away_jdp_cnt,
       ---
       count(distinct conv.id_session_away)                           as conversions,
       count(distinct case
                          when sa.date_diff >= conv_start.date_diff
                              then sa.id end)               as conversion_aways
into #away_click
from #tmp_session_away sa with (nolock)
         join #session session_m (nolock) on sa.date_diff = session_m.date_diff
                                               and sa.id_session = session_m.id
         left join dbo.session_away_message with (nolock) on sa.date_diff = session_away_message.date_diff
                                                                 and sa.id = session_away_message.id_away
         left join dbo.session_click with (nolock) on sa.date_diff = session_click.date_diff
                                                          and sa.id_click = session_click.id
         left join dbo.session_click_message with (nolock) on session_click_message.date_diff = session_click.date_diff
                                                                  and session_click_message.id_click = session_click.id
         left join dbo.session_alertview_message with (nolock) on session_click.date_diff = session_alertview_message.date_diff
                                                                      and session_click.id_alertview = session_alertview_message.id_alertview
         left join #tmp_conv conv on conv.date_diff = sa.date_diff
                                         and conv.id_session_away = sa.id
         left join #tmp_conv_start conv_start with (nolock) on sa.id_project = conv_start.id_project
where isnull(sa.flags, 0) & 2 = 0
  and isnull(sa.flags, 0) & 512 = 0
group by coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                  session_click_message.id_message)
;
create index idx_away_click on #away_click (id_message);




--- aggregation №2
select email_sent.id_message,
       max(case when email_open.id_message is not null then 1 else 0 end)  as is_open,
       max(case when email_visit.id_message is not null then 1 else 0 end) as is_visit
into #email_conversion
from #yesterday_emails email_sent
         left join #email_open email_open on email_sent.id_message = email_open.id_message
         left join #email_visit email_visit on email_sent.id_message = email_visit.id_message
group by email_sent.id_message
;
-- create index idx_email_conversion on #email_conversion (id_message);


select email_alert.id_account,
       count(email_alert.id) as alerts
into #alert
from dbo.email_alert with (nolock)
where email_alert.id_type_unsub is null
group by email_alert.id_account
;
-- create index idx_alert on #alert (id_account);


select email_sent.id_message,
       count(distinct id_alertview) as alertviews
into #alertviews
from dbo.session_alertview_message sam with (nolock)
         join #yesterday_emails email_sent on sam.id_message = email_sent.id_message
                                                  and sam.date_diff = @date_diff
group by email_sent.id_message
;
-- create index idx_alertviews on #alertviews (id_message);
-----------


--- email_sent full
select id_message, id_account, calc_result_count, letter_type, date, date_diff
into #tmp_email_sent from dbo.email_sent
;
--- /email_sent


select id_message, date_diff, id_alertview
into #tmp_session_alertview_message from dbo.session_alertview_message where date_diff = @date_diff
;
select id_alertview, date_diff, id
into #tmp_session_click from dbo.session_click where date_diff = @date_diff
;
select id_click, date_diff, id
into #tmp_session_jdp from dbo.session_jdp where date_diff = @date_diff
;
select id_jdp, date_diff, id
into #tmp_session_jdp_action from dbo.session_jdp_action where date_diff = @date_diff
;
select id_src_jdp_action, date_diff, id
into #tmp_session_apply from dbo.session_apply where date_diff = @date_diff
;
select id_message, date_diff, id_click
into #tmp_session_click_message from dbo.session_click_message where date_diff = @date_diff
;



--- aggregation №3
select email_sent.id_message as message_id,
       session_jdp.id        as id_jdp,
       session_apply.id      as id_apply
into #tt
from #tmp_email_sent email_sent with (nolock)
         inner join #tmp_session_alertview_message session_alertview_message with (nolock) on session_alertview_message.id_message = email_sent.id_message
                                                                and session_alertview_message.date_diff = @date_diff -- added date_diff
         inner join #tmp_session_click session_click with (nolock) on session_alertview_message.id_alertview = session_click.id_alertview
                                                                and session_click.date_diff = @date_diff -- added date_diff
         inner join #tmp_session_jdp session_jdp with (nolock) on session_jdp.id_click = session_click.id
                                                                and session_jdp.date_diff = @date_diff -- added date_diff
         left join #tmp_session_jdp_action session_jdp_action with (nolock) on session_jdp.id = session_jdp_action.id_jdp
                                                                and session_jdp_action.date_diff = @date_diff -- added date_diff
         left join #tmp_session_apply session_apply with (nolock) on session_jdp_action.id = session_apply.id_src_jdp_action
                                                                and session_apply.date_diff = @date_diff -- added date_diff
where session_jdp.date_diff = @date_diff
union all
select email_sent.id_message as message_id,
       session_jdp.id        as id_jdp,
       session_apply.id      as id_apply
from #tmp_email_sent email_sent with (nolock)
         inner join #tmp_session_click_message session_click_message with (nolock) on session_click_message.id_message = email_sent.id_message
                                                                    and session_click_message.date_diff = @date_diff -- added date_diff
         inner join #tmp_session_click session_click with (nolock) on session_click_message.id_click = session_click.id
                                                                    and session_click.date_diff = @date_diff -- added date_diff
         inner join #tmp_session_jdp session_jdp with (nolock) on session_jdp.id_click = session_click.id
                                                                    and session_jdp.date_diff = @date_diff -- added date_diff
         left join #tmp_session_jdp_action session_jdp_action with (nolock) on session_jdp.id = session_jdp_action.id_jdp
                                                                    and session_jdp_action.date_diff = @date_diff -- added date_diff
         left join #tmp_session_apply session_apply with (nolock) on session_jdp_action.id = session_apply.id_src_jdp_action
                                                                    and session_apply.date_diff = @date_diff -- added date_diff
where session_jdp.date_diff = @date_diff
;

-- email_open
SELECT message_id,
       count(distinct id_jdp)   as jdp_cnt,
       count(distinct id_apply) as apply_cnt
into #email_open_jdp
FROM #tt t
GROUP BY message_id
;
-- create index idx_email_open_jdp on #email_open_jdp (message_id);
-- /email_open


--- prepare account data
select id, send_interval, date_add
into #tmp_account from dbo.account
;
-- create index idx_tmp_account on #tmp_account (id);

select id_account, id_traf_src
into #tmp_account_info from dbo.account_info
;
-- create index idx_tmp_account_info on #tmp_account_info (id_account);
-- /prepare account data


-- pre-aggregation of all data
select email_sent.date              as email_sent_date,
       email_sent.letter_type       as email_sent_letter_type,
       email_sent.calc_result_count as email_sent_calc_result_count,
       alert.alerts                 as alert_alerts,
       account.send_interval        as account_send_interval,
       account_info.id_traf_src     as account_info_id_traf_src,
       account.date_add             as account_date_add,
       account.id                   as account_id,
       email_sent.date_diff         as email_sent_date_diff,
       email_sent.id_message        as email_sent_id_message,
       email_conversion.is_open     as email_conversion_is_open,
       email_conversion.id_message  as email_conversion_id_message,
       email_conversion.is_visit    as email_conversion_is_visit,
       email_open_jdp.apply_cnt     as email_open_jdp_apply_cnt,
       away_click.away_open_cnt     as away_click_away_open_cnt,
       away_click.away_jdp_cnt      as away_click_away_jdp_cnt,
       away_click.id_message        as away_click_id_message,
       alertviews.alertviews        as alertviews_alertviews,
       email_open_jdp.jdp_cnt       as email_open_jdp_jdp_cnt,
       email_open_jdp.message_id    as email_open_jdp_message_id,
       away_click.conversion_aways  as away_click_conversion_aways,
       away_click.conversions       as away_click_conversions
into #raw_aggregated_data
from #yesterday_emails
    inner join #tmp_email_sent email_sent with (nolock) on #yesterday_emails.id_message = email_sent.id_message
    left join #tmp_account account with (nolock) on email_sent.id_account = account.id
    left join #tmp_account_info account_info with (nolock) on account.id = account_info.id_account
    left join #email_open_jdp email_open_jdp with (nolock) on email_open_jdp.message_id = email_sent.id_message
    left join #away_click away_click with (nolock) on away_click.id_message = email_sent.id_message
    left join #email_conversion email_conversion on email_conversion.id_message = email_sent.id_message
    left join #alert alert on email_sent.id_account = alert.id_account
    left join #alertviews alertviews on email_sent.id_message = alertviews.id_message
;

update statistics #raw_aggregated_data;
--- /pre-aggregation




-- final aggregation
select @date_diff                                                                                                 as date_diff,
       cast(cast(email_sent_date as date) as datetime)                                                            as sent_date,
       email_sent_letter_type                                                                                     as letter_type,
       case
           when email_sent_calc_result_count < 11 then email_sent_calc_result_count
           when email_sent_calc_result_count > 10
               then 10 end                                                                                        as calc_result_count,
       case
           when alert_alerts <= 5 then cast(alert_alerts as char)
           when alert_alerts > 5
               then '5+' end                                                                                      as account_alerts,
       account_send_interval                                                                                      as account_send_interval,
       account_info_id_traf_src                                                                                   as account_traffic_source,
       cast(DATEFROMPARTS(year(account_date_add), month(account_date_add),
                          1) as datetime)                                                                         as account_date,
       sum(alertviews_alertviews)                                                                                 as alertview_cnt,
       count(distinct account_id)                                                                                 as account_cnt,
       count(distinct case
                          when email_sent_date_diff = @date_diff
                              then email_sent_id_message end)                                                     as sent_msg,
       count(distinct case
                          when email_conversion_is_open = 1
                              then email_conversion_id_message end)                                               as open_msg,
       count(distinct case
                          when email_conversion_is_visit = 1
                              then email_conversion_id_message end)                                               as visit_msg,
       sum(email_open_jdp_jdp_cnt)                                                                                as jdp_cnt,
       sum(email_open_jdp_apply_cnt)                                                                              as apply_cnt,
       sum(away_click_away_open_cnt)                                                                              as away_cnt,
       -----
       sum(away_click_conversion_aways)                                                                           as conversion_aways,
       sum(away_click_conversions)                                                                                as conversions,
       -----
       sum(away_click_away_jdp_cnt)                                                                                          as away_jdp_cnt,
       count(distinct away_click_id_message)                                                                      as message_with_away_cnt,
       count(distinct case
                          when alertviews_alertviews is not null
                              then email_sent_id_message end)                                                     as message_with_alertview_cnt,
       count(distinct case
                          when email_open_jdp_jdp_cnt > 0
                              then email_open_jdp_message_id end)                                                 as message_with_jdp_cnt,
       count(distinct case when away_click_away_jdp_cnt > 0 then away_click_id_message end)                                  as message_with_jdp_away_cnt,
       null                                                                                                       as account_unsub_cnt,
       null                                                                                                       as alert_position,
       null                                                                                                       as email_revenue,
       null                                                                                                       as max_away_position
into #result
from #raw_aggregated_data
group by cast(email_sent_date as date),
         email_sent_letter_type,
         case
             when email_sent_calc_result_count < 11 then email_sent_calc_result_count
             when email_sent_calc_result_count > 10 then 10 end,
         case
             when alert_alerts <= 5 then cast(alert_alerts as char)
             when alert_alerts > 5 then '5+' end,
         account_send_interval,
         DATEFROMPARTS(year(account_date_add), month(account_date_add), 1),
         account_info_id_traf_src
;

select 
    date_diff,
    sent_date,
    letter_type,
    cast(calc_result_count as integer) as calc_result_count,
    account_alerts,
    account_send_interval,
    account_traffic_source,
    account_date,
    alertview_cnt,
    account_cnt,
    sent_msg,
    open_msg,
    visit_msg,
    jdp_cnt,
    apply_cnt,
    away_cnt,
    conversion_aways,
    conversions,
    away_jdp_cnt,
    message_with_away_cnt,
    message_with_alertview_cnt,
    message_with_jdp_cnt,
    message_with_jdp_away_cnt,
    account_unsub_cnt,
    alert_position,
    email_revenue,
    max_away_position
from #result
;
