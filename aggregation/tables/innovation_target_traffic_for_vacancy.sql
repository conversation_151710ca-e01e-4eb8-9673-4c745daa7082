declare  @dt_begin int = ${dt_begin},
		 @dt_end int = ${dt_end},
		 @country_id int = ${country_id};

SELECT @country_id										   as country_id,
       dateadd(day, SJ.date_diff, '1900-01-01')            as session_date,
       SJ.job_id_project                                   as id_project,
       info_project.name                                   as project_name,
       campaign.name                                       as campaign_name,
       sc.id_campaign,
	   session.id_current_traf_source,
	   session.session_create_page_type,
       u_traffic_source.name                               as traffic_name,
       u_traffic_source.is_paid                            as traffic_is_paid,
       u_traffic_source.channel,
	   SC.uid_job,
	   jr.id_job										   as id_job,
	   j.title											   as job_title,
	   jr.id_region,
	   ir.name											   as region_name,
	   ir.is_city,
       session.ip_cc,
	   case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128  then 2
           else 0 end                                      as is_mobile,
	   case when session.flags & 2 = 2 then 1
			else 0 end					                   as is_returned,
       info_currency.name								   as currency_name,
       0                                                   as revenue,
       0                                                   as apply_revenue_origin_currency,
       count(distinct SJ.id)                               as applies_cnt,
       count(distinct SA.id)                               as conversions_cnt

from dbo.session_jdp SJ with (nolock)
         left join dbo.session_jdp_action SJA with (nolock)
                   on SJ.date_diff = SJA.date_diff
                       and SJ.id = SJA.id_jdp
         left join dbo.session_apply SA with (nolock)
                   on SA.date_diff = SJA.date_diff
                       and SA.id_src_jdp_action = SJA.id
         join dbo.session session with (nolock)
              on SJ.date_diff = session.date_diff
                  and SJ.id_session = session.id
         left join dbo.session_click SC with (nolock)
                   on SJ.date_diff = SC.date_diff
                       and SJ.id_click = SC.id
         left join dbo.u_traffic_source with (nolock)
                   on session.id_current_traf_source = u_traffic_source.id
         left join dbo.info_project with (nolock)
                   on SJ.job_id_project = info_project.id
         left join auction.campaign with (nolock)
                   on SC.id_campaign = campaign.id
         left join dbo.info_currency with (nolock)
                   on SC.id_currency = info_currency.id
		 left join dbo.job_region jr with (nolock)
				   on jr.uid = SC.uid_job
		 left join dbo.info_region ir with (nolock)
				   on jr.id_region = ir.id
		 left join dbo.job j with (nolock)
				   on jr.id_job = j.id

where  SJ.date_diff between @dt_begin and @dt_end
		and session.flags & 1 != 1
		  and sj.flags & 4 = 4
			and u_traffic_source.channel = 'Paid Search'
			  and SJ.job_id_project = 16902  /* appcast only */

group by dateadd(day, SJ.date_diff, '1900-01-01'),
         SJ.job_id_project,
         info_project.name,
         campaign.name,
		 SC.id_campaign,
		 session.id_current_traf_source,
		 session.session_create_page_type,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         u_traffic_source.channel,
	     SC.uid_job,
	     jr.id_job,
		 j.title,
		 jr.id_region,
		 ir.name,
		 ir.is_city,
		 session.ip_cc,
         case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128  then 2
           else 0 end,
		 case when session.flags & 2 = 2 then 1 else 0 end,
		 info_currency.name

UNION ALL

   select
       @country_id								 as country_id,
       dateadd(day, date_diff, '1900-01-01')     as session_date,
       id_project,
       project_name,
       campaign_name,
       id_campaign,
	   id_current_traf_source,
	   session_create_page_type,
       traffic_name,
       traffic_is_paid,
       channel,
	   uid_job,
	   id_job,
	   job_title,
	   id_region,
	   region_name,
	   is_city,
       ip_cc,
	   is_mobile,
	   is_returned,
       name								 as currency_name,
       sum(away_revenue)				 as revenue,
       sum(away_revenue_origin_currency) as apply_revenue_origin_currency,
       0						         as applies_cnt,
       0						         as conversions_cnt

from (
      select
       sc.date_diff,
       sc.id_project,
       info_project.name	as project_name,
       campaign.name		as campaign_name,
       sc.id_campaign,
       case
           when s.flags & 16 = 16 then 1
           when s.flags & 64 = 64 or s.flags & 128 = 128  then 2
           else 0 end                                           as is_mobile,
       u_traffic_source.name                                    as traffic_name,
       u_traffic_source.is_paid                                 as traffic_is_paid,
       u_traffic_source.channel,
       s.ip_cc,
       ic.name,
	   SC.uid_job,
	   jr.id_job										   as id_job,
	   j.title											   as job_title,
	   jr.id_region,
	   ir.name											   as region_name,
	   ir.is_city,
       coalesce(sc.click_price, 0) * ic.value_to_usd			as away_revenue,
       coalesce(sc.click_price, 0)                              as away_revenue_origin_currency,
	   s.id_current_traf_source,
	   s.session_create_page_type,
	   case when s.flags & 2 = 2 then 1 else 0 end              as is_returned

    from dbo.session_click sc (nolock)
      inner join dbo.session s (nolock) on sc.date_diff = s.date_diff
                                       and sc.id_session = s.id
      inner join dbo.info_currency ic (nolock) on ic.id = sc.id_currency
      left join auction.campaign ac (nolock) on ac.id = sc.id_campaign
      left join auction.site ast (nolock) on ac.id_site = ast.id
      left join auction.[user] au (nolock) on au.id = ast.id_user
      left join dbo.session_away sa (nolock) on sc.date_diff = sa.date_diff
                                            and sc.id = sa.id_click
      left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff
                                           and sj.id = sa.id_jdp
      left join dbo.info_project with (nolock)
                   on sc.id_project = info_project.id
      left join auction.campaign with (nolock)
                   on sc.id_campaign = campaign.id
      left join dbo.u_traffic_source with (nolock)
                   on s.id_current_traf_source = u_traffic_source.id
	  left join dbo.job_region jr with (nolock) on jr.uid = SC.uid_job
	  left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
	  left join dbo.job j with (nolock) on jr.id_job = j.id

   where sc.date_diff between @dt_begin and @dt_end
        and isnull(s.flags, 0) & 1 = 0
        and
        (
          sc.id_campaign = 0
          or au.flags & 2 = 2
        )
        and isnull(sc.flags, 0) & 16 = 0
        and sc.flags & 4096 = 0
		and sc.job_destination = 3 /* is_apply = 1  only  */
		and u_traffic_source.channel = 'Paid Search'
		and sc.id_project = 16902  /* appcast only */

	  union all

      select
       scns.date_diff,
       scns.id_project,
       info_project.name	as project_name,
       campaign.name		as campaign_name,
       scns.id_campaign,
       case
           when s.flags & 16 = 16 then 1
           when s.flags & 64 = 64 or s.flags & 128 = 128  then 2
           else 0 end                                               as is_mobile,
       u_traffic_source.name                                        as traffic_name,
       u_traffic_source.is_paid                                     as traffic_is_paid,
       u_traffic_source.channel,
       s.ip_cc,
       ic.name,
	   scns.uid_job,
	   jr.id_job										   as id_job,
	   j.title											   as job_title,
	   jr.id_region,
	   ir.name											   as region_name,
	   ir.is_city,
       coalesce(scns.click_price, 0) * ic.value_to_usd				as away_revenue,
       coalesce(scns.click_price, 0)								as away_revenue_origin_currency,
	   s.id_current_traf_source,
	   s.session_create_page_type,
	   case when s.flags & 2 = 2 then 1 else 0 end					as is_returned

      from dbo.session_click_no_serp scns (nolock)
      inner join dbo.session s (nolock) on scns.date_diff = s.date_diff
                                       and scns.id_session = s.id
      inner join dbo.info_currency ic (nolock) on ic.id = scns.id_currency
      inner join auction.campaign ac (nolock) on ac.id = scns.id_campaign
      inner join auction.site ast (nolock) on ac.id_site = ast.id
      inner join auction.[user] au (nolock) on au.id = ast.id_user
      left join dbo.info_project with (nolock)
                   on scns.id_project = info_project.id
      left join auction.campaign with (nolock)
                   on scns.id_campaign = campaign.id
      left join dbo.u_traffic_source with (nolock)
                   on s.id_current_traf_source = u_traffic_source.id
      left join dbo.session_jdp sj (nolock) on sj.id_click_no_serp = scns.id and sj.date_diff = scns.date_diff
      left join dbo.session_away sa (nolock) on sa.id_click_no_serp = scns.id and sa.date_diff = scns.date_diff
	  left join dbo.job_region jr with (nolock) on jr.uid = scns.uid_job
	  left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
	  left join dbo.job j with (nolock) on jr.id_job = j.id

      where
	    	(scns.click_flags & 256 > 0 or scns.click_flags & 512 > 0)
        and scns.date_diff between @dt_begin and @dt_end
        and isnull(s.flags, 0) & 1 = 0
        and au.flags & 2 = 2
        and isnull(scns.flags, 0) & 16 = 0
        and scns.flags & 4096 = 0
		and scns.job_destination = 3 /* is_apply = 1 only  */
		and u_traffic_source.channel = 'Paid Search'
	    and scns.id_project = 16902  /* appcast only */

    ) as  Revenue
group by
       dateadd(day, date_diff, '1900-01-01')                      ,
       id_project,
       project_name,
       campaign_name,
       id_campaign,
	   id_current_traf_source,
	   session_create_page_type,
       traffic_name,
       traffic_is_paid,
       channel,
	   uid_job,
	   id_job,
	   job_title,
	   id_region,
	   region_name,
	   is_city,
       ip_cc,
	   is_mobile,
	   is_returned,
       name;
