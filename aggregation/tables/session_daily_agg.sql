SET NOCOUNT ON;

DE<PERSON>ARE @datediff_start int = :to_sqlcode_date_or_datediff_start,
        @datediff_end int = :to_sqlcode_date_or_datediff_end;

WITH clicks AS (SELECT date_diff,
                       id_session,
                       COUNT(*)      AS clicks,
                       SUM(is_jvers) AS jvers_clicks
                FROM (SELECT date_diff,
                             id_session,
                             session_click.id                                                     AS id_click,
                             CASE WHEN LOWER(info_project.name) LIKE 'j-vers.%' THEN 1 ELSE 0 END AS is_jvers
                      FROM dbo.session_click WITH (NOLOCK)
                               LEFT JOIN dbo.info_project WITH (NOLOCK)
                                         ON session_click.id_project = info_project.id
                      WHERE date_diff BETWEEN @datediff_start AND @datediff_end
                      UNION
                      SELECT date_diff,
                             id_session,
                             session_click_no_serp.id                                             AS id_click,
                             CASE WHEN LOWER(info_project.name) LIKE 'j-vers.%' THEN 1 ELSE 0 END AS is_jvers
                      FROM dbo.session_click_no_serp WITH (NOLOCK)
                               LEFT JOIN dbo.info_project WITH (NOLOCK)
                                         ON session_click_no_serp.id_project = info_project.id
                      WHERE date_diff BETWEEN @datediff_start AND @datediff_end) click
                GROUP BY date_diff,
                         id_session),
     sessions AS (SELECT session.date_diff,
                         id             AS id_session,
                         id_traf_source,
                         id_current_traf_source,
                         session_create_page_type,
                         CASE
                             WHEN session.flags & 16 = 16 THEN 1
                             WHEN session.flags & 64 = 64 OR session.flags & 128 = 128 THEN 2
                             ELSE 0 END AS device,
                         cookie_label,
                         CASE
                             WHEN flags & 1 = 1 OR user_agent_hash64 = 5981440342270357754 THEN 1
                             ELSE 0 END AS bot_session_cnt,
                         CASE
                             WHEN (ip_cc = LOWER(SUBSTRING(DB_NAME(), 5, 2))
                                 OR (ip_cc = 'gb' AND LOWER(SUBSTRING(DB_NAME(), 5, 2)) = 'uk'))
                                 AND flags & 1 = 0 AND user_agent_hash64 != 5981440342270357754
                                 THEN 1
                             ELSE 0 END AS local_session_nobot_cnt,
                         CASE
                             WHEN flags & 16 = 16
                                 AND flags & 1 = 0 AND user_agent_hash64 != 5981440342270357754
                                 THEN 1
                             ELSE 0 END AS mobile_session_nobot_cnt,
                         CASE
                             WHEN flags & 2 = 2
                                 AND flags & 1 = 0 AND user_agent_hash64 != 5981440342270357754
                                 THEN 1
                             ELSE 0 END AS returned_session_nobot_cnt,
                         CASE
                             WHEN (flags & 64 = 64 OR flags & 128 = 128) AND flags & 1 = 0
                                 AND user_agent_hash64 != 5981440342270357754
                                 THEN 1
                             ELSE 0 END AS mobile_app_session_nobot_cnt,
                         clicks         AS clicks
                  FROM dbo.session WITH (NOLOCK)
                           LEFT JOIN clicks
                                     ON session.date_diff = clicks.date_diff
                                         AND session.id = clicks.id_session
                  WHERE session.date_diff BETWEEN @datediff_start AND @datediff_end
                    AND flags & 4 = 0
                    AND (clicks IS NULL OR COALESCE(clicks, 1) > COALESCE(jvers_clicks, 0)))

SELECT date_diff                                                             AS action_datediff,
       id_traf_source,
       id_current_traf_source,
       session_create_page_type,
       CASE WHEN COALESCE(clicks, 0) < 3 THEN COALESCE(clicks, 0) ELSE 3 END AS click_type,
       COUNT(DISTINCT CASE WHEN bot_session_cnt = 0 THEN cookie_label END)   AS user_no_bot_cnt,
       COUNT(id_session)                                                     AS total_session_cnt,
       SUM(bot_session_cnt)                                                  AS bot_session_cnt,
       SUM(local_session_nobot_cnt)                                          AS local_session_nobot_cnt,
       SUM(mobile_session_nobot_cnt)                                         AS mobile_session_nobot_cnt,
       SUM(returned_session_nobot_cnt)                                       AS returned_session_nobot_cnt,
       SUM(mobile_app_session_nobot_cnt)                                     AS mobile_app_session_nobot_cnt,
       SUM(CASE WHEN bot_session_cnt = 0 THEN clicks END)                    AS click_nobot_cnt,
       device
FROM sessions
GROUP BY date_diff,
         id_traf_source,
         id_current_traf_source,
         session_create_page_type,
         CASE WHEN COALESCE(clicks, 0) < 3 THEN COALESCE(clicks, 0) ELSE 3 END,
         device
;
