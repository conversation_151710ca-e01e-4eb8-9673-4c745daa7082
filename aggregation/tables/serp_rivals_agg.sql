

    create temp table if not exists tmp_t as
    select distinct si.date                                                                                    as date_diff,
                    coalesce(si.id_search, si.id_alertview)                                                    as id_search,
                    si.position,
                    coalesce(j.id_project, jh.id_project)                                                      as id_project,
                    round(si.click_price, 3)                                                                   as click_price_usd,
                    round(si.serp_click_value, 3)                                                              as serp_click_value,
                    iif(sis.id_impression is not null, 1, 0)                                                   as is_on_screen,
                    iif(sc.id is not null, 1, 0)                                                               as has_click,
                    row_number()
                    over (partition by coalesce(si.id_search, si.id_alertview), si.position order by si.score) as row_num,
                    coalesce(j.id_category, jh.id_category)                                                    as id_category
    from public.session_impression si
             join public.session s
                  on s.date_diff = si.date
                      and s.id = si.id_session
             left join public.session_impression_on_screen sis
                       on sis.date_diff = si.date and sis.id_impression = si.id
             left join an.snap_job_region jr
                       on jr.uid = si.uid_job
             left join an.snap_job j
                       on j.id = jr.id_job
             left join an.snap_job_history jh
                       on jh.uid = si.uid_job
             left join public.session_click sc
                       on sc.date_diff = si.date and sc.id_impression = si.id
    where si.date = _datediff_start
      and si.position <= 50
      and s.flags & 1 = 0
      and (si.id_search is not null or si.id_alertview is not null)
    ;

    create temp table tmp_serp_project_stat as
    select date_diff,
           id_search,
           position,
           id_project,
           click_price_usd,
           serp_click_value,
           is_on_screen,
           has_click,
           id_category
    from tmp_t
    where row_num = 1;


    create temp table tmp_serp_project_stat2 as
    with clients_list as (select distinct id_project
                          from tmp_serp_project_stat
                          where click_price_usd > 0)
    select iif(id_project in (select * from clients_list), id_project, 0) as rival_project_id,
           date_diff,
           id_search,
           position,
           click_price_usd,
           serp_click_value,
           is_on_screen,
           has_click,
           id_category
    from tmp_serp_project_stat;


    create temp table tmp_serp_keys as
    with serp_keys as (select distinct rival_project_id,
                                       id_search
                       from tmp_serp_project_stat2)
    select t1.rival_project_id as target_project_id,
           t2.rival_project_id,
           t2.id_search
    from serp_keys t1
             join serp_keys t2
                  on t1.id_search = t2.id_search
    where t1.rival_project_id != 0;


    create temp table tmp_result_serp_rivals as
    select tmp_serp_project_stat2.date_diff,
           target_project_id,
           tmp_serp_project_stat2.rival_project_id,
           click_price_usd,
           serp_click_value,
           tmp_serp_project_stat2.id_category,
           count(distinct tmp_serp_keys.id_search) as search_cnt,
           count(*)                                as impression_cnt,
           sum(is_on_screen)                       as impression_on_screen_cnt,
           sum(has_click)                          as click_cnt,
           min(position)                           as position_min,
           max(position)                           as position_max,
           avg(1.0 * position)                     as position_mean
    from tmp_serp_keys
             join tmp_serp_project_stat2
                  on tmp_serp_keys.rival_project_id = tmp_serp_project_stat2.rival_project_id
                      and tmp_serp_keys.id_search = tmp_serp_project_stat2.id_search
    group by tmp_serp_project_stat2.date_diff,
             target_project_id,
             tmp_serp_project_stat2.rival_project_id,
             click_price_usd,
             serp_click_value,
             tmp_serp_project_stat2.id_category;


    insert into tmp_result_serp_rivals
    select tmp_serp_project_stat2.date_diff,
           target_project_id,
           tmp_serp_project_stat2.rival_project_id,
           -1                                      as click_price_usd,
           -1                                      as serp_click_value,
           null                                    as id_category,
           count(distinct tmp_serp_keys.id_search) as search_cnt,
           null                                    as impression_cnt,
           null                                    as impression_on_screen_cnt,
           null                                    as click_cnt,
           null                                    as position_min,
           null                                    as position_max,
           null                                    as position_mean
    from tmp_serp_keys
             join tmp_serp_project_stat2
                  on tmp_serp_keys.rival_project_id = tmp_serp_project_stat2.rival_project_id
                      and tmp_serp_keys.id_search = tmp_serp_project_stat2.id_search
    group by tmp_serp_project_stat2.date_diff,
             target_project_id,
             tmp_serp_project_stat2.rival_project_id;


    --
    delete from an.rpl_serp_rivals_agg
    where date_diff = _datediff_start;
    --

    insert into an.rpl_serp_rivals_agg(country_id, date_diff, target_project_id, rival_project_id, click_price_usd,
                                       serp_click_value, search_cnt, impression_cnt, impression_on_screen_cnt,
                                       click_cnt, position_mean, position_min, position_max, id_category)
    select _country_id as country_id,
           date_diff,
           target_project_id,
           rival_project_id,
           click_price_usd,
           serp_click_value,
           search_cnt,
           impression_cnt,
           impression_on_screen_cnt,
           click_cnt,
           position_mean,
           position_min,
           position_max,
           id_category
    from tmp_result_serp_rivals;

    drop table tmp_serp_project_stat;
    drop table tmp_serp_project_stat2;
    drop table tmp_serp_keys;
    drop table tmp_result_serp_rivals;


