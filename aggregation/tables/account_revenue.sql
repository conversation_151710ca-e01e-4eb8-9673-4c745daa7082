Select
	account_date::date as account_date,
       revenue_date::date as revenue_date,
       source,
       device_type_id,
       max(away_cnt)            as away_cnt,
       max(account_revenue)     as account_revenue,
       max(new_account_cnt)     as new_account_cnt,
       max(revenue_account_cnt) as revenue_account_cnt,
       max(new_verifed_account_cnt) as new_verified_account_cnt,
       max(revenue_verifed_account_cnt) as revenue_verified_account_cnt,
       max(email_revenue)        as email_revenue,
       max(serp_revenue)         as serp_revenue,
       id_traf_src,
	 current_date  as load_date,
       max(total_revenue_mobile_app) as total_revenue_mobile_app,
       max(away_clicks_free_mobile_app) as away_clicks_free_mobile_app,
       max(away_clicks_premium_mobile_app) as away_clicks_premium_mobile_app
from (
         SELECT cast(account.date_add as date)                        as account_date,
                '1900-01-01'::date + account_revenue.date_diff              as revenue_date,
                account.source,
                case
                when account.flags & 1 = 1 then 1 /*mobile*/
                when account_contact.type = 2 or account.source < 1000 then 2 /*mobile app*/
                else 1 /*desktop*/
                end              as device_type_id,
                sum(away_clicks_free + away_clicks_premium)           as away_cnt,
                sum(account_revenue.total_revenue)                    as account_revenue,
                0                                                     as new_account_cnt,
                count(distinct account_revenue.id_account)            as revenue_account_cnt,
                0                                                     as new_verifed_account_cnt,
                count(distinct case when account_contact.verify_date is not null then account_revenue.id_account end) as revenue_verifed_account_cnt,
                sum(account_revenue.email_revenue)                    as email_revenue,
                sum(account_revenue.serp_revenue)                     as serp_revenue,
                account_info.id_traf_src,
                sum(account_revenue.total_revenue_mobile_app)         as total_revenue_mobile_app,
                sum(account_revenue.away_clicks_free_mobile_app)      as away_clicks_free_mobile_app,
                sum(account_revenue.away_clicks_premium_mobile_app)    as away_clicks_premium_mobile_app
         FROM an.account_revenue
                  join link_dbo.account
                       on account_revenue.id_account = account.id
                  join link_dbo.account_contact
                        on account.id = account_contact.id_account
                  join link_dbo.account_info
                        on account.id = account_info.id_account
                  join link_dbo.account_contact
                        on account.id = account_contact.id_account      
         where account_revenue.date_diff = 45299
         group by cast(account.date_add as date),
                  '1900-01-01'::date + account_revenue.date_diff,
                  account.source,
                  case
                  when account.flags & 1 = 1 then 1 /*mobile*/
                  when account_contact.type = 2 or account.source < 1000 then 2 /*mobile app*/
                  else 1 /*desktop*/
                  end,
                  account_info.id_traf_src
         union all
         Select cast(account.date_add as date) as account_date,
                null                           as revenue_date,
                account.source,
                case
                when account.flags & 1 = 1 then 1 /*mobile*/
                when account_contact.type = 2 or account.source < 1000 then 2 /*mobile app*/
                else 1 /*desktop*/
                end              as device_type_id,
                null                           as away_cnt,
                null                           as account_revenue,
                count(distinct account.id)     as new_account_cnt,
                null                           as revenue_account_cnt,
                count(distinct case when account_contact.verify_date is not null then account_contact.id_account end) as new_verifed_account_cnt,
                0 as revenue_verifed_account_cnt,
                null                            as email_revenue,
                null                            as serp_revenue,
                account_info.id_traf_src,
                null                            as total_revenue_mobile_app,
                null                            as away_clicks_free_mobile_app,
                null                            as away_clicks_premium_mobile_app
         from link_dbo.account
         join link_dbo.account_contact
         on account.id = account_contact.id_account
         join link_dbo.account_info
         on account.id = account_info.id_account
         where cast(account.date_add as date) = '1900-01-01'::date + 45299
         group by cast(account.date_add as date), account.source,
             case
                when account.flags & 1 = 1 then 1 /*mobile*/
                when account_contact.type = 2 or account.source < 1000 then 2 /*mobile app*/
                else 1 /*desktop*/
                end,
              account_info.id_traf_src
     ) final
group by account_date,
         revenue_date,
         source,
         device_type_id,
         id_traf_src;
