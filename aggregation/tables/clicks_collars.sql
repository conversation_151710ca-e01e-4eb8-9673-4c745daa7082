with click_data as (
Select coalesce(session_click.id_job, session_click_no_serp.id_job)                         as id_job,
                           coalesce(session_click.uid_job, session_click_no_serp.uid_job)                       as uid,
                           coalesce(u_traffic_source.is_paid::int, 0)                                           as is_paid_source,
                           u_traffic_source.channel,
                           click_metric_agg.placement,
                           sum(click_metric_agg.certified_click_price_usd)                                      as revenue_usd,
                           sum(case when is_paid_click = 'paid' and is_certified_click = 1 then 1 else 0 end)   as cnt_paid_clicks,
                           count(distinct case
                                              when is_certified_click = 1 AND click_type = 1
                                                  then away_id end)                                             as cnt_away,
                           count(distinct case
                                              when is_certified_click = 1 AND click_type = 2
                                                  then away_id end)                                             as cnt_jdp,
                           click_metric_agg.action_datediff                                                     as date_diff
                    from an.click_metric_agg
                             left join public.session_click
                                       on click_metric_agg.action_datediff = session_click.date_diff
                                           and click_metric_agg.click_id = session_click.id
                             left join public.session_click_no_serp
                                       on click_metric_agg.action_datediff = session_click_no_serp.date_diff
                                           and click_metric_agg.click_no_serp_id = session_click_no_serp.id
                             left join link_dbo.u_traffic_source
                                       on click_metric_agg.current_traf_source_id = u_traffic_source.id
                    where action_datediff between ${dt_begin} and ${dt_begin}
                    group by coalesce(session_click.id_job, session_click_no_serp.id_job),
                             coalesce(session_click.uid_job, session_click_no_serp.uid_job),
                             coalesce(u_traffic_source.is_paid::int, 0),
                             u_traffic_source.channel,
                             click_metric_agg.placement,
                             click_metric_agg.action_datediff
        
),

job_data as(
    select
  		cd.id_job,
        count(distinct jc.id_cluster) as cnt_cluster
    from click_data cd
    left join an.job_cluster jc
        on jc.id_job=cd.uid
    group by cd.id_job
)



select
	${country_id} as country_id, --TODO!!!!!!!!!!!!!! Set relevant id_country
    cd.date_diff as datediff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end as cluster_cnt,
    placement,
    is_paid_source,
    channel,
    NULL as local_cluster_id,
    'total' as local_cluster_name,
    sum(revenue_usd) as revenue_usd,
    sum(cnt_paid_clicks) as paid_clicks_cnt,
    sum(cnt_away) as away_clicks_cnt,
    sum(cnt_jdp) as jdp_clicks_cnt
from click_data cd
left join job_data jd
	on cd.id_job = jd.id_job
group by
    cd.date_diff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end,
    placement,
    is_paid_source,
    channel

union all

select
	${country_id} as country_id,     --TODO!!!!! Set relevant id_country
    cd.date_diff as datediff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end as cluster_cnt,
    placement,
    is_paid_source,
    channel,
    coalesce(jc.id_cluster, 0) as local_cluster_id,
    coalesce(jc.cluster, 'unknown') as local_cluster_name,
    sum(revenue_usd) as revenue_usd,
    sum(cnt_paid_clicks) as paid_clicks_cnt,
    sum(cnt_away) as away_clicks_cnt,
    sum(cnt_jdp) as jdp_clicks_cnt
from click_data cd
left join job_data jd
	on cd.id_job = jd.id_job
left join
	(
      	select distinct id_job, id_cluster, cluster
      	from an.job_cluster
      	where id_job in (select uid from click_data)
    ) jc
	on cd.uid = jc.id_job
group by
    cd.date_diff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end,
    placement,
    is_paid_source,
    channel,
    coalesce(jc.id_cluster, 0),
    coalesce(jc.cluster, 'unknown')
