-- auto-generated definition
create table account_revenue
(
    country_id      smallint  not null,
    account_date    varchar   not null,
    revenue_date    timestamp not null,
    flags           integer   not null,
    away_cnt        integer,
    account_revenue numeric   not null,
    primary key (country_id, account_date, revenue_date, flags, account_revenue)
);

create index idx_account_revenue
    on account_revenue (country_id asc, account_date asc, revenue_date desc);

