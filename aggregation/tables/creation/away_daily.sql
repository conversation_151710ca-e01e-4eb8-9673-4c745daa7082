-- auto-generated definition
create table away_daily
(
    country_id      integer,
    country         varchar(50),
    country_code    varchar(5),
    session_date    date,
    id_project      integer,
    project_name    varchar(120),
    campaign_name   varchar(120),
    ip_cc           varchar(50),
    away_type       varchar(50),
    id_campaign     integer,
    is_mobile       bigint,
    channel         varchar(120),
    traffic_name    varchar(120),
    traffic_is_paid boolean,
    click_price     numeric(10, 3),
    value_to_usd    numeric(10, 4),
    session_away_id bigint,
    id_session_away bigint
);

alter table away_daily
    owner to yiv;

grant select on away_daily to readonly;

