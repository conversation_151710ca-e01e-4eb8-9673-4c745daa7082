-- auto-generated definition
create table budget_revenue_daily_agg
(
    country_id                    smallint not null,
    action_date                   date,
    user_id                       integer,
    country_code                  varchar(2),
    project_id                    integer,
    site_url                      varchar(255),
    is_unlim_cmp                  integer,
    session_cnt                   integer,
    cpc_usd                       double precision,
    click_cnt                     integer,
    organic_click_cnt             integer,
    paid_click_cnt                integer,
    revenue_usd                   double precision,
    organic_revenue_usd           double precision,
    paid_revenue_usd              double precision,
    potential_revenue_usd         double precision,
    job_cnt                       integer,
    paid_overflow_cnt             integer,
    user_budget_month_usd         double precision,
    campaign_budget_month_usd     double precision,
    revenue_budget_diff           double precision,
    potential_revenue_budget_diff double precision
);

alter table budget_revenue_daily_agg
    owner to postgres;

grant select on budget_revenue_daily_agg to readonly;

