create procedure insert_aggregation_paid_traffic_product_metrics(_datediff integer)
    language plpgsql
as
$$
begin

create table dwh_test.week_dates as
select info_calendar.date_diff,
       info_calendar.dt,
       date(date_trunc('week'::text, info_calendar.dt::timestamp with time zone)) as week_start
from dimension.info_calendar
where info_calendar.dt <= ((select max(info_calendar_1.dt) as max
                            from dimension.info_calendar info_calendar_1
                            where date_part('isodow'::text, info_calendar_1.dt) = 7::double precision
                              and info_calendar_1.dt <= (current_date - 1)))
order by info_calendar.dt desc
limit 140;

create table dwh_test.conv as
select lower(c.alpha_2::text) as country,
       wd.week_start          as date,
       pc.is_mobile,
       pc.is_returned,
       pc.session_create_page_type,
       case
           when pc.ip_cc::text = lower(c.alpha_2::text) or
                pc.ip_cc::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text then 1
           else 0
           end                as is_local,
       pc.traffic_name        as first_ts_name,
       tsf.is_paid            as first_ts_is_paid,
       tsf.channel            as first_ts_channel,
       ts.name                as current_ts_name,
       ts.is_paid             as current_ts_is_paid,
       ts.channel             as current_ts_channel,
       pc.project_id          as id_project,
       pc.metric,
       sum(pc.aways)          as aways,
       sum(pc.conversions)    as conversions,
       sum(pc.away_revenue)   as away_revenue
from dwh_test.week_dates wd
         join aggregation.project_conversions_daily pc on pc.session_date = wd.dt
         join (select country_id,
                      project_id,
                      metric,
                      min(session_date) as conv_start,
                      max(session_date) as conv_end
               from aggregation.project_conversions_daily
               where conversions > 0
                 and session_date >= now()::date - 147
               group by country_id, project_id, metric) cs
              on cs.project_id = pc.project_id and cs.country_id = pc.country_id and cs.metric = pc.metric and
                 pc.session_date between cs.conv_start and cs.conv_end + 30
         join dimension.countries c on pc.country_id = c.id
         left join dimension.u_traffic_source ts on ts.country = pc.country_id and ts.id = pc.id_current_traf_source
         left join dimension.u_traffic_source tsf on tsf.country = pc.country_id and tsf.name::text = pc.traffic_name::text
group by (lower(c.alpha_2::text)), wd.week_start, pc.is_mobile, pc.is_returned, pc.session_create_page_type,
         (case
              when pc.ip_cc::text = lower(c.alpha_2::text) or
                   pc.ip_cc::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text then 1
              else 0
             end), pc.traffic_name, tsf.is_paid, tsf.channel, ts.name, ts.is_paid, ts.channel, pc.project_id, pc.metric;

create table dwh_test.clicks as
select lower(c.alpha_2::text)  as country,
       wd.week_start           as date,
       jac.is_mobile,
       jac.is_returned,
       jac.session_create_page_type,
       case
           when jac.ip_cc::text = lower(c.alpha_2::text) or
                jac.ip_cc::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text then 1
           else 0
           end                 as is_local,
       jac.traffic_source_name as first_ts_name,
       tsf.is_paid             as first_ts_is_paid,
       tsf.channel             as first_ts_channel,
       tsl.name                as current_ts_name,
       tsl.is_paid             as current_ts_is_paid,
       tsl.channel             as current_ts_channel,
       jac.project_id          as id_project,
       jac.placement,
       jac.away_type,
       jac.is_paid,
       jac.is_paid_overflow,
       sum(jac.revenue_usd)    as revenue_usd,
       sum(jac.jdp_away_count) as jdp_away_count
from dwh_test.week_dates wd
         join aggregation.jdp_away_clicks_agg jac on jac.action_datediff = wd.date_diff
         join dimension.countries c on jac.country_id = c.id
         left join dimension.u_traffic_source tsl
                   on tsl.country = jac.country_id and tsl.id = jac.id_current_traf_source
         left join dimension.u_traffic_source tsf
                   on tsf.country = jac.country_id and tsf.name::text = jac.traffic_source_name::text
where jac.country_id not in (8, 22)
group by (lower(c.alpha_2::text)), wd.week_start, jac.is_mobile, jac.is_returned, jac.session_create_page_type,
         (case
              when jac.ip_cc::text = lower(c.alpha_2::text) or
                   jac.ip_cc::text = 'gb'::text and lower(c.alpha_2::text) = 'uk'::text then 1
              else 0
             end),
         jac.traffic_source_name, tsf.is_paid, tsf.channel, tsl.name, tsl.is_paid, tsl.channel, jac.project_id,
         jac.placement, jac.away_type, jac.is_paid, jac.is_paid_overflow;


create table dwh_test.ab_test as
select lower(c.alpha_2::text) as country,
       wd.week_start          as date,
       ab.is_returned,
       ab.session_create_page_type,
       ab.is_local,
       tsf.name               as first_ts_name,
       tsf.is_paid            as first_ts_is_paid,
       tsf.channel            as first_ts_channel,
       tsl.name               as current_ts_name,
       tsl.is_paid            as current_ts_is_paid,
       tsl.channel            as current_ts_channel,
       case
           when ab.attribute_value = 1 then 'letter type 8'::text
           when ab.attribute_value = 2 then 'recommendations'::text
           when ab.attribute_value = 3 then 'alertview'::text
           when ab.attribute_value = 4 then 'search'::text
           when ab.attribute_value = 5 then 'external'::text
           when ab.attribute_value = 6 then 'other'::text
           when ab.attribute_value = 7 then 'total'::text
           else null::text
           end                as placement,
       ab.metric_name,
       sum(ab.metric_value)   as metric_value
from dwh_test.week_dates wd
         join aggregation.session_abtest_agg ab on ab.action_datediff = wd.date_diff
         join dimension.countries c on ab.country_id = c.id
         left join dimension.u_traffic_source tsl
                   on tsl.country = ab.country_id and tsl.id = ab.current_traffic_source_id
         left join dimension.u_traffic_source tsf on tsf.country = ab.country_id and tsf.id = ab.traffic_source_id
where ab.metric_name::text <> all
      (array ['apply_cnt'::character varying::text, 'apply_session_cnt'::character varying::text, 'conversion_cnt'::character varying::text, 'conversion_session_cnt'::character varying::text, 'new_alert_account_cnt'::character varying::text, 'new_alert_session_cnt'::character varying::text, 'session_action_cnt'::character varying::text, 'session_action_session_cnt'::character varying::text, 'session_filter_action_cnt'::character varying::text, 'session_filter_action_session_cnt'::character varying::text, 'session_filter_search_cnt'::character varying::text, 'session_filter_search_session_cnt'::character varying::text, 'session_jdp_action_cnt'::character varying::text, 'session_jdp_action_session_cnt'::character varying::text])
  and ab.country_id not in (8, 22)
group by (lower(c.alpha_2::text)), wd.week_start, ab.is_returned, ab.session_create_page_type,
         ab.is_local, tsf.name, tsf.is_paid, tsf.channel, tsl.name, tsl.is_paid, tsl.channel, ab.attribute_value,
         ab.metric_name;

truncate table aggregation.paid_traffic_product_metrics;

insert into aggregation.paid_traffic_product_metrics(country_code, action_date, is_returned,
                                                     session_create_page_type,
                                                     is_local, first_ts_name, first_ts_is_paid, first_ts_channel,
                                                     current_ts_name,
                                                     current_ts_is_paid, current_ts_channel, project_id, placement,
                                                     away_type, is_paid,
                                                     is_paid_overflow, metric_name, metric_value, conversions,
                                                     revenue_usd)
select conv.country,
       conv.date,
       conv.is_returned,
       conv.session_create_page_type,
       conv.is_local,
       conv.first_ts_name,
       conv.first_ts_is_paid,
       conv.first_ts_channel,
       conv.current_ts_name,
       conv.current_ts_is_paid,
       conv.current_ts_channel,
       conv.id_project,
       null::character varying as placement,
       null::character varying as away_type,
       null::character varying as is_paid,
       null::smallint          as is_paid_overflow,
       conv.metric             as metric_name,
       conv.aways              as metric_value,
       conv.conversions,
       conv.away_revenue       as revenue_usd
from dwh_test.conv
union
select clicks.country,
       clicks.date,
       clicks.is_returned,
       clicks.session_create_page_type,
       clicks.is_local,
       clicks.first_ts_name,
       clicks.first_ts_is_paid,
       clicks.first_ts_channel,
       clicks.current_ts_name,
       clicks.current_ts_is_paid,
       clicks.current_ts_channel,
       clicks.id_project,
       clicks.placement,
       clicks.away_type,
       clicks.is_paid,
       clicks.is_paid_overflow,
       'click_jdp_away_cnt'::character varying as metric_name,
       clicks.jdp_away_count                   as metric_value,
       null::bigint                            as conversions,
       clicks.revenue_usd
from dwh_test.clicks
union
select ab_test.country,
       ab_test.date,
       ab_test.is_returned,
       ab_test.session_create_page_type,
       ab_test.is_local,
       ab_test.first_ts_name,
       ab_test.first_ts_is_paid,
       ab_test.first_ts_channel,
       ab_test.current_ts_name,
       ab_test.current_ts_is_paid,
       ab_test.current_ts_channel,
       null::integer           as id_project,
       ab_test.placement,
       null::character varying as away_type,
       null::character varying as is_paid,
       null::smallint          as is_paid_overflow,
       ab_test.metric_name,
       ab_test.metric_value,
       null::bigint            as conversions,
       null::numeric           as revenue_usd
from dwh_test.ab_test;

drop table dwh_test.week_dates;
drop table dwh_test.conv;
drop table dwh_test.clicks;
drop table dwh_test.ab_test;







end;

$$;

alter procedure insert_aggregation_paid_traffic_product_metrics(integer) owner to rlu;

