select p.country                                                                                        as country_id,
       fn_get_date_diff(case
                            when p.country = 1 then (date_is_submitted at time zone 'Europe/Kiev')::timestamp
                            when p.country = 10
                                then (date_is_submitted at time zone 'Europe/Budapest')::timestamp end) as submission_datediff,
       count(distinct p.id)                                                                             as profile_submitted_cnt
from imp.profiles p
where p.country in (1, 10)
  and p.is_submitted = true
  and case
          when p.country = 1
              then fn_get_date_diff(
                  (date_is_submitted at time zone 'Europe/Kiev')::timestamp) between 44641 and fn_get_date_diff(current_date) - 1
          when p.country = 10
              then fn_get_date_diff(
                  (date_is_submitted at time zone 'Europe/Budapest')::timestamp) between 44641 and fn_get_date_diff(current_date) - 1 end
group by 1, 2;
